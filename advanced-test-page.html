<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读助手高级功能测试 - 内容提取与多媒体处理验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        
        /* 模拟真实网站的复杂结构 */
        .site-header {
            background: #2c3e50;
            color: white;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-menu {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .logo { font-size: 24px; font-weight: bold; }
        .nav-links { display: flex; gap: 20px; }
        .nav-links a { color: white; text-decoration: none; }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            padding: 20px;
        }
        
        .main-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .article-header {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .article-title {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 15px 0;
            line-height: 1.3;
        }
        
        .article-meta {
            color: #666;
            font-size: 14px;
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .article-excerpt {
            font-size: 18px;
            color: #555;
            font-style: italic;
            line-height: 1.5;
        }
        
        .article-content {
            padding: 30px;
        }
        
        .article-content h2 {
            color: #2c3e50;
            font-size: 24px;
            margin: 30px 0 15px 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        
        .article-content h3 {
            color: #34495e;
            font-size: 20px;
            margin: 25px 0 12px 0;
        }
        
        .article-content p {
            margin: 0 0 18px 0;
            color: #333;
            text-align: justify;
        }
        
        .highlight-box {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .image-item {
            text-align: center;
        }
        
        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .image-caption {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
            font-style: italic;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .quote-block {
            background: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 20px;
            margin: 25px 0;
            font-style: italic;
            font-size: 18px;
            color: #555;
        }
        
        .quote-author {
            text-align: right;
            margin-top: 15px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .widget {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .widget h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        
        .widget ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .widget li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .widget a {
            color: #3498db;
            text-decoration: none;
        }
        
        .advertisement {
            background: #ffe6e6;
            border: 2px dashed #ff6b6b;
            padding: 20px;
            text-align: center;
            color: #c0392b;
            font-weight: bold;
        }
        
        .social-share {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 8px;
        }
        
        .share-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .share-facebook { background: #3b5998; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        
        .comments-section {
            background: white;
            margin-top: 30px;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .loading-placeholder {
            background: #f0f0f0;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            border-radius: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-status" id="testStatus">高级测试页面已加载</div>
    
    <!-- 网站头部 - 应该被过滤 -->
    <header class="site-header">
        <nav class="nav-menu">
            <div class="logo">TechNews Pro</div>
            <div class="nav-links">
                <a href="#">首页</a>
                <a href="#">科技</a>
                <a href="#">AI</a>
                <a href="#">评测</a>
                <a href="#">关于</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <!-- 主要内容区域 - 应该被提取 -->
        <main class="main-content">
            <header class="article-header">
                <h1 class="article-title">人工智能在内容提取中的革命性突破：从基础算法到智能识别</h1>
                <div class="article-meta">
                    <span>📅 2024年1月15日</span>
                    <span>👤 AI研究团队</span>
                    <span>🏷️ 人工智能, 内容提取, 算法优化</span>
                    <span>⏱️ 预计阅读时间：8分钟</span>
                </div>
                <p class="article-excerpt">
                    本文深入探讨了现代内容提取技术的发展历程，重点分析了如何通过先进的算法和多媒体处理技术，
                    实现对复杂网页内容的精确识别和提取，为用户提供更优质的阅读体验。
                </p>
            </header>

            <article class="article-content">
                <h2>1. 内容提取技术的演进</h2>
                
                <p>在数字化信息爆炸的时代，如何从海量的网页内容中准确提取有价值的信息，已成为现代信息处理技术的核心挑战。传统的内容提取方法往往依赖简单的DOM选择器和基础的文本分析，但面对现代网站日益复杂的结构和动态内容，这些方法显得力不从心。</p>

                <div class="highlight-box">
                    <strong>关键洞察：</strong>现代内容提取技术需要结合语义分析、机器学习和启发式算法，才能在复杂的网页环境中实现95%以上的准确率。
                </div>

                <h3>1.1 传统方法的局限性</h3>
                
                <p>早期的内容提取主要依赖预定义的CSS选择器，如<code>.content</code>、<code>#main</code>等。这种方法虽然简单直接，但存在以下问题：</p>
                
                <ul>
                    <li><strong>覆盖面有限：</strong>无法适应不同网站的多样化结构</li>
                    <li><strong>准确性不足：</strong>容易误判广告、导航等干扰内容</li>
                    <li><strong>动态内容支持差：</strong>无法处理JavaScript动态加载的内容</li>
                    <li><strong>多媒体处理弱：</strong>对图片、视频等媒体内容处理不当</li>
                </ul>

                <h3>1.2 现代算法的突破</h3>
                
                <p>为了解决这些问题，我们开发了一套基于多维度评分的智能内容提取算法：</p>

                <div class="code-block">
// 多维度内容评分算法示例
function scoreElement(element) {
    let score = 0;
    
    // 1. 文本质量评分
    score += analyzeTextQuality(element);
    
    // 2. 结构语义评分  
    score += analyzeSemanticStructure(element);
    
    // 3. 多媒体内容评分
    score += analyzeMediaContent(element);
    
    // 4. 位置权重评分
    score += analyzeElementPosition(element);
    
    return score;
}
                </div>

                <h2>2. 多媒体内容处理的创新</h2>

                <p>现代网页中，多媒体内容占据了越来越重要的地位。图片、视频、图表等视觉元素不仅丰富了内容的表现形式，也承载了重要的信息价值。因此，如何正确识别、保留和处理这些多媒体内容，成为内容提取技术的重要课题。</p>

                <div class="image-gallery">
                    <div class="image-item">
                        <img src="https://picsum.photos/400/300?random=1" alt="AI算法可视化图表" title="人工智能算法的工作流程示意图">
                        <div class="image-caption">图1：AI算法可视化 - 展示了现代内容提取算法的多层次处理流程</div>
                    </div>
                    <div class="image-item">
                        <img src="https://picsum.photos/400/300?random=2" alt="内容提取准确率对比" title="不同算法的性能对比">
                        <div class="image-caption">图2：准确率对比 - 传统方法vs智能算法在不同网站类型上的表现</div>
                    </div>
                </div>

                <h3>2.1 智能图片识别与处理</h3>
                
                <p>我们的系统能够智能识别图片的重要性，通过以下维度进行评估：</p>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>评估维度</th>
                            <th>权重</th>
                            <th>评分标准</th>
                            <th>应用场景</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>图片尺寸</td>
                            <td>25%</td>
                            <td>大图片优先，过滤小图标</td>
                            <td>主要插图、信息图表</td>
                        </tr>
                        <tr>
                            <td>Alt文本质量</td>
                            <td>20%</td>
                            <td>描述性文本长度和质量</td>
                            <td>可访问性和SEO优化</td>
                        </tr>
                        <tr>
                            <td>位置权重</td>
                            <td>30%</td>
                            <td>在主要内容区域的位置</td>
                            <td>文章配图、产品图片</td>
                        </tr>
                        <tr>
                            <td>结构语义</td>
                            <td>25%</td>
                            <td>figure/figcaption结构</td>
                            <td>专业文档、学术论文</td>
                        </tr>
                    </tbody>
                </table>

                <h3>2.2 懒加载内容的处理</h3>
                
                <p>现代网站广泛使用懒加载技术来优化性能，但这给内容提取带来了挑战。我们的解决方案包括：</p>

                <div class="loading-placeholder">
                    <div>⏳ 模拟懒加载内容区域 - 系统会自动检测并等待加载完成</div>
                </div>

                <blockquote class="quote-block">
                    "在信息时代，内容提取技术的质量直接决定了用户获取知识的效率。我们的目标是让每一个用户都能享受到纯净、高质量的阅读体验。"
                    <div class="quote-author">— 内容提取技术研发团队</div>
                </blockquote>

                <h2>3. 实际应用效果验证</h2>

                <p>为了验证我们算法的有效性，我们在多种类型的网站上进行了广泛测试：</p>

                <h3>3.1 测试网站类型</h3>
                
                <ol>
                    <li><strong>新闻网站：</strong>CNN, BBC, 新华网等主流媒体</li>
                    <li><strong>技术博客：</strong>Medium, Dev.to, 掘金等开发者平台</li>
                    <li><strong>学术网站：</strong>arXiv, IEEE, ACM等学术期刊</li>
                    <li><strong>电商平台：</strong>Amazon, 淘宝等产品详情页</li>
                    <li><strong>社交媒体：</strong>微博、知乎等内容平台</li>
                </ol>

                <h3>3.2 性能指标</h3>
                
                <p>经过大规模测试，我们的系统在各项指标上都取得了显著提升：</p>

                <ul>
                    <li>✅ <strong>内容提取准确率：</strong>从70%提升至96.5%</li>
                    <li>✅ <strong>多媒体保留率：</strong>从40%提升至92%</li>
                    <li>✅ <strong>干扰内容过滤率：</strong>从60%提升至98%</li>
                    <li>✅ <strong>动态内容支持：</strong>新增功能，支持率达85%</li>
                </ul>

                <figure style="text-align: center; margin: 30px 0;">
                    <img src="https://picsum.photos/600/400?random=3" alt="性能提升对比图表" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    <figcaption style="margin-top: 15px; font-style: italic; color: #666;">图3：算法优化前后的性能对比 - 各项指标均有显著提升</figcaption>
                </figure>

                <h2>4. 技术实现细节</h2>

                <p>我们的内容提取系统采用了模块化设计，主要包含以下核心组件：</p>

                <h3>4.1 内容识别引擎</h3>
                
                <div class="code-block">
// 核心识别算法
class ContentExtractor {
    async extract(document) {
        // 1. 等待动态内容加载
        await this.waitForDynamicContent(document);
        
        // 2. 多维度内容评分
        const candidates = this.findContentCandidates(document);
        const scored = candidates.map(el => ({
            element: el,
            score: this.scoreElement(el)
        }));
        
        // 3. 选择最佳候选
        return this.selectBestCandidate(scored);
    }
}
                </div>

                <h3>4.2 多媒体处理引擎</h3>
                
                <p>专门负责图片、视频等多媒体内容的识别和处理：</p>

                <ul>
                    <li>🖼️ <strong>图片处理：</strong>支持懒加载、响应式图片、图片说明提取</li>
                    <li>🎥 <strong>视频处理：</strong>识别嵌入视频，提供占位符和描述</li>
                    <li>📊 <strong>图表处理：</strong>保留数据可视化内容，维持信息完整性</li>
                    <li>🎨 <strong>交互元素：</strong>为图片添加点击放大、悬停效果等</li>
                </ul>

                <h2>5. 未来发展方向</h2>

                <p>虽然我们已经取得了显著的进展，但内容提取技术仍有很大的发展空间：</p>

                <h3>5.1 AI增强功能</h3>
                
                <p>我们正在开发基于深度学习的内容理解模型，将能够：</p>

                <ul>
                    <li>🧠 理解内容的语义结构和逻辑关系</li>
                    <li>📝 自动生成内容摘要和关键词</li>
                    <li>🔗 识别内容之间的关联性</li>
                    <li>🎯 个性化内容推荐和过滤</li>
                </ul>

                <h3>5.2 跨平台支持</h3>
                
                <p>未来版本将支持更多平台和内容类型：</p>

                <div class="highlight-box">
                    <strong>即将推出：</strong>
                    <ul style="margin: 10px 0 0 0;">
                        <li>📱 移动端优化版本</li>
                        <li>📄 PDF文档内容提取</li>
                        <li>📧 邮件内容智能解析</li>
                        <li>🌐 多语言内容支持</li>
                    </ul>
                </div>

                <h2>结论</h2>

                <p>通过本次技术升级，我们的内容提取系统在准确性、多媒体支持和用户体验方面都取得了质的飞跃。这不仅为用户提供了更好的阅读体验，也为内容提取技术的发展树立了新的标杆。</p>

                <p>我们相信，随着人工智能技术的不断发展，内容提取将变得更加智能和精准，为用户创造更大的价值。</p>

                <!-- 社交分享 - 应该被过滤 -->
                <div class="social-share">
                    <span>分享这篇文章：</span>
                    <button class="share-btn share-facebook">Facebook</button>
                    <button class="share-btn share-twitter">Twitter</button>
                    <button class="share-btn share-linkedin">LinkedIn</button>
                </div>
            </article>
        </main>

        <!-- 侧边栏 - 应该被过滤 -->
        <aside class="sidebar">
            <div class="widget advertisement">
                <h3>广告位</h3>
                <p>这是一个广告内容，应该被过滤掉</p>
            </div>
            
            <div class="widget">
                <h3>相关文章</h3>
                <ul>
                    <li><a href="#">AI技术在内容分析中的应用</a></li>
                    <li><a href="#">机器学习算法优化指南</a></li>
                    <li><a href="#">现代网页结构分析方法</a></li>
                </ul>
            </div>
            
            <div class="widget">
                <h3>热门标签</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <span style="background: #3498db; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">AI</span>
                    <span style="background: #e74c3c; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">算法</span>
                    <span style="background: #27ae60; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">优化</span>
                </div>
            </div>
        </aside>
    </div>

    <!-- 评论区 - 应该被过滤 -->
    <section class="comments-section">
        <h3>用户评论</h3>
        <p>这个评论区应该被过滤掉，不会出现在阅读模式中。</p>
    </section>

    <script>
        // 模拟动态内容加载
        console.log('[测试页面] 高级测试页面已加载');
        
        // 模拟懒加载
        setTimeout(() => {
            const placeholder = document.querySelector('.loading-placeholder');
            if (placeholder) {
                placeholder.innerHTML = '<div style="color: #27ae60;">✅ 动态内容加载完成 - 系统应该能检测到这个变化</div>';
            }
        }, 1000);
        
        // 更新测试状态
        setTimeout(() => {
            const status = document.getElementById('testStatus');
            status.textContent = '准备测试内容提取';
            status.style.background = '#f39c12';
        }, 2000);
        
        setTimeout(() => {
            const status = document.getElementById('testStatus');
            status.textContent = '可以启动阅读模式了';
            status.style.background = '#27ae60';
        }, 3000);
        
        // 检测阅读助手扩展
        if (window.__ReadingAssistant__) {
            console.log('[测试页面] 检测到阅读助手扩展');
        }
    </script>
</body>
</html>
