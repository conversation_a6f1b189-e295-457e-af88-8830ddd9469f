var u=Object.defineProperty;var d=(s,a,c)=>a in s?u(s,a,{enumerable:!0,configurable:!0,writable:!0,value:c}):s[a]=c;var i=(s,a,c)=>d(s,typeof a!="symbol"?a+"":a,c);(function(){"use strict";var l;const n=class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}async setStorage(e,r){try{if(typeof chrome<"u"&&chrome.storage)return chrome.storage.local.set({[e]:r});if(typeof browser<"u"&&browser.storage)return browser.storage.local.set({[e]:r});localStorage.setItem(e,JSON.stringify(r))}catch(o){throw console.error("Failed to set storage:",o),o}}async getStorage(e){try{if(typeof chrome<"u"&&chrome.storage)return(await chrome.storage.local.get(e))[e];if(typeof browser<"u"&&browser.storage)return(await browser.storage.local.get(e))[e];{const r=localStorage.getItem(e);return r?JSON.parse(r):void 0}}catch(r){console.error("Failed to get storage:",r);return}}async removeStorage(e){try{if(typeof chrome<"u"&&chrome.storage)return chrome.storage.local.remove(e);if(typeof browser<"u"&&browser.storage)return browser.storage.local.remove(e);localStorage.removeItem(e)}catch(r){throw console.error("Failed to remove storage:",r),r}}async clearStorage(){try{if(typeof chrome<"u"&&chrome.storage)return chrome.storage.local.clear();if(typeof browser<"u"&&browser.storage)return browser.storage.local.clear();localStorage.clear()}catch(e){throw console.error("Failed to clear storage:",e),e}}async sendMessage(e){try{if(typeof chrome<"u"&&chrome.runtime)return chrome.runtime.sendMessage(e);if(typeof browser<"u"&&browser.runtime)return browser.runtime.sendMessage(e);throw new Error("Browser API not available")}catch(r){throw console.error("Failed to send message:",r),r}}async getCurrentTab(){try{if(typeof chrome<"u"&&chrome.tabs)return(await chrome.tabs.query({active:!0,currentWindow:!0}))[0];if(typeof browser<"u"&&browser.tabs)return(await browser.tabs.query({active:!0,currentWindow:!0}))[0];throw new Error("Tabs API not available")}catch(e){throw console.error("Failed to get current tab:",e),e}}async sendTabMessage(e,r){try{if(typeof chrome<"u"&&chrome.tabs)return chrome.tabs.sendMessage(e,r);if(typeof browser<"u"&&browser.tabs)return browser.tabs.sendMessage(e,r);throw new Error("Tabs API not available")}catch(o){throw console.error("Failed to send tab message:",o),o}}isSpecialPage(e){return["chrome://","chrome-extension://","moz-extension://","edge://","about:","data:","file://"].some(o=>e.startsWith(o))}};i(n,"instance");let s=n;chrome.runtime.onInstalled.addListener(()=>{console.log("[阅读助手] 扩展已安装 - React+TypeScript版本")}),(l=chrome.action.onClicked)==null||l.addListener(async t=>{if(t.id)try{await chrome.tabs.sendMessage(t.id,{type:"TOGGLE_READER"})}catch(e){console.warn("[阅读助手] 无法发送消息到标签页:",e)}}),chrome.runtime.onMessage.addListener((t,e,r)=>{switch(console.log("[阅读助手] 收到消息:",t),t.type){case"GET_ACTIVE_TAB":return chrome.tabs.query({active:!0,currentWindow:!0},o=>{r(o==null?void 0:o[0])}),!0;case"UPDATE_SETTINGS":return a(t.payload).then(()=>r({success:!0})).catch(o=>r({success:!1,error:o.message})),!0;case"GET_SETTINGS":return c().then(o=>r({success:!0,settings:o})).catch(o=>r({success:!1,error:o.message})),!0;default:console.warn("[阅读助手] 未知消息类型:",t.type),r({success:!1,error:"Unknown message type"})}});async function a(t){try{await s.getInstance().setStorage("userSettings",t),console.log("[阅读助手] 设置已更新:",t)}catch(e){throw console.error("[阅读助手] 设置更新失败:",e),e}}async function c(){try{const e=await s.getInstance().getStorage("userSettings");return console.log("[阅读助手] 获取设置:",e),e}catch(t){throw console.error("[阅读助手] 获取设置失败:",t),t}}chrome.tabs.onUpdated.addListener((t,e,r)=>{if(e.status==="complete"&&r.url){if(s.getInstance().isSpecialPage(r.url)){console.log("[阅读助手] 跳过特殊页面:",r.url);return}console.log("[阅读助手] 页面加载完成:",r.url)}}),self.addEventListener("error",t=>{console.error("[阅读助手] 后台脚本错误:",t.error)}),self.addEventListener("unhandledrejection",t=>{console.error("[阅读助手] 未处理的Promise拒绝:",t.reason)}),console.log("[阅读助手] 后台脚本已加载")})();
