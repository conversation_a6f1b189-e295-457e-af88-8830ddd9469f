var xt=Object.defineProperty;var Et=(I,C,$)=>C in I?xt(I,C,{enumerable:!0,configurable:!0,writable:!0,value:$}):I[C]=$;var w=(I,C,$)=>Et(I,typeof C!="symbol"?C+"":C,$);(function(){"use strict";const I={light:{bg:"#ffffff",secondary:"#f9fafb",text:"#1f2937",border:"#e5e7eb"},dark:{bg:"#1f2937",secondary:"#374151",text:"#f9fafb",border:"#4b5563"},sepia:{bg:"#f7f3e9",secondary:"#f0e6d2",text:"#5d4e37",border:"#d4c4a8"},nature:{bg:"#f0f8f0",secondary:"#e8f5e8",text:"#2d5016",border:"#c8e6c9"},warm:{bg:"#fdf2f8",secondary:"#fce7f3",text:"#7c2d12",border:"#fce7f3"},eyecare:{bg:"#f5f5f5",secondary:"#eeeeee",text:"#424242",border:"#d0d0d0"}},C=["article","main",'[role="main"]','[role="article"]','[role="document"]',".story-body",".article-body",".story-content",".article-content",".news-content",".story-text",".article-text",".content-body",".post-content",".entry-content",".postArticle-content",".section-content",".markup",".post-body",".entry-body",".blog-content",".blog-post",".post-text",".entry-text",".documentation",".doc-content",".wiki-content",".paper-content",".abstract",".full-text",".research-content",".product-description",".product-details",".product-content",".item-description",".product-info",".main-content",".page-content",".content-area",".primary-content",".content-wrapper",".content-container",".content","#content",".text",".article",".post",".entry",".story",".wp-content",".drupal-content",".joomla-content",".ghost-content",".medium-content","[data-content]","[data-article]","[data-post]",'[class*="content"]','[class*="article"]','[class*="post"]','[class*="story"]','[class*="text"]','[id*="content"]','[id*="article"]','[id*="post"]'],$=["script","style","noscript",'link[rel="stylesheet"]',"iframe","object","embed","video","audio",".advertisement",".ad",".ads",".advert",".ad-container",".ad-banner",".ad-block",".google-ad",".adsense",'[class*="ad-"]','[id*="ad-"]','[class*="ads"]','[id*="ads"]',"nav",".navigation",".nav",".navbar",".menu",".breadcrumb",".breadcrumbs",".pagination",".pager","header","footer",".header",".footer",".site-header",".site-footer",".page-header",".page-footer","aside",".sidebar",".side-bar",".widget",".widgets",".social",".social-share",".share",".sharing",".social-media",".social-buttons",".comments",".comment",".comment-section",".disqus",".facebook-comments",".related",".related-posts",".related-articles",".recommended",".suggestions",".more-stories","form","input","textarea","select",'button[type="submit"]',".search",".search-form",".newsletter",".subscribe",".popup",".modal",".overlay",".banner",".notice",".alert",".notification",".cookie-notice",".gdpr",".promo",".promotion",".sponsor",".sponsored",".affiliate",'[style*="display: none"]','[style*="visibility: hidden"]',".hidden",".invisible",".sr-only",".twitter-tweet",".instagram-media",".facebook-post",".youtube-player",".nav-menu",".main-nav",".site-nav",".top-nav",".bottom-nav",".nav-links",".menu-item",".menu-list",".ad-wrapper",".ad-content",".ad-space",".advertisement-wrapper",".sponsored-content",".promo-box",".promotion-banner",".affiliate-link",".affiliate-disclosure",".social-links",".social-icons",".share-buttons",".sharing-tools",".follow-buttons",".social-follow",".like-button",".tweet-button",".comment-form",".comment-list",".comment-thread",".discussion",".feedback",".rating",".vote",".poll",".related-articles",".recommended-posts",".you-might-like",".more-from-author",".trending",".popular",".latest",".recent-posts",".newsletter-signup",".email-signup",".subscription",".notification",".alert-bar",".announcement",".cookie-banner",".gdpr-notice",".price",".buy-button",".add-to-cart",".checkout",".shipping",".reviews",".rating-stars",".product-options",".debug",".dev-tools",".admin-bar",".edit-link",".version-info"],pe={EXTRACTION_TIMEOUT:5e3,RENDER_TIMEOUT:2e3,MAX_CONTENT_LENGTH:1e6,CACHE_DURATION:7*24*60*60*1e3};class ge{constructor(){w(this,"MIN_CONTENT_LENGTH",100)}async extract(e){try{console.log("[内容提取器] 开始提取内容");const t=new Promise((o,i)=>{setTimeout(()=>i(new Error("内容提取超时")),pe.EXTRACTION_TIMEOUT)}),n=this.performExtraction(e),s=await Promise.race([n,t]);return s&&console.log("[内容提取器] 内容提取成功:",s.title),s}catch(t){return console.error("[内容提取器] 提取失败:",t),null}}canHandle(e){var o;if(!e||e.contentType!=="text/html"||(((o=e.body)==null?void 0:o.textContent)||"").length<this.MIN_CONTENT_LENGTH)return!1;const n=e.location.href;return![/^chrome:/,/^about:/,/^moz-extension:/,/^chrome-extension:/,/^data:/,/^file:/].some(i=>i.test(n))}async performExtraction(e){await this.waitForDynamicContent(e);const t=this.cleanDocument(e),n=this.findMainContent(t);if(!n)return console.warn("[内容提取器] 未找到主要内容容器"),null;const s=this.extractTitle(e),o=this.extractContentFromContainer(n);if(!o||o.length<this.MIN_CONTENT_LENGTH)return console.warn("[内容提取器] 提取的内容太短"),null;const i=this.extractMetadata(e,o),a={id:this.generateId(),url:e.location.href,urlHash:this.hashUrl(e.location.href),title:s,content:o,originalContent:n.innerHTML,metadata:i,extractedAt:new Date,confidence:this.calculateConfidence(n,o),extractionMethod:"enhanced"};return console.log("[内容提取器] 内容提取完成，置信度:",a.confidence),a}async waitForDynamicContent(e){return new Promise(t=>{let n=0;const s=10,o=200,i=()=>{n++;const a=e.querySelectorAll(".loading, .spinner, .skeleton, [data-loading], .lazy-loading"),c=e.querySelectorAll('img[data-src], img[data-lazy-src], img[loading="lazy"]');a.length===0&&c.length===0||n>=s?t():setTimeout(i,o)};setTimeout(i,100)})}cleanDocument(e){const t=e.cloneNode(!0);return $.forEach(n=>{t.querySelectorAll(n).forEach(o=>o.remove())}),t}findMainContent(e){for(const s of C){const o=e.querySelector(s);if(o&&this.isValidContentContainer(o))return console.log("[内容提取器] 使用选择器找到内容:",s),o}const n=this.findContentCandidates(e).map(s=>({element:s,score:this.scoreElement(s)}));return n.sort((s,o)=>o.score-s.score),n.length>0&&n[0].score>0?(console.log("[内容提取器] 使用启发式算法找到内容，得分:",n[0].score),n[0].element):(console.warn("[内容提取器] 使用后备方案：body元素"),e.body)}findContentCandidates(e){const t=[];return["div","article","section","main","p"].forEach(s=>{e.querySelectorAll(s).forEach(i=>{const a=i;this.isValidContentContainer(a)&&t.push(a)})}),t}isValidContentContainer(e){return!((e.textContent||"").length<this.MIN_CONTENT_LENGTH||e.querySelectorAll("p").length<2)}scoreElement(e){let t=0;const n=e.textContent||"",s=n.length;if(s<this.MIN_CONTENT_LENGTH)return 0;const o=Math.log(s/100+1)*12;t+=o;const i=e.querySelectorAll("p"),a=Array.from(i).filter(T=>{var _;const A=((_=T.textContent)==null?void 0:_.trim())||"";return A.length>30&&A.split(" ").length>5});t+=a.length*4;const c=n.split(/[.!?]+/).filter(T=>T.trim().length>15),l=c.length>0?s/c.length:0;l>=50&&l<=150?t+=10:l>=30&&l<=200&&(t+=5),t+=Math.min(c.length*.8,25);const u=e.querySelectorAll("a"),y=Array.from(u).reduce((T,A)=>{var _;return T+(((_=A.textContent)==null?void 0:_.length)||0)},0),d=s>0?y/s:0;d>.4?t-=d*40:d>.25?t-=d*25:d>.15?t-=d*15:d>.05&&(t+=2);const m=e.tagName.toLowerCase();t+={article:30,main:25,section:15,div:0,span:-3,aside:-10,nav:-15,header:-10,footer:-10}[m]||0;const f=e.className.toLowerCase(),g=e.id.toLowerCase(),S=`${f} ${g}`,p=[{terms:["content","article","post","story","text","body"],weight:18},{terms:["main","primary","principal"],weight:15},{terms:["entry","blog","news"],weight:12},{terms:["description","detail","info"],weight:10},{terms:["full","complete","entire"],weight:8}],E=[{terms:["sidebar","widget","ad","advertisement"],weight:-25},{terms:["nav","menu","navigation"],weight:-20},{terms:["comment","social","share","follow"],weight:-18},{terms:["related","recommended","popular"],weight:-15},{terms:["footer","header","banner"],weight:-12},{terms:["promo","sponsor","affiliate"],weight:-20}];for(const{terms:T,weight:A}of p)for(const _ of T)if(S.includes(_)){t+=A;break}for(const{terms:T,weight:A}of E)for(const _ of T)if(S.includes(_)){t+=A;break}const G=e.querySelectorAll("h1, h2, h3, h4, h5, h6"),B=Math.min(G.length*3,15);t+=B;const mt=this.scoreMediaContent(e);t+=mt;const yt=this.scoreContentStructure(e);t+=yt;const vt=this.scoreNegativeIndicators(e);t+=vt;const St=this.scoreElementPosition(e);t+=St;const bt=this.scoreContentDensity(e);return t+=bt,Math.max(0,Math.round(t))}scoreMediaContent(e){let t=0;const n=e.querySelectorAll("img"),s=Array.from(n).filter(a=>{const c=a.getAttribute("src")||a.getAttribute("data-src")||"",l=a.getAttribute("alt")||"",u=a.getAttribute("width")||a.naturalWidth||0,y=a.getAttribute("height")||a.naturalHeight||0,d=c.includes("ad")||c.includes("banner")||l.toLowerCase().includes("ad")||l.toLowerCase().includes("banner"),m=u&&y&&Math.max(+u,+y)<50;return c.length>0&&!d&&!m});t+=Math.min(s.length*2,12);const o=e.querySelectorAll('video, iframe[src*="youtube"], iframe[src*="vimeo"]');t+=Math.min(o.length*3,9);const i=e.querySelectorAll("pre, code, .highlight, .code-block");return t+=Math.min(i.length*2,8),t}scoreContentStructure(e){let t=0;const n=e.querySelectorAll("ul, ol"),s=Array.from(n).filter(l=>{const u=l.querySelectorAll("li");return u.length>=2&&Array.from(u).some(y=>{var d;return(((d=y.textContent)==null?void 0:d.length)||0)>10})});t+=Math.min(s.length*2,8);const o=e.querySelectorAll("blockquote, .quote");t+=Math.min(o.length*3,9);const i=e.querySelectorAll("table"),a=Array.from(i).filter(l=>l.querySelectorAll("tr").length>=2);t+=Math.min(a.length*4,12);const c=e.querySelectorAll("section, .section");return t+=Math.min(c.length*1,5),t}scoreNegativeIndicators(e){let t=0;const n=[".ad",".advertisement",".promo",".sponsor",".comment",".social",".share",".related",".sidebar",".widget",".nav",".menu",".footer",".header",".banner"];for(const i of n){const a=e.querySelectorAll(i);a.length>0&&(t-=a.length*5)}const s=e.querySelectorAll("form, input, textarea, select");t-=s.length*3;const o=e.querySelectorAll("a");return o.length>20&&(t-=(o.length-20)*.5),Math.max(t,-50)}scoreElementPosition(e){let t=0;try{const n=e.getBoundingClientRect(),s=window.innerHeight,o=window.innerWidth;n.top<s*.2?t+=8:n.top<s*.5?t+=5:n.top<s&&(t+=2);const i=n.width/o;i>.6?t+=5:i>.4?t+=3:i<.2&&(t-=5),(n.height===0||n.width===0)&&(t-=20)}catch(n){console.warn("[内容提取器] 获取元素位置失败:",n)}return t}scoreContentDensity(e){const n=(e.textContent||"").length,s=e.children.length;if(s===0)return n>100?5:0;const o=n/s;return o>200?10:o>100?5:o<20?-5:0}extractTitle(e){var s,o;const t=[{selector:"article h1",priority:100},{selector:"main h1",priority:95},{selector:'[role="main"] h1',priority:90},{selector:".post-title",priority:85},{selector:".article-title",priority:85},{selector:".entry-title",priority:85},{selector:".story-title",priority:85},{selector:".content-title",priority:80},{selector:".page-title",priority:75},{selector:"h1",priority:70},{selector:".title",priority:65},{selector:"#title",priority:65},{selector:'[class*="title"]',priority:60},{selector:'[id*="title"]',priority:60},{selector:"h2",priority:40},{selector:".headline",priority:35},{selector:".header h1",priority:30},{selector:".header h2",priority:25}],n=[];for(const{selector:i,priority:a}of t){const c=e.querySelectorAll(i);for(const l of c){const u=(s=l.textContent)==null?void 0:s.trim();u&&u.length>5&&u.length<200&&n.push({text:u,priority:a,element:l})}}if(n.length===0){const i=(o=e.title)==null?void 0:o.trim();return i&&i!=="Untitled"?i.split(" - ")[0].split(" | ")[0].split(" :: ")[0].split(" — ")[0].trim()||i:"未知标题"}return n.sort((i,a)=>{if(i.priority!==a.priority)return a.priority-i.priority;const c=this.scoreTitleCandidate(i.text,i.element);return this.scoreTitleCandidate(a.text,a.element)-c}),n[0].text}scoreTitleCandidate(e,t){let n=0;const s=e.length;s>=10&&s<=100?n+=10:s>=5&&s<=150?n+=5:n-=5,t.getBoundingClientRect().top<window.innerHeight*.5&&(n+=5);const i=[/^(menu|nav|search|login|register|subscribe)/i,/^(home|back|next|previous|more)/i,/^(advertisement|sponsored|promoted)/i];for(const a of i)a.test(e)&&(n-=20);return n}extractContentFromContainer(e){const t=e.cloneNode(!0);return $.forEach(n=>{t.querySelectorAll(n).forEach(o=>o.remove())}),this.extractFormattedText(t)}extractFormattedText(e){var o;const t=document.createTreeWalker(e,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null);let n="",s;for(;s=t.nextNode();)if(s.nodeType===Node.TEXT_NODE){const i=(o=s.textContent)==null?void 0:o.trim();i&&(n+=i+" ")}else if(s.nodeType===Node.ELEMENT_NODE){const a=s.tagName.toLowerCase();["p","div","br","h1","h2","h3","h4","h5","h6"].includes(a)&&(n+=`
`)}return n.replace(/\s+/g," ").trim()}extractMetadata(e,t){const n=t.split(/\s+/).length,s=Math.ceil(n/200);return{wordCount:n,readingTime:s,language:this.detectLanguage(t),contentType:"article",tags:[],images:this.extractImages(e)}}detectLanguage(e){const t=e.match(/[\u4e00-\u9fff]/g),n=e.length;return t&&t.length/n>.3?"zh-CN":"en"}extractImages(e){const t=e.querySelectorAll("img"),n=[];for(const s of Array.from(t)){const o=this.processImage(s);o&&this.isValidContentImage(o)&&n.push(o)}return n.sort((s,o)=>o.importance-s.importance).slice(0,15)}processImage(e){var t;try{const n=e.src||e.getAttribute("data-src")||e.getAttribute("data-lazy-src")||e.getAttribute("data-original")||((t=e.getAttribute("srcset"))==null?void 0:t.split(" ")[0])||"";if(!n)return null;const s=e.alt||e.getAttribute("title")||"",o=e.naturalWidth||parseInt(e.getAttribute("width")||"0")||0,i=e.naturalHeight||parseInt(e.getAttribute("height")||"0")||0,a=this.findImageCaption(e),c=this.calculateImageImportance(e,n,s,o,i);return{src:this.normalizeImageUrl(n),alt:s,caption:a,width:o,height:i,importance:c,position:this.getImagePosition(e),isInContent:this.isImageInMainContent(e)}}catch(n){return console.warn("[内容提取器] 处理图片失败:",n),null}}findImageCaption(e){var i,a;const t=e.parentElement;if(!t)return"";const n=e.closest("figure");if(n){const c=n.querySelector("figcaption");if(c!=null&&c.textContent)return c.textContent.trim()}const s=[".caption",".image-caption",".photo-caption",".img-caption",".description",".subtitle"];for(const c of s){const l=t.querySelector(c)||((i=t.parentElement)==null?void 0:i.querySelector(c));if(l!=null&&l.textContent)return l.textContent.trim()}const o=e.nextElementSibling;if(o&&o.tagName.toLowerCase()==="p"){const c=((a=o.textContent)==null?void 0:a.trim())||"";if(c.length>0&&c.length<200)return c}return""}calculateImageImportance(e,t,n,s,o){var l;let i=0;const a=s*o;a>1e5?i+=10:a>5e4?i+=7:a>1e4?i+=4:a<2500&&(i-=5),n.length>10?i+=5:n.length>0&&(i+=2);const c=((l=t.split("/").pop())==null?void 0:l.toLowerCase())||"";return(c.includes("hero")||c.includes("main")||c.includes("featured"))&&(i+=8),(c.includes("thumb")||c.includes("icon")||c.includes("logo"))&&(i-=3),this.isImageInMainContent(e)&&(i+=10),e.closest("figure")&&(i+=5),e.closest("article, main, .content")&&(i+=7),Math.max(0,i)}isImageInMainContent(e){const t=["article","main",".content",".post-content",".entry-content",".article-content",".story-body"];for(const n of t)if(e.closest(n))return!0;return!1}getImagePosition(e){try{const t=e.getBoundingClientRect();return{top:t.top,left:t.left,width:t.width,height:t.height}}catch{return{top:0,left:0,width:0,height:0}}}normalizeImageUrl(e){try{return e.startsWith("//")?`https:${e}`:e.startsWith("/")?`${window.location.origin}${e}`:e.startsWith("http")?e:`${window.location.origin}/${e}`}catch{return e}}isValidContentImage(e){const{src:t,width:n,height:s,importance:o}=e;if(!t||o<0||n>0&&s>0&&Math.max(n,s)<50)return!1;const i=t.toLowerCase(),a=["ad","banner","sponsor","promo","tracking","pixel","beacon","logo","icon","avatar"];for(const c of a)if(i.includes(c))return!1;return!0}calculateConfidence(e,t){let n=.5;t.length>1e3&&(n+=.2),t.length>2e3&&(n+=.1);const s=e.querySelectorAll("p");return s.length>3&&(n+=.1),s.length>5&&(n+=.1),Math.min(1,n)}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}hashUrl(e){let t=0;for(let n=0;n<e.length;n++){const s=e.charCodeAt(n);t=(t<<5)-t+s,t=t&t}return t.toString(36)}}const me={},K=r=>{let e;const t=new Set,n=(u,y)=>{const d=typeof u=="function"?u(e):u;if(!Object.is(d,e)){const m=e;e=y??(typeof d!="object"||d===null)?d:Object.assign({},e,d),t.forEach(v=>v(e,m))}},s=()=>e,c={setState:n,getState:s,getInitialState:()=>l,subscribe:u=>(t.add(u),()=>t.delete(u)),destroy:()=>{(me?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),t.clear()}},l=e=r(n,s,c);return c},ye=r=>r?K(r):K;function V(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var X={exports:{}},h={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var L=Symbol.for("react.element"),ve=Symbol.for("react.portal"),Se=Symbol.for("react.fragment"),be=Symbol.for("react.strict_mode"),xe=Symbol.for("react.profiler"),Ee=Symbol.for("react.provider"),we=Symbol.for("react.context"),Ce=Symbol.for("react.forward_ref"),Te=Symbol.for("react.suspense"),_e=Symbol.for("react.memo"),Ae=Symbol.for("react.lazy"),J=Symbol.iterator;function Ie(r){return r===null||typeof r!="object"?null:(r=J&&r[J]||r["@@iterator"],typeof r=="function"?r:null)}var Q={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Y=Object.assign,Z={};function M(r,e,t){this.props=r,this.context=e,this.refs=Z,this.updater=t||Q}M.prototype.isReactComponent={},M.prototype.setState=function(r,e){if(typeof r!="object"&&typeof r!="function"&&r!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,r,e,"setState")},M.prototype.forceUpdate=function(r){this.updater.enqueueForceUpdate(this,r,"forceUpdate")};function ee(){}ee.prototype=M.prototype;function O(r,e,t){this.props=r,this.context=e,this.refs=Z,this.updater=t||Q}var P=O.prototype=new ee;P.constructor=O,Y(P,M.prototype),P.isPureReactComponent=!0;var te=Array.isArray,re=Object.prototype.hasOwnProperty,D={current:null},ne={key:!0,ref:!0,__self:!0,__source:!0};function oe(r,e,t){var n,s={},o=null,i=null;if(e!=null)for(n in e.ref!==void 0&&(i=e.ref),e.key!==void 0&&(o=""+e.key),e)re.call(e,n)&&!ne.hasOwnProperty(n)&&(s[n]=e[n]);var a=arguments.length-2;if(a===1)s.children=t;else if(1<a){for(var c=Array(a),l=0;l<a;l++)c[l]=arguments[l+2];s.children=c}if(r&&r.defaultProps)for(n in a=r.defaultProps,a)s[n]===void 0&&(s[n]=a[n]);return{$$typeof:L,type:r,key:o,ref:i,props:s,_owner:D.current}}function $e(r,e){return{$$typeof:L,type:r.type,key:e,ref:r.ref,props:r.props,_owner:r._owner}}function j(r){return typeof r=="object"&&r!==null&&r.$$typeof===L}function Me(r){var e={"=":"=0",":":"=2"};return"$"+r.replace(/[=:]/g,function(t){return e[t]})}var se=/\/+/g;function F(r,e){return typeof r=="object"&&r!==null&&r.key!=null?Me(""+r.key):e.toString(36)}function N(r,e,t,n,s){var o=typeof r;(o==="undefined"||o==="boolean")&&(r=null);var i=!1;if(r===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(r.$$typeof){case L:case ve:i=!0}}if(i)return i=r,s=s(i),r=n===""?"."+F(i,0):n,te(s)?(t="",r!=null&&(t=r.replace(se,"$&/")+"/"),N(s,e,t,"",function(l){return l})):s!=null&&(j(s)&&(s=$e(s,t+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(se,"$&/")+"/")+r)),e.push(s)),1;if(i=0,n=n===""?".":n+":",te(r))for(var a=0;a<r.length;a++){o=r[a];var c=n+F(o,a);i+=N(o,e,t,c,s)}else if(c=Ie(r),typeof c=="function")for(r=c.call(r),a=0;!(o=r.next()).done;)o=o.value,c=n+F(o,a++),i+=N(o,e,t,c,s);else if(o==="object")throw e=String(r),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(r).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return i}function R(r,e,t){if(r==null)return r;var n=[],s=0;return N(r,n,"","",function(o){return e.call(t,o,s++)}),n}function ke(r){if(r._status===-1){var e=r._result;e=e(),e.then(function(t){(r._status===0||r._status===-1)&&(r._status=1,r._result=t)},function(t){(r._status===0||r._status===-1)&&(r._status=2,r._result=t)}),r._status===-1&&(r._status=0,r._result=e)}if(r._status===1)return r._result.default;throw r._result}var x={current:null},q={transition:null},Le={ReactCurrentDispatcher:x,ReactCurrentBatchConfig:q,ReactCurrentOwner:D};function ie(){throw Error("act(...) is not supported in production builds of React.")}h.Children={map:R,forEach:function(r,e,t){R(r,function(){e.apply(this,arguments)},t)},count:function(r){var e=0;return R(r,function(){e++}),e},toArray:function(r){return R(r,function(e){return e})||[]},only:function(r){if(!j(r))throw Error("React.Children.only expected to receive a single React element child.");return r}},h.Component=M,h.Fragment=Se,h.Profiler=xe,h.PureComponent=O,h.StrictMode=be,h.Suspense=Te,h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Le,h.act=ie,h.cloneElement=function(r,e,t){if(r==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+r+".");var n=Y({},r.props),s=r.key,o=r.ref,i=r._owner;if(e!=null){if(e.ref!==void 0&&(o=e.ref,i=D.current),e.key!==void 0&&(s=""+e.key),r.type&&r.type.defaultProps)var a=r.type.defaultProps;for(c in e)re.call(e,c)&&!ne.hasOwnProperty(c)&&(n[c]=e[c]===void 0&&a!==void 0?a[c]:e[c])}var c=arguments.length-2;if(c===1)n.children=t;else if(1<c){a=Array(c);for(var l=0;l<c;l++)a[l]=arguments[l+2];n.children=a}return{$$typeof:L,type:r.type,key:s,ref:o,props:n,_owner:i}},h.createContext=function(r){return r={$$typeof:we,_currentValue:r,_currentValue2:r,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},r.Provider={$$typeof:Ee,_context:r},r.Consumer=r},h.createElement=oe,h.createFactory=function(r){var e=oe.bind(null,r);return e.type=r,e},h.createRef=function(){return{current:null}},h.forwardRef=function(r){return{$$typeof:Ce,render:r}},h.isValidElement=j,h.lazy=function(r){return{$$typeof:Ae,_payload:{_status:-1,_result:r},_init:ke}},h.memo=function(r,e){return{$$typeof:_e,type:r,compare:e===void 0?null:e}},h.startTransition=function(r){var e=q.transition;q.transition={};try{r()}finally{q.transition=e}},h.unstable_act=ie,h.useCallback=function(r,e){return x.current.useCallback(r,e)},h.useContext=function(r){return x.current.useContext(r)},h.useDebugValue=function(){},h.useDeferredValue=function(r){return x.current.useDeferredValue(r)},h.useEffect=function(r,e){return x.current.useEffect(r,e)},h.useId=function(){return x.current.useId()},h.useImperativeHandle=function(r,e,t){return x.current.useImperativeHandle(r,e,t)},h.useInsertionEffect=function(r,e){return x.current.useInsertionEffect(r,e)},h.useLayoutEffect=function(r,e){return x.current.useLayoutEffect(r,e)},h.useMemo=function(r,e){return x.current.useMemo(r,e)},h.useReducer=function(r,e,t){return x.current.useReducer(r,e,t)},h.useRef=function(r){return x.current.useRef(r)},h.useState=function(r){return x.current.useState(r)},h.useSyncExternalStore=function(r,e,t){return x.current.useSyncExternalStore(r,e,t)},h.useTransition=function(){return x.current.useTransition()},h.version="18.3.1",X.exports=h;var U=X.exports;const ze=V(U);var ae={exports:{}},ce={},le={exports:{}},de={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k=U;function Ne(r,e){return r===e&&(r!==0||1/r===1/e)||r!==r&&e!==e}var Re=typeof Object.is=="function"?Object.is:Ne,qe=k.useState,He=k.useEffect,Oe=k.useLayoutEffect,Pe=k.useDebugValue;function De(r,e){var t=e(),n=qe({inst:{value:t,getSnapshot:e}}),s=n[0].inst,o=n[1];return Oe(function(){s.value=t,s.getSnapshot=e,W(s)&&o({inst:s})},[r,t,e]),He(function(){return W(s)&&o({inst:s}),r(function(){W(s)&&o({inst:s})})},[r]),Pe(t),t}function W(r){var e=r.getSnapshot;r=r.value;try{var t=e();return!Re(r,t)}catch{return!0}}function je(r,e){return e()}var Fe=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?je:De;de.useSyncExternalStore=k.useSyncExternalStore!==void 0?k.useSyncExternalStore:Fe,le.exports=de;var Ue=le.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var H=U,We=Ue;function Ge(r,e){return r===e&&(r!==0||1/r===1/e)||r!==r&&e!==e}var Be=typeof Object.is=="function"?Object.is:Ge,Ke=We.useSyncExternalStore,Ve=H.useRef,Xe=H.useEffect,Je=H.useMemo,Qe=H.useDebugValue;ce.useSyncExternalStoreWithSelector=function(r,e,t,n,s){var o=Ve(null);if(o.current===null){var i={hasValue:!1,value:null};o.current=i}else i=o.current;o=Je(function(){function c(m){if(!l){if(l=!0,u=m,m=n(m),s!==void 0&&i.hasValue){var v=i.value;if(s(v,m))return y=v}return y=m}if(v=y,Be(u,m))return v;var f=n(m);return s!==void 0&&s(v,f)?(u=m,v):(u=m,y=f)}var l=!1,u,y,d=t===void 0?null:t;return[function(){return c(e())},d===null?void 0:function(){return c(d())}]},[e,t,n,s]);var a=Ke(r,o[0],o[1]);return Xe(function(){i.hasValue=!0,i.value=a},[a]),Qe(a),a},ae.exports=ce;var Ye=ae.exports;const Ze=V(Ye),ue={},{useDebugValue:et}=ze,{useSyncExternalStoreWithSelector:tt}=Ze;let fe=!1;const rt=r=>r;function nt(r,e=rt,t){(ue?"production":void 0)!=="production"&&t&&!fe&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),fe=!0);const n=tt(r.subscribe,r.getState,r.getServerState||r.getInitialState,e,t);return et(n),n}const ot=r=>{(ue?"production":void 0)!=="production"&&typeof r!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const e=typeof r=="function"?ye(r):r,t=(n,s)=>nt(e,n,s);return Object.assign(t,e),t},st=r=>ot,it={};function at(r,e){let t;try{t=r()}catch{return}return{getItem:s=>{var o;const i=c=>c===null?null:JSON.parse(c,void 0),a=(o=t.getItem(s))!=null?o:null;return a instanceof Promise?a.then(i):i(a)},setItem:(s,o)=>t.setItem(s,JSON.stringify(o,void 0)),removeItem:s=>t.removeItem(s)}}const z=r=>e=>{try{const t=r(e);return t instanceof Promise?t:{then(n){return z(n)(t)},catch(n){return this}}}catch(t){return{then(n){return this},catch(n){return z(n)(t)}}}},ct=(r,e)=>(t,n,s)=>{let o={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:g=>g,version:0,merge:(g,S)=>({...S,...g}),...e},i=!1;const a=new Set,c=new Set;let l;try{l=o.getStorage()}catch{}if(!l)return r((...g)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),t(...g)},n,s);const u=z(o.serialize),y=()=>{const g=o.partialize({...n()});let S;const p=u({state:g,version:o.version}).then(E=>l.setItem(o.name,E)).catch(E=>{S=E});if(S)throw S;return p},d=s.setState;s.setState=(g,S)=>{d(g,S),y()};const m=r((...g)=>{t(...g),y()},n,s);let v;const f=()=>{var g;if(!l)return;i=!1,a.forEach(p=>p(n()));const S=((g=o.onRehydrateStorage)==null?void 0:g.call(o,n()))||void 0;return z(l.getItem.bind(l))(o.name).then(p=>{if(p)return o.deserialize(p)}).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==o.version){if(o.migrate)return o.migrate(p.state,p.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return p.state}).then(p=>{var E;return v=o.merge(p,(E=n())!=null?E:m),t(v,!0),y()}).then(()=>{S==null||S(v,void 0),i=!0,c.forEach(p=>p(v))}).catch(p=>{S==null||S(void 0,p)})};return s.persist={setOptions:g=>{o={...o,...g},g.getStorage&&(l=g.getStorage())},clearStorage:()=>{l==null||l.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>f(),hasHydrated:()=>i,onHydrate:g=>(a.add(g),()=>{a.delete(g)}),onFinishHydration:g=>(c.add(g),()=>{c.delete(g)})},f(),v||m},lt=(r,e)=>(t,n,s)=>{let o={storage:at(()=>localStorage),partialize:f=>f,version:0,merge:(f,g)=>({...g,...f}),...e},i=!1;const a=new Set,c=new Set;let l=o.storage;if(!l)return r((...f)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),t(...f)},n,s);const u=()=>{const f=o.partialize({...n()});return l.setItem(o.name,{state:f,version:o.version})},y=s.setState;s.setState=(f,g)=>{y(f,g),u()};const d=r((...f)=>{t(...f),u()},n,s);s.getInitialState=()=>d;let m;const v=()=>{var f,g;if(!l)return;i=!1,a.forEach(p=>{var E;return p((E=n())!=null?E:d)});const S=((g=o.onRehydrateStorage)==null?void 0:g.call(o,(f=n())!=null?f:d))||void 0;return z(l.getItem.bind(l))(o.name).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==o.version){if(o.migrate)return[!0,o.migrate(p.state,p.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,p.state];return[!1,void 0]}).then(p=>{var E;const[G,B]=p;if(m=o.merge(B,(E=n())!=null?E:d),t(m,!0),G)return u()}).then(()=>{S==null||S(m,void 0),m=n(),i=!0,c.forEach(p=>p(m))}).catch(p=>{S==null||S(void 0,p)})};return s.persist={setOptions:f=>{o={...o,...f},f.storage&&(l=f.storage)},clearStorage:()=>{l==null||l.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>v(),hasHydrated:()=>i,onHydrate:f=>(a.add(f),()=>{a.delete(f)}),onFinishHydration:f=>(c.add(f),()=>{c.delete(f)})},o.skipHydration||v(),m||d},dt=(r,e)=>"getStorage"in e||"serialize"in e||"deserialize"in e?((it?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),ct(r,e)):lt(r,e),ut={fontSize:16,lineHeight:1.6,contentWidth:65,theme:"light",fontFamily:"system-ui, sans-serif",enableAI:!0,autoExtract:!0,keyboardShortcuts:!0,showProgress:!0,enableSync:!1,language:"zh-CN"},b=st()(dt((r,e)=>({settings:ut,updateSettings:t=>{const s={...e().settings,...t};r({settings:s}),typeof chrome<"u"&&chrome.storage&&chrome.storage.local.set({userSettings:s})},content:null,setContent:t=>r({content:t}),isLoading:!1,setLoading:t=>r({isLoading:t}),error:null,setError:t=>r({error:t}),isReaderActive:!1,setReaderActive:t=>r({isReaderActive:t})}),{name:"reading-assistant-store",partialize:r=>({settings:r.settings})})),ft=async()=>{try{if(typeof chrome<"u"&&chrome.storage){const r=await chrome.storage.local.get(["userSettings"]);r.userSettings&&b.getState().updateSettings(r.userSettings)}}catch(r){console.warn("Failed to load settings from browser storage:",r)}};class ht{constructor(){w(this,"container",null);w(this,"overlay",null);w(this,"isRendered",!1)}async render(e){try{console.log("[阅读器渲染器] 开始渲染阅读器"),this.isRendered&&this.destroy(),this.createOverlay(),this.createContainer(),this.renderContent(e),this.applyStyles(),this.appendToPage(),this.isRendered=!0,console.log("[阅读器渲染器] 阅读器渲染完成")}catch(t){throw console.error("[阅读器渲染器] 渲染失败:",t),t}}updateSettings(e){!this.isRendered||!this.container||(console.log("[阅读器渲染器] 更新设置:",e),this.applyStyles())}destroy(){try{console.log("[阅读器渲染器] 销毁阅读器"),this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.overlay&&this.overlay.parentNode&&this.overlay.parentNode.removeChild(this.overlay),this.container=null,this.overlay=null,this.isRendered=!1,document.body.style.overflow=""}catch(e){console.error("[阅读器渲染器] 销毁失败:",e)}}createOverlay(){this.overlay=document.createElement("div"),this.overlay.id="reading-assistant-overlay",this.overlay.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999998;
      backdrop-filter: blur(2px);
    `,this.overlay.addEventListener("click",e=>{e.target===this.overlay&&(this.destroy(),b.getState().setReaderActive(!1))})}createContainer(){this.container=document.createElement("div"),this.container.id="reading-assistant-container",this.container.innerHTML=this.getContainerHTML()}getContainerHTML(){return`
      <div class="reader-header">
        <div class="header-content">
          <div class="reader-title"></div>
          <div class="reader-meta"></div>
        </div>
        <div class="reader-controls">
          <button class="control-btn" id="reader-settings" title="设置 (S)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </svg>
          </button>
          <button class="control-btn" id="reader-fullscreen" title="全屏切换 (F)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
            </svg>
          </button>
          <button class="control-btn" id="reader-close" title="关闭阅读器 (Esc)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="reader-content">
        <div class="content-inner"></div>
      </div>
    `}renderContent(e){if(!this.container)return;const t=this.container.querySelector(".reader-title");t&&(t.textContent=e.title);const n=this.container.querySelector(".reader-meta");n&&(n.innerHTML=`
        <span>字数: ${e.metadata.wordCount.toLocaleString()}</span>
        <span>阅读时间: ${e.metadata.readingTime}分钟</span>
        <span>置信度: ${Math.round(e.confidence*100)}%</span>
        <span>语言: ${e.metadata.language}</span>
      `);const s=this.container.querySelector(".content-inner");if(s){let o="";e.originalContent&&e.originalContent.trim()?o=this.processHtmlContent(e.originalContent):o=e.content.split(`
`).filter(a=>a.trim()).map(a=>`<p>${this.escapeHtml(a)}</p>`).join(""),s.innerHTML=o}this.bindEvents()}processHtmlContent(e){const t=document.createElement("div");return t.innerHTML=e,this.preprocessMediaContent(t),this.removeUnwantedElements(t),this.optimizeContentStructure(t),this.cleanEmptyElements(t),t.innerHTML}preprocessMediaContent(e){e.querySelectorAll("img[data-src], img[data-lazy-src], img[data-original]").forEach(o=>{const i=o.getAttribute("data-src")||o.getAttribute("data-lazy-src")||o.getAttribute("data-original");i&&!o.getAttribute("src")&&o.setAttribute("src",i)}),e.querySelectorAll("img[data-srcset]").forEach(o=>{const i=o.getAttribute("data-srcset");i&&!o.getAttribute("srcset")&&o.setAttribute("srcset",i)}),e.querySelectorAll("[data-video-id], [data-youtube-id]").forEach(o=>{var a;const i=o.getAttribute("data-video-id")||o.getAttribute("data-youtube-id");if(i){const c=document.createElement("div");c.className="video-placeholder",c.innerHTML=`
          <div style="
            background: #f0f0f0;
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin: 16px 0;
          ">
            📹 视频内容: ${i}
          </div>
        `,(a=o.parentNode)==null||a.replaceChild(c,o)}})}removeUnwantedElements(e){["script","style","noscript",'link[rel="stylesheet"]',".ad",".advertisement",".promo",".sponsor",".social-share",".sharing",".social-buttons",".comments",".comment-section",".disqus",".related",".recommended",".sidebar",".widget",".nav",".navigation",".menu",".breadcrumb",".header",".footer",".banner","form",".search",".newsletter",".subscribe"].forEach(o=>{e.querySelectorAll(o).forEach(a=>a.remove())}),e.querySelectorAll('[style*="display: none"], [style*="visibility: hidden"], .hidden').forEach(o=>o.remove()),e.querySelectorAll("a").forEach(o=>{var c,l;const i=o.getAttribute("href")||"",a=((c=o.textContent)==null?void 0:c.trim())||"";if(!i||i==="#"||a.length===0||a.toLowerCase().includes("read more")||a.toLowerCase().includes("continue reading"))if(o.querySelector("img")){const u=o.querySelector("img");u&&((l=o.parentNode)==null||l.replaceChild(u,o))}else o.remove()})}optimizeContentStructure(e){["p","div","span"].forEach(s=>{const o=e.querySelectorAll(s);for(let i=0;i<o.length-1;i++){const a=o[i],c=o[i+1];c&&a.nextElementSibling===c&&a.className===c.className&&!a.querySelector("img, video, audio")&&!c.querySelector("img, video, audio")&&(a.innerHTML+=" "+c.innerHTML,c.remove())}}),e.querySelectorAll("img").forEach(s=>{var i,a,c;const o=s.parentElement;if(o&&o.tagName.toLowerCase()==="a"&&((i=o.parentNode)==null||i.replaceChild(s,o)),((a=s.parentElement)==null?void 0:a.tagName.toLowerCase())==="div"&&s.parentElement.children.length===1){const l=document.createElement("p");l.style.textAlign="center",(c=s.parentElement.parentNode)==null||c.replaceChild(l,s.parentElement),l.appendChild(s)}})}cleanEmptyElements(e){let t=!0,n=0;const s=5;for(;t&&n<s;)t=!1,n++,e.querySelectorAll("*").forEach(i=>{var u,y;const a=((u=i.textContent)==null?void 0:u.trim().length)>0,c=i.querySelector("img, video, audio, iframe, canvas, svg"),l=i.tagName.toLowerCase()==="br"||i.tagName.toLowerCase()==="hr";(!a&&!c&&!l&&i.children.length===0||((y=i.textContent)==null?void 0:y.trim())===""&&!c&&!l)&&(i.remove(),t=!0)})}bindEvents(){if(!this.container)return;const e=this.container.querySelector("#reader-close");e==null||e.addEventListener("click",()=>{this.destroy(),b.getState().setReaderActive(!1)});const t=this.container.querySelector("#reader-settings");t==null||t.addEventListener("click",()=>{console.log("打开设置面板")})}applyStyles(){if(!this.container)return;const e=b.getState().settings,t=I[e.theme];this.container.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: ${t.bg};
      color: ${t.text};
      z-index: 999999;
      font-family: ${e.fontFamily};
      font-size: ${e.fontSize}px;
      line-height: ${e.lineHeight};
      overflow: hidden;
      display: flex;
      flex-direction: column;
    `;const n=this.container.querySelector(".reader-header");n&&(n.style.cssText=`
        padding: 16px 20px;
        border-bottom: 1px solid ${t.border};
        background: ${t.secondary};
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
      `);const s=this.container.querySelector(".header-content");s&&(s.style.cssText=`
        flex: 1;
        min-width: 0;
      `);const o=this.container.querySelector(".reader-controls");o&&(o.style.cssText=`
        display: flex;
        gap: 8px;
        flex-shrink: 0;
      `),this.container.querySelectorAll(".control-btn").forEach(u=>{u.style.cssText=`
        background: none;
        border: 1px solid ${t.border};
        color: ${t.text};
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        margin-left: 8px;
        font-size: 14px;
        transition: all 0.2s;
      `,u.addEventListener("mouseenter",()=>{u.style.background=t.border}),u.addEventListener("mouseleave",()=>{u.style.background="none"})});const a=this.container.querySelector(".reader-title");a&&(a.style.cssText=`
        font-size: ${Math.max(e.fontSize+8,24)}px;
        font-weight: 700;
        margin: 0;
        color: ${t.text};
        line-height: 1.3;
        max-width: 80%;
      `);const c=this.container.querySelector(".reader-meta");c&&(c.style.cssText=`
        font-size: ${Math.max(e.fontSize-2,12)}px;
        color: ${t.text};
        opacity: 0.7;
        display: flex;
        gap: 16px;
        margin-top: 8px;
      `);const l=this.container.querySelector(".reader-content");if(l){l.style.cssText=`
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        justify-content: center;
        background: ${t.bg};
      `;const u=l.querySelector(".content-inner");if(u){const y=Math.min(e.contentWidth*10,800);u.style.cssText=`
          max-width: ${y}px;
          width: 100%;
          padding: 32px 24px;
          line-height: ${e.lineHeight};
        `,this.applyContentStyles(u,e,t)}}}applyContentStyles(e,t,n){e.querySelectorAll("p").forEach(d=>{d.style.cssText=`
        margin: 0 0 ${t.fontSize*1.2}px 0;
        text-align: justify;
        word-break: break-word;
        hyphens: auto;
        color: ${n.text};
      `}),e.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach((d,m)=>{const v=parseInt(d.tagName.charAt(1)),f=Math.max(t.fontSize+(7-v)*2,t.fontSize);d.style.cssText=`
        font-size: ${f}px;
        font-weight: 600;
        margin: ${f*1.5}px 0 ${f*.8}px 0;
        color: ${n.text};
        line-height: 1.3;
      `,m===0&&(d.style.marginTop=`${f*.5}px`)}),e.querySelectorAll("ul, ol").forEach(d=>{d.style.cssText=`
        margin: ${t.fontSize}px 0;
        padding-left: ${t.fontSize*2}px;
        color: ${n.text};
      `,d.querySelectorAll("li").forEach(v=>{v.style.cssText=`
          margin: ${t.fontSize*.5}px 0;
          line-height: ${t.lineHeight};
        `})}),e.querySelectorAll("img").forEach(d=>{this.processImageElement(d,t,n)}),e.querySelectorAll("blockquote").forEach(d=>{d.style.cssText=`
        margin: ${t.fontSize*1.5}px 0;
        padding: ${t.fontSize}px ${t.fontSize*1.5}px;
        border-left: 4px solid ${n.border};
        background: ${n.secondary};
        font-style: italic;
        color: ${n.text};
        border-radius: 0 8px 8px 0;
      `}),e.querySelectorAll("pre, code").forEach(d=>{d.style.cssText=`
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: ${Math.max(t.fontSize-2,12)}px;
        background: ${n.secondary};
        color: ${n.text};
        padding: ${d.tagName==="PRE"?t.fontSize:"2px 6px"};
        border-radius: 4px;
        overflow-x: auto;
        ${d.tagName==="PRE"?`margin: ${t.fontSize}px 0;`:"display: inline;"}
      `}),e.querySelectorAll("a").forEach(d=>{d.style.cssText=`
        color: #007acc;
        text-decoration: underline;
        text-decoration-color: rgba(0, 122, 204, 0.3);
        transition: all 0.2s;
      `,d.addEventListener("mouseenter",()=>{d.style.textDecorationColor="#007acc"}),d.addEventListener("mouseleave",()=>{d.style.textDecorationColor="rgba(0, 122, 204, 0.3)"})}),e.querySelectorAll("table").forEach(d=>{d.style.cssText=`
        width: 100%;
        border-collapse: collapse;
        margin: ${t.fontSize*1.5}px 0;
        font-size: ${Math.max(t.fontSize-1,13)}px;
      `,d.querySelectorAll("th, td").forEach(f=>{f.style.cssText=`
          padding: ${t.fontSize*.5}px ${t.fontSize*.8}px;
          border: 1px solid ${n.border};
          text-align: left;
          color: ${n.text};
        `}),d.querySelectorAll("th").forEach(f=>{f.style.background=n.secondary,f.style.fontWeight="600"})})}processImageElement(e,t,n){e.style.cssText=`
      max-width: 100%;
      height: auto;
      margin: ${t.fontSize*1.5}px auto;
      display: block;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;
      cursor: pointer;
    `,this.handleLazyLoadedImage(e),this.addImageErrorHandling(e,t,n),this.wrapImageWithCaption(e,t,n),this.addImageInteractions(e)}handleLazyLoadedImage(e){const t=e.getAttribute("data-src")||e.getAttribute("data-lazy-src")||e.getAttribute("data-original");t&&!e.src&&(e.src=t);const n=e.getAttribute("data-srcset");n&&!e.srcset&&(e.srcset=n),"loading"in e&&(e.loading="eager")}addImageErrorHandling(e,t,n){e.addEventListener("error",()=>{var c;const s=document.createElement("div");s.style.cssText=`
        width: 100%;
        min-height: 200px;
        background: ${n.secondary};
        border: 2px dashed ${n.border};
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: ${t.fontSize*1.5}px auto;
        color: ${n.text};
        opacity: 0.7;
        font-size: ${t.fontSize-2}px;
      `;const o=document.createElement("div");o.innerHTML="🖼️",o.style.fontSize="48px",o.style.marginBottom="12px";const i=document.createElement("div");i.textContent="图片加载失败",i.style.marginBottom="8px";const a=e.alt;if(a){const l=document.createElement("div");l.textContent=`描述: ${a}`,l.style.fontSize=`${t.fontSize-4}px`,l.style.fontStyle="italic",l.style.textAlign="center",l.style.maxWidth="80%",s.appendChild(l)}s.appendChild(o),s.appendChild(i),(c=e.parentNode)==null||c.replaceChild(s,e)})}wrapImageWithCaption(e,t,n){var c;const s=e.alt,o=e.title,i=this.findImageCaption(e);if(!s&&!o&&!i)return;const a=document.createElement("figure");if(a.style.cssText=`
      margin: ${t.fontSize*2}px 0;
      text-align: center;
    `,(c=e.parentNode)==null||c.insertBefore(a,e),a.appendChild(e),s||o||i){const l=document.createElement("figcaption");l.style.cssText=`
        margin-top: ${t.fontSize*.8}px;
        font-size: ${Math.max(t.fontSize-2,12)}px;
        color: ${n.text};
        opacity: 0.8;
        font-style: italic;
        line-height: 1.4;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
      `;const u=i||s||o;l.textContent=u,a.appendChild(l)}}findImageCaption(e){const t=e.closest("figure");if(t){const s=t.querySelector("figcaption");if(s!=null&&s.textContent)return s.textContent.trim()}const n=e.parentElement;if(n){const s=[".caption",".image-caption",".photo-caption",".img-caption",".description",".subtitle"];for(const o of s){const i=n.querySelector(o);if(i!=null&&i.textContent)return i.textContent.trim()}}return""}addImageInteractions(e){e.addEventListener("mouseenter",()=>{e.style.transform="scale(1.02)"}),e.addEventListener("mouseleave",()=>{e.style.transform="scale(1)"}),e.addEventListener("click",()=>{this.showImageModal(e)})}showImageModal(e){const t=document.createElement("div");t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.9);
      z-index: 1000000;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    `;const n=document.createElement("img");n.src=e.src,n.alt=e.alt,n.style.cssText=`
      max-width: 90vw;
      max-height: 90vh;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    `,t.appendChild(n),t.addEventListener("click",()=>{document.body.removeChild(t)});const s=o=>{o.key==="Escape"&&(document.body.removeChild(t),document.removeEventListener("keydown",s))};document.addEventListener("keydown",s),document.body.appendChild(t)}appendToPage(){this.overlay&&this.container&&(document.body.appendChild(this.overlay),document.body.appendChild(this.container),document.body.style.overflow="hidden")}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}}class pt{constructor(){w(this,"handlers",new Map);w(this,"isEnabled",!1);this.bindGlobalEvents()}onKeyPress(e,t){this.handlers.set(e,t)}enable(){this.isEnabled=!0,console.log("[键盘处理器] 已启用")}disable(){this.isEnabled=!1,console.log("[键盘处理器] 已禁用")}bindGlobalEvents(){document.addEventListener("keydown",e=>{if(!this.isEnabled)return;const t=this.getKeyFromEvent(e),n=this.handlers.get(t);n&&(e.preventDefault(),e.stopPropagation(),n())})}getKeyFromEvent(e){return e.key==="Escape"?"Escape":e.key==="="&&(e.ctrlKey||e.metaKey)?"Equal":e.key==="-"&&(e.ctrlKey||e.metaKey)?"Minus":e.code.startsWith("Key")?e.code:e.key}}class gt{constructor(){w(this,"handlers",new Map);this.bindMessageEvents()}onMessage(e,t){this.handlers.set(e,t)}bindMessageEvents(){chrome.runtime.onMessage.addListener((e,t,n)=>{console.log("[消息处理器] 收到消息:",e);const s=this.handlers.get(e.type);if(s)try{const o=s(e.payload);if(o instanceof Promise)return o.then(i=>n({success:!0,data:i})).catch(i=>n({success:!1,error:i.message})),!0;n({success:!0,data:o})}catch(o){console.error("[消息处理器] 处理消息失败:",o),n({success:!1,error:o instanceof Error?o.message:String(o)})}else console.warn("[消息处理器] 未知消息类型:",e.type),n({success:!1,error:"Unknown message type"})})}}class he{constructor(){w(this,"extractor");w(this,"renderer");w(this,"keyboardHandler");w(this,"messageHandler");w(this,"isActive",!1);this.extractor=new ge,this.renderer=new ht,this.keyboardHandler=new pt,this.messageHandler=new gt,this.init()}async init(){try{console.log("[阅读助手] Content Script 初始化开始"),await ft(),this.setupMessageHandlers(),this.setupKeyboardHandlers(),b.getState().settings.autoExtract&&setTimeout(()=>this.checkAutoExtract(),2e3),console.log("[阅读助手] Content Script 初始化完成")}catch(e){console.error("[阅读助手] 初始化失败:",e)}}setupMessageHandlers(){this.messageHandler.onMessage("TOGGLE_READER",async()=>{try{return await this.toggleReader(),{success:!0,message:"阅读模式切换成功"}}catch(e){return console.error("[阅读助手] 切换失败:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}),this.messageHandler.onMessage("EXTRACT_CONTENT",async()=>{try{return{success:!0,content:await this.extractContent()}}catch(e){return console.error("[阅读助手] 内容提取失败:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}),this.messageHandler.onMessage("UPDATE_SETTINGS",e=>{this.handleSettingsUpdate(e)}),this.messageHandler.onMessage("GET_SETTINGS",()=>b.getState().settings)}setupKeyboardHandlers(){this.keyboardHandler.onKeyPress("Escape",()=>{this.isActive&&this.closeReader()}),this.keyboardHandler.onKeyPress("Equal",()=>{this.isActive&&this.adjustFontSize(1)}),this.keyboardHandler.onKeyPress("Minus",()=>{this.isActive&&this.adjustFontSize(-1)})}async toggleReader(){console.log("[阅读助手] 开始切换阅读模式，当前状态:",this.isActive);try{this.isActive?(console.log("[阅读助手] 关闭阅读模式"),this.closeReader()):(console.log("[阅读助手] 开启阅读模式"),await this.openReader()),console.log("[阅读助手] 阅读模式切换完成，新状态:",this.isActive)}catch(e){throw console.error("[阅读助手] 切换阅读模式失败:",e),this.showError("切换阅读模式失败，请重试"),e}}async openReader(){console.log("[阅读助手] 开启阅读模式"),this.showLoading();try{const e=await this.extractContent();if(!e)throw new Error("无法提取页面内容");await this.renderer.render(e),this.isActive=!0,b.getState().setReaderActive(!0),b.getState().settings.keyboardShortcuts&&this.keyboardHandler.enable(),console.log("[阅读助手] 阅读模式已开启")}catch(e){console.error("[阅读助手] 开启阅读模式失败:",e),this.showError("开启阅读模式失败："+(e instanceof Error?e.message:String(e)))}finally{this.hideLoading()}}closeReader(){console.log("[阅读助手] 关闭阅读模式");try{this.renderer.destroy(),this.isActive=!1,b.getState().setReaderActive(!1),this.keyboardHandler.disable(),console.log("[阅读助手] 阅读模式已关闭")}catch(e){console.error("[阅读助手] 关闭阅读模式失败:",e)}}async extractContent(){try{console.log("[阅读助手] 开始提取内容");const e=await this.extractor.extract(document);return e?(b.getState().setContent(e),console.log("[阅读助手] 内容提取成功:",e.title)):console.warn("[阅读助手] 内容提取失败"),e}catch(e){return console.error("[阅读助手] 内容提取错误:",e),null}}handleSettingsUpdate(e){try{b.getState().updateSettings(e),this.isActive&&this.renderer.updateSettings(e),console.log("[阅读助手] 设置已更新:",e)}catch(t){console.error("[阅读助手] 设置更新失败:",t)}}adjustFontSize(e){const t=b.getState().settings,n=Math.max(12,Math.min(24,t.fontSize+e));this.handleSettingsUpdate({fontSize:n})}checkAutoExtract(){b.getState().settings.autoExtract&&this.extractor.canHandle(document)&&(console.log("[阅读助手] 执行自动内容提取"),this.extractContent())}showLoading(){b.getState().setLoading(!0)}hideLoading(){b.getState().setLoading(!1)}showError(e){b.getState().setError(e),console.error("[阅读助手] 错误:",e)}}if(window.__ReadingAssistantInitialized__)console.log("[阅读助手] Content Script已经初始化，跳过重复初始化");else{console.log("[阅读助手] 开始初始化Content Script"),window.__ReadingAssistantInitialized__=!0;let r;document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{console.log("[阅读助手] DOM加载完成，初始化阅读助手"),r=new he,window.__ReadingAssistant__=r}):(console.log("[阅读助手] DOM已就绪，立即初始化阅读助手"),r=new he,window.__ReadingAssistant__=r),chrome.runtime.onMessage.addListener((e,t,n)=>(console.log("[阅读助手] 全局消息监听器收到消息:",e),e.type==="TOGGLE_READER"?(console.log("[阅读助手] 处理TOGGLE_READER消息"),r?r.toggleReader().then(()=>{console.log("[阅读助手] 阅读模式切换成功"),n({success:!0,message:"阅读模式切换成功"})}).catch(s=>{console.error("[阅读助手] 切换失败:",s),n({success:!1,error:s instanceof Error?s.message:String(s)})}):(console.warn("[阅读助手] 助手实例未初始化"),n({success:!1,error:"助手未初始化"})),!0):!1))}})();
