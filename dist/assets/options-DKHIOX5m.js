import{u as v,r as m,l as k,j as e,T as S,F as x,a as f,L as C,W as E,e as I,i as F,b as T,c as y}from"./constants-B6hLOxd8.js";const O=()=>{const{settings:t,updateSettings:g}=v(),[o,c]=m.useState("idle");m.useEffect(()=>{k()},[]);const a=(s,n)=>{g({[s]:n}),c("saving"),setTimeout(()=>c("saved"),500),setTimeout(()=>c("idle"),2e3)},p=()=>{confirm("确定要重置所有设置到默认值吗？")&&(T(),c("saved"),setTimeout(()=>c("idle"),2e3))},u=()=>{try{const s=I(),n=new Blob([s],{type:"application/json"}),l=URL.createObjectURL(n),i=document.createElement("a");i.href=l,i.download="reading-assistant-settings.json",i.click(),URL.revokeObjectURL(l)}catch(s){console.error("导出设置失败:",s),alert("导出设置失败")}},N=()=>{const s=document.createElement("input");s.type="file",s.accept=".json",s.onchange=n=>{var i;const l=(i=n.target.files)==null?void 0:i[0];if(l){const d=new FileReader;d.onload=b=>{var h;try{const r=(h=b.target)==null?void 0:h.result;F(r),c("saved"),setTimeout(()=>c("idle"),2e3)}catch(r){console.error("导入设置失败:",r),alert("导入设置失败：文件格式不正确")}},d.readAsText(l)}},s.click()};return e.jsxs("div",{className:"options-container",children:[e.jsxs("header",{className:"options-header",children:[e.jsx("h1",{children:"阅读助手 - 设置"}),e.jsxs("div",{className:"save-status",children:[o==="saving"&&e.jsx("span",{className:"status saving",children:"保存中..."}),o==="saved"&&e.jsx("span",{className:"status saved",children:"已保存"})]})]}),e.jsxs("main",{className:"options-main",children:[e.jsxs("div",{className:"settings-grid",children:[e.jsxs("section",{className:"settings-section",children:[e.jsx("h2",{children:"外观设置"}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{children:"主题"}),e.jsx("div",{className:"theme-grid",children:Object.entries(S).map(([s,n])=>e.jsxs("button",{className:`theme-option ${t.theme===s?"active":""}`,onClick:()=>a("theme",s),style:{backgroundColor:n.bg,color:n.text,border:`2px solid ${n.border}`},children:[s==="light"&&"明亮",s==="dark"&&"深色",s==="sepia"&&"护眼",s==="nature"&&"自然",s==="warm"&&"温馨",s==="eyecare"&&"纸质"]},s))})]}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{htmlFor:"fontSize",children:["字体大小: ",t.fontSize,"px"]}),e.jsx("input",{id:"fontSize",type:"range",min:x.min,max:x.max,value:t.fontSize,onChange:s=>a("fontSize",parseInt(s.target.value)),className:"slider"})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"fontFamily",children:"字体"}),e.jsx("select",{id:"fontFamily",value:t.fontFamily,onChange:s=>a("fontFamily",s.target.value),className:"select",children:f.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{htmlFor:"lineHeight",children:["行高: ",t.lineHeight]}),e.jsx("select",{id:"lineHeight",value:t.lineHeight,onChange:s=>a("lineHeight",parseFloat(s.target.value)),className:"select",children:C.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{htmlFor:"contentWidth",children:["内容宽度: ",t.contentWidth,"%"]}),e.jsx("select",{id:"contentWidth",value:t.contentWidth,onChange:s=>a("contentWidth",parseInt(s.target.value)),className:"select",children:E.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]})]}),e.jsxs("section",{className:"settings-section",children:[e.jsx("h2",{children:"功能设置"}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{className:"checkbox-label",children:[e.jsx("input",{type:"checkbox",checked:t.enableAI,onChange:s=>a("enableAI",s.target.checked)}),e.jsx("span",{className:"checkmark"}),"启用AI增强功能"]}),e.jsx("p",{className:"setting-description",children:"使用AI技术优化内容提取和排版效果"})]}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{className:"checkbox-label",children:[e.jsx("input",{type:"checkbox",checked:t.autoExtract,onChange:s=>a("autoExtract",s.target.checked)}),e.jsx("span",{className:"checkmark"}),"自动提取内容"]}),e.jsx("p",{className:"setting-description",children:"页面加载完成后自动分析并提取主要内容"})]}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{className:"checkbox-label",children:[e.jsx("input",{type:"checkbox",checked:t.keyboardShortcuts,onChange:s=>a("keyboardShortcuts",s.target.checked)}),e.jsx("span",{className:"checkmark"}),"启用键盘快捷键"]}),e.jsx("p",{className:"setting-description",children:"支持Esc关闭、Ctrl+/-调节字号等快捷键"})]}),e.jsxs("div",{className:"setting-item",children:[e.jsxs("label",{className:"checkbox-label",children:[e.jsx("input",{type:"checkbox",checked:t.showProgress,onChange:s=>a("showProgress",s.target.checked)}),e.jsx("span",{className:"checkmark"}),"显示进度指示"]}),e.jsx("p",{className:"setting-description",children:"在内容提取和处理时显示进度条"})]})]})]}),e.jsxs("section",{className:"settings-actions",children:[e.jsx("h2",{children:"设置管理"}),e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{onClick:u,className:"action-btn secondary",children:"导出设置"}),e.jsx("button",{onClick:N,className:"action-btn secondary",children:"导入设置"}),e.jsx("button",{onClick:p,className:"action-btn danger",children:"重置设置"})]})]}),e.jsxs("section",{className:"settings-section",children:[e.jsx("h2",{children:"关于"}),e.jsxs("div",{className:"about-info",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"阅读助手"})," v0.2.0"]}),e.jsx("p",{children:"跨浏览器网页内容提取与重排版插件"}),e.jsx("p",{children:"React + TypeScript 现代化版本"})]})]})]})]})},j=document.getElementById("options-root");j?y(j).render(e.jsx(O,{})):console.error("找不到options-root容器");
