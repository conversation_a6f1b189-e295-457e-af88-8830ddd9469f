var j=Object.defineProperty;var S=(a,e,r)=>e in a?j(a,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[e]=r;var h=(a,e,r)=>S(a,typeof e!="symbol"?e+"":e,r);import{u as E,r as g,j as o,E as v,c as N}from"./constants-B6hLOxd8.js";const i=class i{static getInstance(){return i.instance||(i.instance=new i),i.instance}async setStorage(e,r){try{if(typeof chrome<"u"&&chrome.storage)return chrome.storage.local.set({[e]:r});if(typeof browser<"u"&&browser.storage)return browser.storage.local.set({[e]:r});localStorage.setItem(e,JSON.stringify(r))}catch(t){throw console.error("Failed to set storage:",t),t}}async getStorage(e){try{if(typeof chrome<"u"&&chrome.storage)return(await chrome.storage.local.get(e))[e];if(typeof browser<"u"&&browser.storage)return(await browser.storage.local.get(e))[e];{const r=localStorage.getItem(e);return r?JSON.parse(r):void 0}}catch(r){console.error("Failed to get storage:",r);return}}async removeStorage(e){try{if(typeof chrome<"u"&&chrome.storage)return chrome.storage.local.remove(e);if(typeof browser<"u"&&browser.storage)return browser.storage.local.remove(e);localStorage.removeItem(e)}catch(r){throw console.error("Failed to remove storage:",r),r}}async clearStorage(){try{if(typeof chrome<"u"&&chrome.storage)return chrome.storage.local.clear();if(typeof browser<"u"&&browser.storage)return browser.storage.local.clear();localStorage.clear()}catch(e){throw console.error("Failed to clear storage:",e),e}}async sendMessage(e){try{if(typeof chrome<"u"&&chrome.runtime)return chrome.runtime.sendMessage(e);if(typeof browser<"u"&&browser.runtime)return browser.runtime.sendMessage(e);throw new Error("Browser API not available")}catch(r){throw console.error("Failed to send message:",r),r}}async getCurrentTab(){try{if(typeof chrome<"u"&&chrome.tabs)return(await chrome.tabs.query({active:!0,currentWindow:!0}))[0];if(typeof browser<"u"&&browser.tabs)return(await browser.tabs.query({active:!0,currentWindow:!0}))[0];throw new Error("Tabs API not available")}catch(e){throw console.error("Failed to get current tab:",e),e}}async sendTabMessage(e,r){try{if(typeof chrome<"u"&&chrome.tabs)return chrome.tabs.sendMessage(e,r);if(typeof browser<"u"&&browser.tabs)return browser.tabs.sendMessage(e,r);throw new Error("Tabs API not available")}catch(t){throw console.error("Failed to send tab message:",t),t}}isSpecialPage(e){return["chrome://","chrome-extension://","moz-extension://","edge://","about:","data:","file://"].some(t=>e.startsWith(t))}};h(i,"instance");let p=i;const P=()=>{const{isLoading:a,setLoading:e,error:r,setError:t}=E(),[s,f]=g.useState(null);g.useEffect(()=>{(async()=>{try{const c=await p.getInstance().getCurrentTab();f(c)}catch(l){console.error("获取当前标签页失败:",l),t("无法获取当前页面信息")}})()},[t]);const b=async()=>{if(!(s!=null&&s.id)){t("无法获取当前页面信息");return}const m=p.getInstance();if(s.url&&m.isSpecialPage(s.url)){t(v.SPECIAL_PAGE);return}e(!0),t(null);try{console.log("[阅读助手] 开始处理阅读模式切换");try{await chrome.scripting.executeScript({target:{tabId:s.id},files:["content.js"]}),await chrome.scripting.insertCSS({target:{tabId:s.id},files:["content.css"]}),console.log("[阅读助手] Content script注入成功")}catch(n){console.warn("[阅读助手] Content script注入失败:",n)}await new Promise(n=>setTimeout(n,500));const l={type:"TOGGLE_READER"};let c=0;const d=5;let u;for(;c<d;)try{console.log(`[阅读助手] 尝试发送消息 (${c+1}/${d})`);const n=await chrome.tabs.sendMessage(s.id,l);if(console.log("[阅读助手] 消息发送成功，响应:",n),n&&n.success===!1)throw new Error(n.error||"阅读模式启动失败");window.close();return}catch(n){u=n,c++,console.warn(`[阅读助手] 消息发送失败 (${c}/${d}):`,n),c<d&&await new Promise(x=>setTimeout(x,300))}throw new Error(`消息发送失败，已重试${d}次。最后错误: ${(u==null?void 0:u.message)||u}`)}catch(l){console.error("[阅读助手] 处理失败:",l),t("无法启动阅读模式，请刷新页面后重试")}finally{e(!1)}},y=()=>{chrome.runtime.openOptionsPage(),window.close()};return o.jsxs("div",{className:"popup-container",children:[o.jsxs("div",{className:"popup-header",children:[o.jsx("h1",{className:"popup-title",children:"阅读助手"}),o.jsx("span",{className:"popup-version",children:"v0.2.0"})]}),o.jsxs("div",{className:"popup-content",children:[r&&o.jsxs("div",{className:"error-message",children:[o.jsx("span",{className:"error-icon",children:"⚠️"}),o.jsx("span",{className:"error-text",children:r})]}),o.jsxs("div",{className:"popup-actions",children:[o.jsx("button",{className:"action-button primary",onClick:b,disabled:a,children:a?o.jsxs(o.Fragment,{children:[o.jsx("span",{className:"loading-spinner"}),"处理中..."]}):"开启/关闭 阅读模式"}),o.jsx("button",{className:"action-button secondary",onClick:y,disabled:a,children:"设置选项"})]}),o.jsxs("div",{className:"popup-tips",children:[o.jsxs("p",{className:"tip-text",children:["💡 提示：可在页面中按 ",o.jsx("kbd",{children:"Esc"})," 关闭阅读模式"]}),(s==null?void 0:s.url)&&o.jsxs("p",{className:"current-url",children:["当前页面：",new URL(s.url).hostname]})]})]})]})},w=document.getElementById("popup-root");w?N(w).render(o.jsx(P,{})):console.error("找不到popup-root容器");
