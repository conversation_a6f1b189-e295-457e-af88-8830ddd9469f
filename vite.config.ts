import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/hooks': resolve(__dirname, 'src/hooks'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/stores': resolve(__dirname, 'src/stores')
    }
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    emptyOutDir: true,

    // 多入口配置 - 浏览器扩展需要多个入口点
    rollupOptions: {
      input: {
        // 弹出窗口
        popup: resolve(__dirname, 'src/popup/index.html'),
        // 选项页面
        options: resolve(__dirname, 'src/options/index.html')
        // content和background script将单独构建
      },

      output: {
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    
    // 代码分割配置
    chunkSizeWarningLimit: 1000,
    
    // 源码映射
    sourcemap: process.env.NODE_ENV === 'development'
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    host: 'localhost',
    
    // 热重载配置
    hmr: {
      port: 3001
    }
  },
  
  // 环境变量配置
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __VERSION__: JSON.stringify(process.env.npm_package_version || '0.2.0')
  },
  
  // 优化配置
  optimizeDeps: {
    include: ['react', 'react-dom', 'zustand']
  }
});
