# 阅读助手插件技术架构设计文档

## 文档信息

| 项目名称 | 阅读助手浏览器插件 |
|---------|------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2024-01-15 |
| 最后更新 | 2024-01-15 |
| 文档状态 | 草案 |
| 作者 | 架构团队 |

## 目录

1. [项目概述](#1-项目概述)
2. [架构原则与设计目标](#2-架构原则与设计目标)
3. [系统架构图](#3-系统架构图)
4. [技术栈选择](#4-技术栈选择)
5. [核心模块设计](#5-核心模块设计)
6. [数据架构](#6-数据架构)
7. [API设计](#7-api设计)
8. [安全架构](#8-安全架构)
9. [性能优化策略](#9-性能优化策略)
10. [部署架构](#10-部署架构)
11. [扩展性设计](#11-扩展性设计)
12. [风险评估与应对](#12-风险评估与应对)

## 1. 项目概述

### 1.1 项目背景

阅读助手插件是一个智能化的浏览器扩展，旨在为用户提供清洁、高效的网页阅读体验。通过AI技术增强的内容提取和重排版功能，帮助用户专注于核心内容，提升阅读效率。

### 1.2 核心功能

#### 主要功能模块
- **智能内容提取**：自动识别并提取网页主要内容
- **AI内容增强**：使用AI技术优化内容质量和结构
- **个性化重排版**：根据用户偏好重新排版内容
- **多主题支持**：提供多种阅读主题和样式
- **跨设备同步**：用户设置和阅读记录云端同步
- **无障碍支持**：完整的无障碍功能支持

#### 技术特性
- **跨浏览器兼容**：支持Chrome、Firefox、Safari、Edge
- **离线功能**：核心功能支持离线使用
- **高性能**：快速内容提取和渲染
- **隐私保护**：本地优先的数据处理策略

### 1.3 用户群体

- **主要用户**：经常阅读网络文章的知识工作者
- **次要用户**：学生、研究人员、内容创作者
- **特殊需求用户**：视觉障碍用户、阅读障碍用户

## 2. 架构原则与设计目标

### 2.1 架构原则

#### 2.1.1 模块化设计
```typescript
// 模块化架构示例
interface ModuleInterface {
  name: string;
  version: string;
  dependencies: string[];
  initialize(): Promise<void>;
  destroy(): Promise<void>;
}

abstract class BaseModule implements ModuleInterface {
  abstract name: string;
  abstract version: string;
  abstract dependencies: string[];
  
  async initialize(): Promise<void> {
    // 基础初始化逻辑
  }
  
  async destroy(): Promise<void> {
    // 清理资源
  }
}
```

#### 2.1.2 单一职责原则
每个模块只负责一个特定的功能领域：
- **ContentExtractor**：专注内容提取
- **AIEnhancer**：专注AI增强功能
- **UIRenderer**：专注界面渲染
- **SettingsManager**：专注设置管理

#### 2.1.3 依赖倒置原则
```typescript
// 依赖注入容器
class DIContainer {
  private services = new Map<string, any>();
  
  register<T>(name: string, factory: () => T): void {
    this.services.set(name, factory);
  }
  
  resolve<T>(name: string): T {
    const factory = this.services.get(name);
    if (!factory) {
      throw new Error(`Service ${name} not found`);
    }
    return factory();
  }
}

// 使用示例
const container = new DIContainer();
container.register('contentExtractor', () => new ContentExtractor());
container.register('aiEnhancer', () => new AIEnhancer());
```

### 2.2 设计目标

#### 2.2.1 性能目标
- **内容提取速度**：< 500ms（90%的页面）
- **UI渲染时间**：< 200ms
- **内存使用**：< 50MB（典型使用场景）
- **CPU使用率**：< 10%（空闲状态）

#### 2.2.2 可用性目标
- **可用性**：99.9%
- **错误率**：< 0.1%
- **用户满意度**：> 4.5/5.0
- **学习成本**：< 5分钟上手

#### 2.2.3 扩展性目标
- **插件系统**：支持第三方插件
- **API开放**：提供开发者API
- **多语言支持**：支持10+语言
- **平台扩展**：支持移动端

#### 2.2.4 安全性目标
- **数据加密**：敏感数据端到端加密
- **隐私保护**：最小化数据收集
- **权限控制**：细粒度权限管理
- **安全审计**：定期安全评估

## 3. 系统架构图

### 3.1 整体架构

```mermaid
graph TB
    subgraph "浏览器环境"
        subgraph "Content Script"
            CS[内容脚本]
            CE[内容提取器]
            UI[UI渲染器]
        end
        
        subgraph "Background Script"
            BS[后台脚本]
            API[API管理器]
            SM[设置管理器]
            CM[缓存管理器]
        end
        
        subgraph "Popup/Options"
            PP[弹出页面]
            OP[选项页面]
        end
    end
    
    subgraph "云端服务"
        subgraph "AI服务"
            AI[AI增强引擎]
            NLP[自然语言处理]
        end
        
        subgraph "后端服务"
            AUTH[认证服务]
            SYNC[同步服务]
            ANALYTICS[分析服务]
        end
        
        subgraph "数据存储"
            DB[(数据库)]
            CACHE[(缓存)]
            CDN[CDN]
        end
    end
    
    CS --> BS
    BS --> API
    API --> AI
    API --> AUTH
    API --> SYNC
    PP --> BS
    OP --> BS
    SM --> DB
    CM --> CACHE
    CDN --> UI
```

### 3.2 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant CS as Content Script
    participant BS as Background Script
    participant AI as AI服务
    participant DB as 数据库
    
    User->>CS: 触发内容提取
    CS->>CS: 基础内容提取
    CS->>BS: 发送提取结果
    BS->>AI: 请求AI增强
    AI->>BS: 返回增强内容
    BS->>CS: 发送最终内容
    CS->>User: 显示重排版内容
    BS->>DB: 保存用户偏好
```

### 3.3 组件交互图

```mermaid
graph LR
    subgraph "前端组件"
        A[内容提取器] --> B[内容解析器]
        B --> C[AI增强器]
        C --> D[重排版引擎]
        D --> E[UI渲染器]
        
        F[设置管理器] --> G[主题管理器]
        G --> E
        
        H[事件管理器] --> A
        H --> F
    end
    
    subgraph "后端服务"
        I[API网关] --> J[认证服务]
        I --> K[AI服务]
        I --> L[同步服务]
        
        M[数据层] --> N[用户数据]
        M --> O[内容缓存]
        M --> P[设置数据]
    end
    
    C --> I
    F --> I
    L --> M
```

## 4. 技术栈选择

### 4.1 前端技术栈

#### 4.1.1 核心框架
```typescript
// 技术选择：TypeScript + React + Vite
interface TechStack {
  language: 'TypeScript';
  framework: 'React 18';
  bundler: 'Vite';
  stateManagement: 'Zustand';
  styling: 'Tailwind CSS + CSS Modules';
  testing: 'Jest + React Testing Library';
}

// 选择理由
const reasons = {
  typescript: [
    '类型安全，减少运行时错误',
    '更好的开发体验和代码提示',
    '便于大型项目维护'
  ],
  react: [
    '组件化开发，代码复用性高',
    '丰富的生态系统',
    '团队熟悉度高'
  ],
  vite: [
    '快速的开发构建',
    '优秀的HMR体验',
    '现代化的构建工具'
  ]
};
```

#### 4.1.2 状态管理
```typescript
// 使用Zustand进行状态管理
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AppState {
  // 用户设置
  settings: UserSettings;
  updateSettings: (settings: Partial<UserSettings>) => void;
  
  // 内容状态
  content: ExtractedContent | null;
  setContent: (content: ExtractedContent) => void;
  
  // UI状态
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  
  // 错误状态
  error: string | null;
  setError: (error: string | null) => void;
}

const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        })),
      
      content: null,
      setContent: (content) => set({ content }),
      
      isLoading: false,
      setLoading: (isLoading) => set({ isLoading }),
      
      error: null,
      setError: (error) => set({ error })
    }),
    {
      name: 'reading-assistant-store',
      partialize: (state) => ({ settings: state.settings })
    }
  )
);
```

### 4.2 浏览器扩展技术

#### 4.2.1 Manifest V3架构
```json
{
  "manifest_version": 3,
  "name": "阅读助手",
  "version": "1.0.0",
  "description": "智能网页阅读助手",
  
  "permissions": [
    "activeTab",
    "storage",
    "scripting"
  ],
  
  "host_permissions": [
    "https://*/*",
    "http://*/*"
  ],
  
  "background": {
    "service_worker": "background.js",
    "type": "module"
  },
  
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content.js"],
      "css": ["content.css"],
      "run_at": "document_idle"
    }
  ],
  
  "action": {
    "default_popup": "popup.html",
    "default_title": "阅读助手"
  },
  
  "options_page": "options.html",
  
  "web_accessible_resources": [
    {
      "resources": ["assets/*"],
      "matches": ["<all_urls>"]
    }
  ]
}
```

#### 4.2.2 跨浏览器兼容性
```typescript
// 浏览器API适配层
class BrowserAPI {
  private static instance: BrowserAPI;
  
  static getInstance(): BrowserAPI {
    if (!BrowserAPI.instance) {
      BrowserAPI.instance = new BrowserAPI();
    }
    return BrowserAPI.instance;
  }
  
  // 统一的存储API
  async setStorage(key: string, value: any): Promise<void> {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      return chrome.storage.local.set({ [key]: value });
    } else if (typeof browser !== 'undefined' && browser.storage) {
      return browser.storage.local.set({ [key]: value });
    } else {
      localStorage.setItem(key, JSON.stringify(value));
    }
  }
  
  async getStorage(key: string): Promise<any> {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get(key);
      return result[key];
    } else if (typeof browser !== 'undefined' && browser.storage) {
      const result = await browser.storage.local.get(key);
      return result[key];
    } else {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : undefined;
    }
  }
  
  // 统一的消息传递API
  sendMessage(message: any): Promise<any> {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      return chrome.runtime.sendMessage(message);
    } else if (typeof browser !== 'undefined' && browser.runtime) {
      return browser.runtime.sendMessage(message);
    } else {
      throw new Error('Browser API not available');
    }
  }
}
```

### 4.3 后端技术栈

#### 4.3.1 服务架构
```typescript
// 微服务架构设计
interface ServiceArchitecture {
  apiGateway: 'Kong' | 'Nginx';
  services: {
    auth: 'Node.js + Express';
    ai: 'Python + FastAPI';
    sync: 'Node.js + Express';
    analytics: 'Node.js + Express';
  };
  database: {
    primary: 'PostgreSQL';
    cache: 'Redis';
    search: 'Elasticsearch';
  };
  messageQueue: 'RabbitMQ';
  monitoring: 'Prometheus + Grafana';
}

// AI服务技术栈
const aiServiceStack = {
  framework: 'FastAPI',
  ml: ['Transformers', 'spaCy', 'scikit-learn'],
  deployment: 'Docker + Kubernetes',
  gpu: 'NVIDIA CUDA',
  models: [
    'BERT for content classification',
    'GPT for content enhancement',
    'Custom models for extraction'
  ]
};
```

#### 4.3.2 数据库设计
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    subscription_tier VARCHAR(50) DEFAULT 'free'
);

-- 用户设置表
CREATE TABLE user_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    settings JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容缓存表
CREATE TABLE content_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url_hash VARCHAR(64) UNIQUE NOT NULL,
    original_content TEXT,
    extracted_content JSONB,
    ai_enhanced_content JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    access_count INTEGER DEFAULT 0
);

-- 用户活动日志表
CREATE TABLE user_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    activity_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_content_cache_url_hash ON content_cache(url_hash);
CREATE INDEX idx_content_cache_expires_at ON content_cache(expires_at);
CREATE INDEX idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX idx_user_activities_created_at ON user_activities(created_at);
```

### 4.4 AI技术栈

#### 4.4.1 内容提取AI
```python
# 内容提取模型架构
import torch
import torch.nn as nn
from transformers import BertModel, BertTokenizer

class ContentExtractionModel(nn.Module):
    def __init__(self, bert_model_name='bert-base-uncased'):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model_name)
        self.dropout = nn.Dropout(0.1)
        self.classifier = nn.Linear(self.bert.config.hidden_size, 2)  # 内容/非内容
        
    def forward(self, input_ids, attention_mask):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        pooled_output = self.dropout(pooled_output)
        logits = self.classifier(pooled_output)
        return logits

# 内容增强服务
class ContentEnhancementService:
    def __init__(self):
        self.tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        self.model = ContentExtractionModel()
        self.load_model()
    
    def load_model(self):
        # 加载预训练模型
        checkpoint = torch.load('models/content_extraction.pth')
        self.model.load_state_dict(checkpoint)
        self.model.eval()
    
    async def enhance_content(self, content: str) -> dict:
        # 内容分析和增强
        analysis = await self.analyze_content(content)
        enhanced = await self.apply_enhancements(content, analysis)
        
        return {
            'original': content,
            'enhanced': enhanced,
            'analysis': analysis,
            'confidence': analysis.get('confidence', 0.0)
        }
    
    async def analyze_content(self, content: str) -> dict:
        # 内容质量分析
        return {
            'readability_score': self.calculate_readability(content),
            'structure_quality': self.analyze_structure(content),
            'content_type': self.classify_content_type(content),
            'confidence': 0.95
        }
```

#### 4.4.2 AI服务API
```python
# FastAPI AI服务
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import asyncio

app = FastAPI(title="Reading Assistant AI Service")

class ContentRequest(BaseModel):
    content: str
    url: str
    options: dict = {}

class ContentResponse(BaseModel):
    enhanced_content: str
    analysis: dict
    processing_time: float
    confidence: float

@app.post("/api/v1/enhance-content", response_model=ContentResponse)
async def enhance_content(request: ContentRequest):
    try:
        start_time = time.time()
        
        # 内容增强处理
        service = ContentEnhancementService()
        result = await service.enhance_content(request.content)
        
        processing_time = time.time() - start_time
        
        return ContentResponse(
            enhanced_content=result['enhanced'],
            analysis=result['analysis'],
            processing_time=processing_time,
            confidence=result['confidence']
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "timestamp": time.time()}
```

## 5. 核心模块设计

### 5.1 内容提取模块

#### 5.1.1 提取器架构
```typescript
// 内容提取器接口
interface ContentExtractor {
  extract(document: Document): Promise<ExtractedContent>;
  canHandle(document: Document): boolean;
  priority: number;
}

// 基础提取器
class BaseContentExtractor implements ContentExtractor {
  priority = 1;
  
  canHandle(document: Document): boolean {
    return true; // 基础提取器可以处理任何文档
  }
  
  async extract(document: Document): Promise<ExtractedContent> {
    const content = this.extractMainContent(document);
    const metadata = this.extractMetadata(document);
    
    return {
      title: this.extractTitle(document),
      content: content,
      metadata: metadata,
      extractedAt: new Date(),
      confidence: this.calculateConfidence(content)
    };
  }
  
  private extractMainContent(document: Document): string {
    // 基于启发式规则的内容提取
    const candidates = this.findContentCandidates(document);
    const scored = this.scoreContentCandidates(candidates);
    return this.selectBestCandidate(scored);
  }
  
  private findContentCandidates(document: Document): HTMLElement[] {
    const selectors = [
      'article',
      '[role="main"]',
      '.content',
      '.post-content',
      '.entry-content',
      '#content',
      'main'
    ];
    
    const candidates: HTMLElement[] = [];
    
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      candidates.push(...Array.from(elements) as HTMLElement[]);
    }
    
    return candidates;
  }
  
  private scoreContentCandidates(candidates: HTMLElement[]): ScoredElement[] {
    return candidates.map(element => ({
      element,
      score: this.calculateElementScore(element)
    }));
  }
  
  private calculateElementScore(element: HTMLElement): number {
    let score = 0;
    
    // 文本长度权重
    const textLength = element.textContent?.length || 0;
    score += Math.min(textLength / 1000, 10);
    
    // 段落数量权重
    const paragraphs = element.querySelectorAll('p').length;
    score += paragraphs * 2;
    
    // 链接密度惩罚
    const links = element.querySelectorAll('a').length;
    const linkDensity = links / Math.max(paragraphs, 1);
    score -= linkDensity * 3;
    
    // 类名和ID权重
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    
    if (className.includes('content') || id.includes('content')) score += 5;
    if (className.includes('article') || id.includes('article')) score += 5;
    if (className.includes('post') || id.includes('post')) score += 3;
    
    // 广告和导航惩罚
    if (className.includes('ad') || className.includes('advertisement')) score -= 10;
    if (className.includes('nav') || className.includes('menu')) score -= 5;
    if (className.includes('sidebar')) score -= 3;
    
    return score;
  }
}

// 智能提取器（使用AI）
class AIContentExtractor implements ContentExtractor {
  priority = 10;
  
  canHandle(document: Document): boolean {
    // 检查是否有复杂的页面结构需要AI处理
    return this.isComplexPage(document);
  }
  
  async extract(document: Document): Promise<ExtractedContent> {
    const htmlContent = document.documentElement.outerHTML;
    
    try {
      const response = await fetch('/api/ai/extract-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          html: htmlContent,
          url: document.location.href
        })
      });
      
      const result = await response.json();
      
      return {
        title: result.title,
        content: result.content,
        metadata: result.metadata,
        extractedAt: new Date(),
        confidence: result.confidence
      };
    } catch (error) {
      // 降级到基础提取器
      const baseExtractor = new BaseContentExtractor();
      return baseExtractor.extract(document);
    }
  }
  
  private isComplexPage(document: Document): boolean {
    // 判断页面复杂度的启发式规则
    const totalElements = document.querySelectorAll('*').length;
    const textElements = document.querySelectorAll('p, div, span').length;
    const complexity = totalElements / textElements;
    
    return complexity > 5; // 复杂度阈值
  }
}

// 提取器管理器
class ContentExtractionManager {
  private extractors: ContentExtractor[] = [];
  
  constructor() {
    this.registerExtractor(new BaseContentExtractor());
    this.registerExtractor(new AIContentExtractor());
    this.registerExtractor(new ReadabilityExtractor());
    this.registerExtractor(new MercuryExtractor());
  }
  
  registerExtractor(extractor: ContentExtractor): void {
    this.extractors.push(extractor);
    this.extractors.sort((a, b) => b.priority - a.priority);
  }
  
  async extract(document: Document): Promise<ExtractedContent> {
    for (const extractor of this.extractors) {
      if (extractor.canHandle(document)) {
        try {
          const result = await extractor.extract(document);
          if (result.confidence > 0.7) {
            return result;
          }
        } catch (error) {
          console.warn(`Extractor ${extractor.constructor.name} failed:`, error);
          continue;
        }
      }
    }
    
    throw new Error('No suitable extractor found');
  }
}
```

#### 5.1.2 内容清理和标准化
```typescript
// 内容清理器
class ContentCleaner {
  private readonly REMOVE_SELECTORS = [
    'script',
    'style',
    'noscript',
    'iframe',
    'object',
    'embed',
    '.advertisement',
    '.ad',
    '.social-share',
    '.comments',
    '.related-posts'
  ];
  
  private readonly UNWRAP_SELECTORS = [
    'font',
    'center',
    'big',
    'small'
  ];
  
  clean(element: HTMLElement): HTMLElement {
    const cleaned = element.cloneNode(true) as HTMLElement;
    
    // 移除不需要的元素
    this.removeUnwantedElements(cleaned);
    
    // 清理属性
    this.cleanAttributes(cleaned);
    
    // 标准化标签
    this.normalizeElements(cleaned);
    
    // 清理空白
    this.cleanWhitespace(cleaned);
    
    return cleaned;
  }
  
  private removeUnwantedElements(element: HTMLElement): void {
    for (const selector of this.REMOVE_SELECTORS) {
      const elements = element.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    }
  }
  
  private cleanAttributes(element: HTMLElement): void {
    const allowedAttributes = new Set([
      'href', 'src', 'alt', 'title', 'id', 'class'
    ]);
    
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_ELEMENT,
      null,
      false
    );
    
    let node;
    while (node = walker.nextNode()) {
      const el = node as HTMLElement;
      const attributes = Array.from(el.attributes);
      
      for (const attr of attributes) {
        if (!allowedAttributes.has(attr.name)) {
          el.removeAttribute(attr.name);
        }
      }
    }
  }
  
  private normalizeElements(element: HTMLElement): void {
    // 将过时的标签转换为现代标签
    const replacements = new Map([
      ['b', 'strong'],
      ['i', 'em'],
      ['u', 'span']
    ]);
    
    for (const [oldTag, newTag] of replacements) {
      const elements = element.querySelectorAll(oldTag);
      elements.forEach(el => {
        const newEl = document.createElement(newTag);
        newEl.innerHTML = el.innerHTML;
        el.parentNode?.replaceChild(newEl, el);
      });
    }
  }
  
  private cleanWhitespace(element: HTMLElement): void {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    
    let node;
    while (node = walker.nextNode()) {
      const textNode = node as Text;
      textNode.textContent = textNode.textContent?.replace(/\s+/g, ' ') || '';
    }
  }
}
```

### 5.2 AI增强模块

#### 5.2.1 AI服务客户端
```typescript
// AI服务客户端
class AIServiceClient {
  private baseURL: string;
  private apiKey: string;
  private cache = new Map<string, any>();
  
  constructor(baseURL: string, apiKey: string) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
  }
  
  async enhanceContent(content: string, options: AIEnhanceOptions = {}): Promise<AIEnhanceResult> {
    const cacheKey = this.generateCacheKey(content, options);
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      const response = await this.makeRequest('/api/v1/enhance-content', {
        content,
        options: {
          improve_readability: options.improveReadability ?? true,
          fix_formatting: options.fixFormatting ?? true,
          enhance_structure: options.enhanceStructure ?? true,
          language: options.language ?? 'auto'
        }
      });
      
      const result = await response.json();
      
      // 缓存结果
      this.cache.set(cacheKey, result);
      
      return result;
    } catch (error) {
      console.error('AI enhancement failed:', error);
      throw new AIServiceError('Content enhancement failed', error);
    }
  }
  
  async summarizeContent(content: string, maxLength: number = 200): Promise<string> {
    const response = await this.makeRequest('/api/v1/summarize', {
      content,
      max_length: maxLength
    });
    
    const result = await response.json();
    return result.summary;
  }
  
  async translateContent(content: string, targetLanguage: string): Promise<string> {
    const response = await this.makeRequest('/api/v1/translate', {
      content,
      target_language: targetLanguage
    });
    
    const result = await response.json();
    return result.translated_content;
  }
  
  private async makeRequest(endpoint: string, data: any): Promise<Response> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Client-Version': '1.0.0'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response;
  }
  
  private generateCacheKey(content: string, options: AIEnhanceOptions): string {
    const hash = this.simpleHash(content + JSON.stringify(options));
    return `ai_enhance_${hash}`;
  }
  
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// AI增强选项接口
interface AIEnhanceOptions {
  improveReadability?: boolean;
  fixFormatting?: boolean;
  enhanceStructure?: boolean;
  language?: string;
}

// AI增强结果接口
interface AIEnhanceResult {
  enhanced_content: string;
  analysis: {
    readability_score: number;
    structure_quality: number;
    content_type: string;
    improvements_made: string[];
  };
  processing_time: number;
  confidence: number;
}

// AI服务错误类
class AIServiceError extends Error {
  constructor(message: string, public cause?: any) {
    super(message);
    this.name = 'AIServiceError';
  }
}
```

#### 5.2.2 本地AI处理
```typescript
// 本地AI处理器（用于离线场景）
class LocalAIProcessor {
  private models = new Map<string, any>();
  private isInitialized = false;
  
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // 加载轻量级模型
      await this.loadModels();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize local AI processor:', error);
      throw error;
    }
  }
  
  private async loadModels(): Promise<void> {
    // 加载文本分类模型
    const classificationModel = await this.loadModel('text-classification');
    this.models.set('classification', classificationModel);
    
    // 加载可读性分析模型
    const readabilityModel = await this.loadModel('readability-analysis');
    this.models.set('readability', readabilityModel);
  }
  
  private async loadModel(modelName: string): Promise<any> {
    // 这里可以集成TensorFlow.js或其他轻量级ML库
    // 示例使用简化的规则引擎
    switch (modelName) {
      case 'text-classification':
        return new TextClassificationModel();
      case 'readability-analysis':
        return new ReadabilityAnalysisModel();
      default:
        throw new Error(`Unknown model: ${modelName}`);
    }
  }
  
  async enhanceContent(content: string): Promise<LocalAIResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    const analysis = await this.analyzeContent(content);
    const enhanced = await this.applyEnhancements(content, analysis);
    
    return {
      enhanced_content: enhanced,
      analysis,
      confidence: analysis.confidence
    };
  }
  
  private async analyzeContent(content: string): Promise<ContentAnalysis> {
    const classificationModel = this.models.get('classification');
    const readabilityModel = this.models.get('readability');
    
    const contentType = await classificationModel.classify(content);
    const readabilityScore = await readabilityModel.analyze(content);
    
    return {
      content_type: contentType,
      readability_score: readabilityScore,
      structure_quality: this.analyzeStructure(content),
      confidence: 0.8 // 本地模型置信度较低
    };
  }
  
  private analyzeStructure(content: string): number {
    // 简单的结构质量分析
    const paragraphs = content.split('\n\n').length;
    const sentences = content.split(/[.!?]+/).length;
    const avgSentencesPerParagraph = sentences / paragraphs;
    
    // 理想的段落应该有3-5个句子
    const idealRange = avgSentencesPerParagraph >= 3 && avgSentencesPerParagraph <= 5;
    return idealRange ? 0.8 : 0.6;
  }
  
  private async applyEnhancements(content: string, analysis: ContentAnalysis): Promise<string> {
    let enhanced = content;
    
    // 基于分析结果应用增强
    if (analysis.readability_score < 0.6) {
      enhanced = this.improveReadability(enhanced);
    }
    
    if (analysis.structure_quality < 0.7) {
      enhanced = this.improveStructure(enhanced);
    }
    
    return enhanced;
  }
  
  private improveReadability(content: string): string {
    // 简化长句子
    return content.replace(/([^.!?]{100,}?)[,;](\s+)/g, '$1.$2');
  }
  
  private improveStructure(content: string): string {
    // 改善段落结构
    const paragraphs = content.split('\n\n');
    return paragraphs.map(p => {
      if (p.length > 500) {
        // 分割长段落
        const sentences = p.split(/([.!?]+\s+)/);
        const midPoint = Math.floor(sentences.length / 2);
        return sentences.slice(0, midPoint).join('') + '\n\n' + 
               sentences.slice(midPoint).join('');
      }
      return p;
    }).join('\n\n');
  }
}

// 本地AI结果接口
interface LocalAIResult {
  enhanced_content: string;
  analysis: ContentAnalysis;
  confidence: number;
}

interface ContentAnalysis {
  content_type: string;
  readability_score: number;
  structure_quality: number;
  confidence: number;
}
```

### 5.3 用户界面模块

#### 5.3.1 UI渲染引擎
```typescript
// UI渲染引擎
class UIRenderer {
  private container: HTMLElement;
  private shadowRoot: ShadowRoot;
  private currentTheme: Theme;
  private eventManager: EventManager;
  
  constructor() {
    this.createContainer();
    this.setupShadowDOM();
    this.eventManager = new EventManager();
    this.currentTheme = new DefaultTheme();
  }
  
  private createContainer(): void {
    this.container = document.createElement('div');
    this.container.id = 'reading-assistant-container';
    this.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2147483647;
      pointer-events: none;
    `;
  }
  
  private setupShadowDOM(): void {
    this.shadowRoot = this.container.attachShadow({ mode: 'closed' });
    
    // 注入样式
    const styleSheet = document.createElement('style');
    styleSheet.textContent = this.getBaseStyles();
    this.shadowRoot.appendChild(styleSheet);
  }
  
  render(content: ExtractedContent, settings: UserSettings): void {
    const readerElement = this.createReaderElement(content, settings);
    
    // 清空现有内容
    while (this.shadowRoot.children.length > 1) {
      this.shadowRoot.removeChild(this.shadowRoot.lastChild!);
    }
    
    this.shadowRoot.appendChild(readerElement);
    
    if (!document.body.contains(this.container)) {
      document.body.appendChild(this.container);
    }
    
    // 启用指针事件
    this.container.style.pointerEvents = 'auto';
    
    // 应用主题
    this.applyTheme(settings.theme);
    
    // 绑定事件
    this.bindEvents(readerElement);
    
    // 触发渲染完成事件
    this.eventManager.emit('render:complete', { content, settings });
  }
  
  private createReaderElement(content: ExtractedContent, settings: UserSettings): HTMLElement {
    const reader = document.createElement('div');
    reader.className = 'reading-assistant-reader';
    
    // 创建头部
    const header = this.createHeader(content.title);
    reader.appendChild(header);
    
    // 创建内容区域
    const contentArea = this.createContentArea(content.content, settings);
    reader.appendChild(contentArea);
    
    // 创建控制栏
    const controls = this.createControls(settings);
    reader.appendChild(controls);
    
    return reader;
  }
  
  private createHeader(title: string): HTMLElement {
    const header = document.createElement('header');
    header.className = 'reader-header';
    
    const titleElement = document.createElement('h1');
    titleElement.className = 'reader-title';
    titleElement.textContent = title;
    
    const closeButton = document.createElement('button');
    closeButton.className = 'reader-close';
    closeButton.innerHTML = '×';
    closeButton.setAttribute('aria-label', '关闭阅读器');
    closeButton.addEventListener('click', () => this.close());
    
    header.appendChild(titleElement);
    header.appendChild(closeButton);
    
    return header;
  }
  
  private createContentArea(content: string, settings: UserSettings): HTMLElement {
    const contentArea = document.createElement('main');
    contentArea.className = 'reader-content';
    contentArea.setAttribute('role', 'main');
    
    // 应用用户设置
    this.applyContentSettings(contentArea, settings);
    
    // 设置内容
    contentArea.innerHTML = content;
    
    // 处理图片懒加载
    this.setupLazyLoading(contentArea);
    
    return contentArea;
  }
  
  private createControls(settings: UserSettings): HTMLElement {
    const controls = document.createElement('aside');
    controls.className = 'reader-controls';
    controls.setAttribute('role', 'complementary');
    
    // 字体大小控制
    const fontSizeControl = this.createFontSizeControl(settings.fontSize);
    controls.appendChild(fontSizeControl);
    
    // 主题切换
    const themeControl = this.createThemeControl(settings.theme);
    controls.appendChild(themeControl);
    
    // 宽度控制
    const widthControl = this.createWidthControl(settings.contentWidth);
    controls.appendChild(widthControl);
    
    return controls;
  }
  
  private applyContentSettings(element: HTMLElement, settings: UserSettings): void {
    element.style.fontSize = `${settings.fontSize}px`;
    element.style.lineHeight = settings.lineHeight.toString();
    element.style.maxWidth = `${settings.contentWidth}ch`;
    element.style.fontFamily = settings.fontFamily;
  }
  
  private applyTheme(theme: string): void {
    const themeClass = `theme-${theme}`;
    this.shadowRoot.host.className = themeClass;
  }
  
  private bindEvents(element: HTMLElement): void {
    // 键盘事件
    document.addEventListener('keydown', this.handleKeydown.bind(this));
    
    // 滚动事件
    element.addEventListener('scroll', this.handleScroll.bind(this));
    
    // 点击事件
    element.addEventListener('click', this.handleClick.bind(this));
  }
  
  private handleKeydown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Escape':
        this.close();
        break;
      case '+':
      case '=':
        if (event.ctrlKey || event.metaKey) {
          this.increaseFontSize();
          event.preventDefault();
        }
        break;
      case '-':
        if (event.ctrlKey || event.metaKey) {
          this.decreaseFontSize();
          event.preventDefault();
        }
        break;
    }
  }
  
  close(): void {
    this.container.style.pointerEvents = 'none';
    this.container.style.opacity = '0';
    
    setTimeout(() => {
      if (this.container.parentNode) {
        this.container.parentNode.removeChild(this.container);
      }
    }, 300);
    
    this.eventManager.emit('reader:close');
  }
  
  private getBaseStyles(): string {
    return `
      .reading-assistant-reader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--bg-primary, #ffffff);
        color: var(--text-primary, #1f2937);
        font-family: var(--font-family, system-ui, sans-serif);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: fadeIn 0.3s ease-out;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .reader-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        border-bottom: 1px solid var(--border-color, #e5e7eb);
        background: var(--bg-secondary, #f9fafb);
      }
      
      .reader-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary, #1f2937);
      }
      
      .reader-close {
        background: none;
        border: none;
        font-size: 2rem;
        cursor: pointer;
        color: var(--text-secondary, #6b7280);
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
      }
      
      .reader-close:hover {
        background: var(--bg-tertiary, #f3f4f6);
        color: var(--text-primary, #1f2937);
      }
      
      .reader-content {
        flex: 1;
        padding: 2rem;
        overflow-y: auto;
        line-height: 1.7;
        max-width: 65ch;
        margin: 0 auto;
        width: 100%;
      }
      
      .reader-controls {
        position: fixed;
        top: 50%;
        right: 2rem;
        transform: translateY(-50%);
        background: var(--bg-primary, #ffffff);
        border: 1px solid var(--border-color, #e5e7eb);
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      }
      
      /* 主题样式 */
      .theme-dark {
        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --bg-tertiary: #4b5563;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --border-color: #4b5563;
      }
      
      .theme-sepia {
        --bg-primary: #f7f3e9;
        --bg-secondary: #f0e6d2;
        --bg-tertiary: #e8dcc0;
        --text-primary: #5d4e37;
        --text-secondary: #8b7355;
        --border-color: #d4c4a8;
      }
      
      /* 响应式设计 */
      @media (max-width: 768px) {
        .reader-header {
          padding: 1rem;
        }
        
        .reader-content {
          padding: 1rem;
          max-width: none;
        }
        
        .reader-controls {
          position: static;
          transform: none;
          margin: 1rem;
          right: auto;
          top: auto;
        }
      }
    `;
  }
}
```

### 5.4 设置管理模块

#### 5.4.1 设置管理器
```typescript
// 设置管理器
class SettingsManager {
  private settings: UserSettings;
  private storage: StorageAdapter;
  private eventEmitter: EventEmitter;
  private syncService: SyncService;
  
  constructor(storage: StorageAdapter, syncService?: SyncService) {
    this.storage = storage;
    this.syncService = syncService;
    this.eventEmitter = new EventEmitter();
    this.settings = this.getDefaultSettings();
  }
  
  async initialize(): Promise<void> {
    try {
      // 从本地存储加载设置
      const localSettings = await this.storage.get('userSettings');
      if (localSettings) {
        this.settings = { ...this.settings, ...localSettings };
      }
      
      // 如果有同步服务，尝试同步云端设置
      if (this.syncService) {
        await this.syncFromCloud();
      }
      
      this.eventEmitter.emit('settings:loaded', this.settings);
    } catch (error) {
      console.error('Failed to initialize settings:', error);
      // 使用默认设置
    }
  }
  
  getSettings(): UserSettings {
    return { ...this.settings };
  }
  
  async updateSetting<K extends keyof UserSettings>(
    key: K,
    value: UserSettings[K]
  ): Promise<void> {
    const oldValue = this.settings[key];
    this.settings[key] = value;
    
    try {
      // 保存到本地存储
      await this.storage.set('userSettings', this.settings);
      
      // 同步到云端
      if (this.syncService) {
        await this.syncService.updateSetting(key, value);
      }
      
      // 触发变更事件
      this.eventEmitter.emit('setting:changed', {
        key,
        oldValue,
        newValue: value
      });
      
      this.eventEmitter.emit(`setting:${key}:changed`, value);
      
    } catch (error) {
      // 回滚设置
      this.settings[key] = oldValue;
      throw new SettingsError(`Failed to update setting ${key}`, error);
    }
  }
  
  async updateSettings(newSettings: Partial<UserSettings>): Promise<void> {
    const oldSettings = { ...this.settings };
    
    try {
      // 批量更新设置
      Object.assign(this.settings, newSettings);
      
      // 验证设置
      this.validateSettings(this.settings);
      
      // 保存到本地存储
      await this.storage.set('userSettings', this.settings);
      
      // 同步到云端
      if (this.syncService) {
        await this.syncService.updateSettings(newSettings);
      }
      
      // 触发变更事件
      this.eventEmitter.emit('settings:changed', {
        oldSettings,
        newSettings: this.settings
      });
      
    } catch (error) {
      // 回滚设置
      this.settings = oldSettings;
      throw new SettingsError('Failed to update settings', error);
    }
  }
  
  async resetSettings(): Promise<void> {
    const defaultSettings = this.getDefaultSettings();
    await this.updateSettings(defaultSettings);
  }
  
  async exportSettings(): Promise<string> {
    return JSON.stringify(this.settings, null, 2);
  }
  
  async importSettings(settingsJson: string): Promise<void> {
    try {
      const importedSettings = JSON.parse(settingsJson);
      this.validateSettings(importedSettings);
      await this.updateSettings(importedSettings);
    } catch (error) {
      throw new SettingsError('Invalid settings format', error);
    }
  }
  
  private async syncFromCloud(): Promise<void> {
    try {
      const cloudSettings = await this.syncService.getSettings();
      if (cloudSettings) {
        // 合并云端设置，本地设置优先
        const mergedSettings = { ...cloudSettings, ...this.settings };
        this.settings = mergedSettings;
        
        // 保存合并后的设置到本地
        await this.storage.set('userSettings', this.settings);
      }
    } catch (error) {
      console.warn('Failed to sync settings from cloud:', error);
    }
  }
  
  private validateSettings(settings: any): void {
    const schema = {
      fontSize: { type: 'number', min: 12, max: 24 },
      lineHeight: { type: 'number', min: 1.2, max: 2.0 },
      contentWidth: { type: 'number', min: 40, max: 100 },
      theme: { type: 'string', enum: ['light', 'dark', 'sepia'] },
      fontFamily: { type: 'string' },
      enableAI: { type: 'boolean' },
      autoExtract: { type: 'boolean' }
    };
    
    for (const [key, rules] of Object.entries(schema)) {
      const value = settings[key];
      
      if (value === undefined) continue;
      
      if (typeof value !== rules.type) {
        throw new Error(`Invalid type for ${key}: expected ${rules.type}`);
      }
      
      if (rules.min !== undefined && value < rules.min) {
        throw new Error(`Value for ${key} is below minimum: ${rules.min}`);
      }
      
      if (rules.max !== undefined && value > rules.max) {
        throw new Error(`Value for ${key} is above maximum: ${rules.max}`);
      }
      
      if (rules.enum && !rules.enum.includes(value)) {
        throw new Error(`Invalid value for ${key}: must be one of ${rules.enum.join(', ')}`);
      }
    }
  }
  
  private getDefaultSettings(): UserSettings {
    return {
      fontSize: 16,
      lineHeight: 1.6,
      contentWidth: 65,
      theme: 'light',
      fontFamily: 'system-ui, sans-serif',
      enableAI: true,
      autoExtract: true,
      keyboardShortcuts: true,
      showProgress: true,
      enableSync: false,
      language: 'zh-CN'
    };
  }
  
  // 事件监听
  on(event: string, listener: Function): void {
    this.eventEmitter.on(event, listener);
  }
  
  off(event: string, listener: Function): void {
    this.eventEmitter.off(event, listener);
  }
}

// 用户设置接口
interface UserSettings {
  fontSize: number;
  lineHeight: number;
  contentWidth: number;
  theme: 'light' | 'dark' | 'sepia';
  fontFamily: string;
  enableAI: boolean;
  autoExtract: boolean;
  keyboardShortcuts: boolean;
  showProgress: boolean;
  enableSync: boolean;
  language: string;
}

// 设置错误类
class SettingsError extends Error {
  constructor(message: string, public cause?: any) {
    super(message);
    this.name = 'SettingsError';
  }
}

// 存储适配器接口
interface StorageAdapter {
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
}

// 浏览器存储适配器
class BrowserStorageAdapter implements StorageAdapter {
  async get(key: string): Promise<any> {
    const browserAPI = BrowserAPI.getInstance();
    return browserAPI.getStorage(key);
  }
  
  async set(key: string, value: any): Promise<void> {
    const browserAPI = BrowserAPI.getInstance();
    return browserAPI.setStorage(key, value);
  }
  
  async remove(key: string): Promise<void> {
    const browserAPI = BrowserAPI.getInstance();
    return browserAPI.removeStorage(key);
  }
  
  async clear(): Promise<void> {
    const browserAPI = BrowserAPI.getInstance();
    return browserAPI.clearStorage();
  }
}
```

## 6. 数据架构

### 6.1 数据模型设计

#### 6.1.1 核心数据模型
```typescript
// 用户数据模型
interface User {
  id: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  subscriptionTier: 'free' | 'premium' | 'enterprise';
  preferences: UserPreferences;
}

interface UserPreferences {
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

// 内容数据模型
interface ExtractedContent {
  id: string;
  url: string;
  urlHash: string;
  title: string;
  content: string;
  originalContent?: string;
  aiEnhancedContent?: string;
  metadata: ContentMetadata;
  extractedAt: Date;
  confidence: number;
  extractionMethod: 'basic' | 'ai' | 'manual';
}

interface ContentMetadata {
  author?: string;
  publishDate?: Date;
  wordCount: number;
  readingTime: number;
  language: string;
  contentType: 'article' | 'blog' | 'news' | 'documentation' | 'other';
  tags: string[];
  images: ImageMetadata[];
}

interface ImageMetadata {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  caption?: string;
}

// 用户活动数据模型
interface UserActivity {
  id: string;
  userId: string;
  activityType: ActivityType;
  activityData: any;
  timestamp: Date;
  sessionId: string;
}

type ActivityType = 
  | 'content_extracted'
  | 'content_enhanced'
  | 'settings_changed'
  | 'theme_switched'
  | 'reading_started'
  | 'reading_completed'
  | 'error_occurred';

// 缓存数据模型
interface CacheEntry {
  key: string;
  value: any;
  createdAt: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
  size: number;
}
```

#### 6.1.2 数据关系图
```mermaid
erDiagram
    User ||--o{ UserActivity : has
    User ||--|| UserSettings : has
    User ||--o{ ContentCache : owns
    
    User {
        string id PK
        string email UK
        timestamp created_at
        timestamp updated_at
        boolean is_active
        string subscription_tier
    }
    
    UserSettings {
        string id PK
        string user_id FK
        jsonb settings
        timestamp created_at
        timestamp updated_at
    }
    
    ContentCache {
        string id PK
        string url_hash UK
        text original_content
        jsonb extracted_content
        jsonb ai_enhanced_content
        jsonb metadata
        timestamp created_at
        timestamp expires_at
        integer access_count
    }
    
    UserActivity {
        string id PK
        string user_id FK
        string activity_type
        jsonb activity_data
        timestamp created_at
        string session_id
    }
    
    SyncRecord {
        string id PK
        string user_id FK
        string data_type
        jsonb data
        timestamp synced_at
        string device_id
    }
```

### 6.2 存储策略

#### 6.2.1 本地存储架构
```typescript
// 本地存储管理器
class LocalStorageManager {
  private dbName = 'ReadingAssistantDB';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;
  
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建对象存储
        this.createObjectStores(db);
      };
    });
  }
  
  private createObjectStores(db: IDBDatabase): void {
    // 用户设置存储
    if (!db.objectStoreNames.contains('settings')) {
      const settingsStore = db.createObjectStore('settings', { keyPath: 'id' });
      settingsStore.createIndex('userId', 'userId', { unique: false });
    }
    
    // 内容缓存存储
    if (!db.objectStoreNames.contains('contentCache')) {
      const cacheStore = db.createObjectStore('contentCache', { keyPath: 'id' });
      cacheStore.createIndex('urlHash', 'urlHash', { unique: true });
      cacheStore.createIndex('expiresAt', 'expiresAt', { unique: false });
    }
    
    // 用户活动存储
    if (!db.objectStoreNames.contains('activities')) {
      const activityStore = db.createObjectStore('activities', { keyPath: 'id' });
      activityStore.createIndex('userId', 'userId', { unique: false });
      activityStore.createIndex('timestamp', 'timestamp', { unique: false });
    }
    
    // 离线队列存储
    if (!db.objectStoreNames.contains('offlineQueue')) {
      const queueStore = db.createObjectStore('offlineQueue', { keyPath: 'id' });
      queueStore.createIndex('priority', 'priority', { unique: false });
    }
  }
  
  async storeContent(content: ExtractedContent): Promise<void> {
    const transaction = this.db!.transaction(['contentCache'], 'readwrite');
    const store = transaction.objectStore('contentCache');
    
    const cacheEntry: CacheEntry = {
      id: content.id,
      urlHash: content.urlHash,
      content: content,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天过期
      accessCount: 0,
      lastAccessed: new Date(),
      size: JSON.stringify(content).length
    };
    
    await store.put(cacheEntry);
  }
  
  async getContent(urlHash: string): Promise<ExtractedContent | null> {
    const transaction = this.db!.transaction(['contentCache'], 'readwrite');
    const store = transaction.objectStore('contentCache');
    const index = store.index('urlHash');
    
    const request = index.get(urlHash);
    const result = await new Promise<CacheEntry>((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
    
    if (result && result.expiresAt > new Date()) {
      // 更新访问统计
      result.accessCount++;
      result.lastAccessed = new Date();
      await store.put(result);
      
      return result.content;
    }
    
    return null;
  }
  
  async cleanExpiredCache(): Promise<void> {
    const transaction = this.db!.transaction(['contentCache'], 'readwrite');
    const store = transaction.objectStore('contentCache');
    const index = store.index('expiresAt');
    
    const range = IDBKeyRange.upperBound(new Date());
    const request = index.openCursor(range);
    
    request.onsuccess = (event) => {
      const cursor = (event.target as IDBRequest).result;
      if (cursor) {
        cursor.delete();
        cursor.continue();
      }
    };
  }
}

// 存储配额管理
class StorageQuotaManager {
  private maxCacheSize = 50 * 1024 * 1024; // 50MB
  private maxCacheEntries = 1000;
  
  async checkQuota(): Promise<StorageQuota> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        available: estimate.quota || 0,
        percentage: ((estimate.usage || 0) / (estimate.quota || 1)) * 100
      };
    }
    
    return { used: 0, available: 0, percentage: 0 };
  }
  
  async enforceQuota(storageManager: LocalStorageManager): Promise<void> {
    const quota = await this.checkQuota();
    
    if (quota.percentage > 80) {
      await this.cleanupOldEntries(storageManager);
    }
  }
  
  private async cleanupOldEntries(storageManager: LocalStorageManager): Promise<void> {
    // 清理最少使用的缓存条目
    // 实现LRU清理策略
  }
}

interface StorageQuota {
  used: number;
  available: number;
  percentage: number;
}
```

#### 6.2.2 云端存储架构
```typescript
// 云端同步服务
class CloudSyncService {
  private apiClient: APIClient;
  private conflictResolver: ConflictResolver;
  private syncQueue: SyncQueue;
  
  constructor(apiClient: APIClient) {
    this.apiClient = apiClient;
    this.conflictResolver = new ConflictResolver();
    this.syncQueue = new SyncQueue();
  }
  
  async syncSettings(localSettings: UserSettings): Promise<SyncResult> {
    try {
      // 获取云端设置
      const cloudSettings = await this.apiClient.getSettings();
      
      // 检测冲突
      const conflicts = this.detectConflicts(localSettings, cloudSettings);
      
      if (conflicts.length > 0) {
        // 解决冲突
        const resolved = await this.conflictResolver.resolve(conflicts);
        return { status: 'conflict_resolved', data: resolved };
      }
      
      // 合并设置
      const mergedSettings = this.mergeSettings(localSettings, cloudSettings);
      
      // 上传到云端
      await this.apiClient.updateSettings(mergedSettings);
      
      return { status: 'success', data: mergedSettings };
      
    } catch (error) {
      // 添加到同步队列，稍后重试
      this.syncQueue.add({
        type: 'settings',
        data: localSettings,
        timestamp: new Date(),
        retryCount: 0
      });
      
      return { status: 'error', error };
    }
  }
  
  async syncUserActivity(activities: UserActivity[]): Promise<void> {
    const batchSize = 100;
    
    for (let i = 0; i < activities.length; i += batchSize) {
      const batch = activities.slice(i, i + batchSize);
      
      try {
        await this.apiClient.uploadActivities(batch);
      } catch (error) {
        // 添加失败的批次到队列
        this.syncQueue.add({
          type: 'activities',
          data: batch,
          timestamp: new Date(),
          retryCount: 0
        });
      }
    }
  }
  
  private detectConflicts(local: UserSettings, cloud: UserSettings): Conflict[] {
    const conflicts: Conflict[] = [];
    
    for (const key in local) {
      if (local[key] !== cloud[key]) {
        conflicts.push({
          field: key,
          localValue: local[key],
          cloudValue: cloud[key],
          timestamp: new Date()
        });
      }
    }
    
    return conflicts;
  }
  
  private mergeSettings(local: UserSettings, cloud: UserSettings): UserSettings {
    // 实现智能合并策略
    // 优先使用最近修改的设置
    return { ...cloud, ...local };
  }
}

// 冲突解决器
class ConflictResolver {
  async resolve(conflicts: Conflict[]): Promise<UserSettings> {
    // 实现冲突解决策略
    // 1. 用户手动选择
    // 2. 基于时间戳的自动解决
    // 3. 基于优先级的解决
    
    const resolved: Partial<UserSettings> = {};
    
    for (const conflict of conflicts) {
      // 简单策略：使用本地值
      resolved[conflict.field] = conflict.localValue;
    }
    
    return resolved as UserSettings;
  }
}

interface Conflict {
  field: string;
  localValue: any;
  cloudValue: any;
  timestamp: Date;
}

interface SyncResult {
  status: 'success' | 'conflict_resolved' | 'error';
  data?: any;
  error?: any;
}
```

### 6.3 缓存机制

#### 6.3.1 多层缓存架构
```typescript
// 缓存管理器
class CacheManager {
  private memoryCache: MemoryCache;
  private localCache: LocalCache;
  private distributedCache: DistributedCache;
  
  constructor() {
    this.memoryCache = new MemoryCache(10 * 1024 * 1024); // 10MB内存缓存
    this.localCache = new LocalCache();
    this.distributedCache = new DistributedCache();
  }
  
  async get<T>(key: string): Promise<T | null> {
    // L1: 内存缓存
    let result = await this.memoryCache.get<T>(key);
    if (result) {
      return result;
    }
    
    // L2: 本地存储缓存
    result = await this.localCache.get<T>(key);
    if (result) {
      // 回填内存缓存
      await this.memoryCache.set(key, result);
      return result;
    }
    
    // L3: 分布式缓存
    result = await this.distributedCache.get<T>(key);
    if (result) {
      // 回填本地缓存
      await this.localCache.set(key, result);
      await this.memoryCache.set(key, result);
      return result;
    }
    
    return null;
  }
  
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    // 写入所有缓存层
    await Promise.all([
      this.memoryCache.set(key, value, ttl),
      this.localCache.set(key, value, ttl),
      this.distributedCache.set(key, value, ttl)
    ]);
  }
  
  async invalidate(key: string): Promise<void> {
    await Promise.all([
      this.memoryCache.delete(key),
      this.localCache.delete(key),
      this.distributedCache.delete(key)
    ]);
  }
  
  async clear(): Promise<void> {
    await Promise.all([
      this.memoryCache.clear(),
      this.localCache.clear(),
      this.distributedCache.clear()
    ]);
  }
}

// 内存缓存实现
class MemoryCache {
  private cache = new Map<string, CacheItem>();
  private maxSize: number;
  private currentSize = 0;
  
  constructor(maxSize: number) {
    this.maxSize = maxSize;
  }
  
  async get<T>(key: string): Promise<T | null> {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (item.expiresAt && item.expiresAt < Date.now()) {
      this.cache.delete(key);
      this.currentSize -= item.size;
      return null;
    }
    
    // 更新访问时间（LRU）
    item.lastAccessed = Date.now();
    
    return item.value as T;
  }
  
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const size = this.calculateSize(value);
    
    // 检查是否需要清理空间
    if (this.currentSize + size > this.maxSize) {
      await this.evictLRU(size);
    }
    
    const item: CacheItem = {
      value,
      size,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      expiresAt: ttl ? Date.now() + ttl : undefined
    };
    
    this.cache.set(key, item);
    this.currentSize += size;
  }
  
  private async evictLRU(requiredSpace: number): Promise<void> {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
    
    let freedSpace = 0;
    
    for (const [key, item] of entries) {
      this.cache.delete(key);
      this.currentSize -= item.size;
      freedSpace += item.size;
      
      if (freedSpace >= requiredSpace) break;
    }
  }
  
  private calculateSize(value: any): number {
    return JSON.stringify(value).length * 2; // 粗略估算
  }
}

interface CacheItem {
  value: any;
  size: number;
  createdAt: number;
  lastAccessed: number;
  expiresAt?: number;
}
```

## 7. API设计

### 7.1 RESTful API规范

#### 7.1.1 API接口设计
```typescript
// API路由定义
const API_ROUTES = {
  // 认证相关
  auth: {
    login: 'POST /api/v1/auth/login',
    logout: 'POST /api/v1/auth/logout',
    refresh: 'POST /api/v1/auth/refresh',
    register: 'POST /api/v1/auth/register'
  },
  
  // 用户相关
  users: {
    profile: 'GET /api/v1/users/profile',
    updateProfile: 'PUT /api/v1/users/profile',
    settings: 'GET /api/v1/users/settings',
    updateSettings: 'PUT /api/v1/users/settings'
  },
  
  // 内容相关
  content: {
    extract: 'POST /api/v1/content/extract',
    enhance: 'POST /api/v1/content/enhance',
    cache: 'GET /api/v1/content/cache/:hash',
    analyze: 'POST /api/v1/content/analyze'
  },
  
  // AI服务
  ai: {
    enhance: 'POST /api/v1/ai/enhance',
    summarize: 'POST /api/v1/ai/summarize',
    translate: 'POST /api/v1/ai/translate',
    classify: 'POST /api/v1/ai/classify'
  },
  
  // 同步服务
  sync: {
    settings: 'POST /api/v1/sync/settings',
    activities: 'POST /api/v1/sync/activities',
    status: 'GET /api/v1/sync/status'
  }
} as const;

// API客户端
class APIClient {
  private baseURL: string;
  private authToken: string | null = null;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  
  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.setupDefaultInterceptors();
  }
  
  // 认证API
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.post('/api/v1/auth/login', credentials);
    
    if (response.token) {
      this.setAuthToken(response.token);
    }
    
    return response;
  }
  
  async logout(): Promise<void> {
    await this.post('/api/v1/auth/logout');
    this.setAuthToken(null);
  }
  
  // 内容API
  async extractContent(request: ContentExtractionRequest): Promise<ExtractedContent> {
    return this.post('/api/v1/content/extract', request);
  }
  
  async enhanceContent(request: ContentEnhancementRequest): Promise<EnhancedContent> {
    return this.post('/api/v1/ai/enhance', request);
  }
  
  async getCachedContent(urlHash: string): Promise<CachedContent | null> {
    try {
      return await this.get(`/api/v1/content/cache/${urlHash}`);
    } catch (error) {
      if (error.status === 404) return null;
      throw error;
    }
  }
  
  // 用户设置API
  async getSettings(): Promise<UserSettings> {
    return this.get('/api/v1/users/settings');
  }
  
  async updateSettings(settings: Partial<UserSettings>): Promise<UserSettings> {
    return this.put('/api/v1/users/settings', settings);
  }
  
  // 同步API
  async syncSettings(settings: UserSettings): Promise<SyncResponse> {
    return this.post('/api/v1/sync/settings', { settings });
  }
  
  async uploadActivities(activities: UserActivity[]): Promise<void> {
    await this.post('/api/v1/sync/activities', { activities });
  }
  
  // 基础HTTP方法
  private async get<T>(endpoint: string): Promise<T> {
    return this.request('GET', endpoint);
  }
  
  private async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request('POST', endpoint, data);
  }
  
  private async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request('PUT', endpoint, data);
  }
  
  private async delete<T>(endpoint: string): Promise<T> {
    return this.request('DELETE', endpoint);
  }
  
  private async request<T>(method: string, endpoint: string, data?: any): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    let config: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '1.0.0'
      }
    };
    
    if (this.authToken) {
      config.headers['Authorization'] = `Bearer ${this.authToken}`;
    }
    
    if (data) {
      config.body = JSON.stringify(data);
    }
    
    // 应用请求拦截器
    for (const interceptor of this.requestInterceptors) {
      config = await interceptor(config);
    }
    
    let response = await fetch(url, config);
    
    // 应用响应拦截器
    for (const interceptor of this.responseInterceptors) {
      response = await interceptor(response);
    }
    
    if (!response.ok) {
      throw new APIError(response.status, response.statusText, await response.text());
    }
    
    return response.json();
  }
  
  private setupDefaultInterceptors(): void {
    // 请求重试拦截器
    this.addRequestInterceptor(async (config) => {
      config.retryCount = config.retryCount || 0;
      return config;
    });
    
    // 响应错误处理拦截器
    this.addResponseInterceptor(async (response) => {
      if (response.status === 401) {
        // Token过期，尝试刷新
        await this.refreshToken();
        // 重新发起请求
        // ... 实现重试逻辑
      }
      
      return response;
    });
  }
  
  private async refreshToken(): Promise<void> {
    // 实现token刷新逻辑
  }
  
  setAuthToken(token: string | null): void {
    this.authToken = token;
  }
  
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }
  
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }
}

// API错误类
class APIError extends Error {
  constructor(
    public status: number,
    public statusText: string,
    public body: string
  ) {
    super(`API Error ${status}: ${statusText}`);
    this.name = 'APIError';
  }
}

// 类型定义
interface LoginCredentials {
  email: string;
  password: string;
}

interface AuthResponse {
  token: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

interface ContentExtractionRequest {
  url: string;
  html?: string;
  options?: {
    useAI?: boolean;
    language?: string;
  };
}

interface ContentEnhancementRequest {
  content: string;
  options?: {
    improveReadability?: boolean;
    fixFormatting?: boolean;
    enhanceStructure?: boolean;
  };
}

type RequestInterceptor = (config: RequestInit) => Promise<RequestInit>;
type ResponseInterceptor = (response: Response) => Promise<Response>;
```

#### 7.1.2 API文档生成
```yaml
# OpenAPI 3.0 规范
openapi: 3.0.3
info:
  title: Reading Assistant API
  description: 阅读助手插件后端API
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: https://api.readingassistant.com/v1
    description: 生产环境
  - url: https://staging-api.readingassistant.com/v1
    description: 测试环境

paths:
  /auth/login:
    post:
      summary: 用户登录
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  minLength: 8
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /content/extract:
    post:
      summary: 提取网页内容
      tags: [Content]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContentExtractionRequest'
      responses:
        '200':
          description: 内容提取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtractedContent'
        '400':
          description: 请求参数错误
        '429':
          description: 请求频率限制

  /ai/enhance:
    post:
      summary: AI内容增强
      tags: [AI]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContentEnhancementRequest'
      responses:
        '200':
          description: 内容增强成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnhancedContent'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AuthResponse:
      type: object
      properties:
        token:
          type: string
          description: JWT访问令牌
        refreshToken:
          type: string
          description: 刷新令牌
        user:
          $ref: '#/components/schemas/User'
        expiresIn:
          type: integer
          description: 令牌过期时间（秒）

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        createdAt:
          type: string
          format: date-time
        subscriptionTier:
          type: string
          enum: [free, premium, enterprise]

    ContentExtractionRequest:
      type: object
      required: [url]
      properties:
        url:
          type: string
          format: uri
          description: 要提取内容的网页URL
        html:
          type: string
          description: 可选的HTML内容
        options:
          type: object
          properties:
            useAI:
              type: boolean
              default: true
            language:
              type: string
              default: auto

    ExtractedContent:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        content:
          type: string
        metadata:
          $ref: '#/components/schemas/ContentMetadata'
        confidence:
          type: number
          minimum: 0
          maximum: 1

    Error:
      type: object
      properties:
        error:
          type: string
          description: 错误类型
        message:
          type: string
          description: 错误描述
        details:
          type: object
          description: 详细错误信息
```

### 7.2 认证授权

#### 7.2.1 JWT认证实现
```typescript
// JWT认证服务
class AuthService {
  private jwtSecret: string;
  private refreshTokenSecret: string;
  private tokenExpiry = '15m';
  private refreshTokenExpiry = '7d';
  
  constructor(jwtSecret: string, refreshTokenSecret: string) {
    this.jwtSecret = jwtSecret;
    this.refreshTokenSecret = refreshTokenSecret;
  }
  
  async login(email: string, password: string): Promise<AuthResult> {
    // 验证用户凭据
    const user = await this.validateCredentials(email, password);
    if (!user) {
      throw new AuthError('Invalid credentials');
    }
    
    // 生成令牌
    const accessToken = this.generateAccessToken(user);
    const refreshToken = this.generateRefreshToken(user);
    
    // 存储刷新令牌
    await this.storeRefreshToken(user.id, refreshToken);
    
    return {
      accessToken,
      refreshToken,
      user,
      expiresIn: 15 * 60 // 15分钟
    };
  }
  
  async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      // 验证刷新令牌
      const payload = jwt.verify(refreshToken, this.refreshTokenSecret) as JWTPayload;
      
      // 检查令牌是否在数据库中
      const storedToken = await this.getStoredRefreshToken(payload.userId);
      if (storedToken !== refreshToken) {
        throw new AuthError('Invalid refresh token');
      }
      
      // 获取用户信息
      const user = await this.getUserById(payload.userId);
      if (!user) {
        throw new AuthError('User not found');
      }
      
      // 生成新的访问令牌
      const newAccessToken = this.generateAccessToken(user);
      
      return {
        accessToken: newAccessToken,
        refreshToken, // 保持原有刷新令牌
        user,
        expiresIn: 15 * 60
      };
      
    } catch (error) {
      throw new AuthError('Invalid refresh token');
    }
  }
  
  async logout(userId: string): Promise<void> {
    // 删除存储的刷新令牌
    await this.removeRefreshToken(userId);
  }
  
  async validateToken(token: string): Promise<User | null> {
    try {
      const payload = jwt.verify(token, this.jwtSecret) as JWTPayload;
      return await this.getUserById(payload.userId);
    } catch (error) {
      return null;
    }
  }
  
  private generateAccessToken(user: User): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      subscriptionTier: user.subscriptionTier,
      iat: Math.floor(Date.now() / 1000)
    };
    
    return jwt.sign(payload, this.jwtSecret, { expiresIn: this.tokenExpiry });
  }
  
  private generateRefreshToken(user: User): string {
    const payload: JWTPayload = {
      userId: user.id,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000)
    };
    
    return jwt.sign(payload, this.refreshTokenSecret, { expiresIn: this.refreshTokenExpiry });
  }
  
  private async validateCredentials(email: string, password: string): Promise<User | null> {
    // 实现用户凭据验证逻辑
    // 包括密码哈希验证等
    return null; // 占位符
  }
  
  private async storeRefreshToken(userId: string, token: string): Promise<void> {
    // 存储刷新令牌到数据库
  }
  
  private async getStoredRefreshToken(userId: string): Promise<string | null> {
    // 从数据库获取存储的刷新令牌
    return null; // 占位符
  }
  
  private async removeRefreshToken(userId: string): Promise<void> {
    // 从数据库删除刷新令牌
  }
  
  private async getUserById(userId: string): Promise<User | null> {
    // 从数据库获取用户信息
    return null; // 占位符
  }
}

interface JWTPayload {
  userId: string;
  email?: string;
  subscriptionTier?: string;
  type?: string;
  iat: number;
}

interface AuthResult {
  accessToken: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

class AuthError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthError';
  }
}
```

#### 7.2.2 权限控制中间件
```typescript
// 权限控制中间件
class AuthMiddleware {
  private authService: AuthService;
  
  constructor(authService: AuthService) {
    this.authService = authService;
  }
  
  // 基础认证中间件
  authenticate() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const token = this.extractToken(req);
        if (!token) {
          return res.status(401).json({ error: 'No token provided' });
        }
        
        const user = await this.authService.validateToken(token);
        if (!user) {
          return res.status(401).json({ error: 'Invalid token' });
        }
        
        req.user = user;
        next();
        
      } catch (error) {
        res.status(401).json({ error: 'Authentication failed' });
      }
    };
  }
  
  // 订阅层级权限检查
  requireSubscription(requiredTier: SubscriptionTier) {
    return (req: Request, res: Response, next: NextFunction) => {
      const user = req.user;
      if (!user) {
        return res.status(401).json({ error: 'Authentication required' });
      }
      
      if (!this.hasSubscriptionAccess(user.subscriptionTier, requiredTier)) {
        return res.status(403).json({ 
          error: 'Insufficient subscription level',
          required: requiredTier,
          current: user.subscriptionTier
        });
      }
      
      next();
    };
  }
  
  // 速率限制中间件
  rateLimit(options: RateLimitOptions) {
    const limiter = new Map<string, RateLimitData>();
    
    return (req: Request, res: Response, next: NextFunction) => {
      const key = this.getRateLimitKey(req);
      const now = Date.now();
      
      let data = limiter.get(key);
      if (!data) {
        data = { count: 0, resetTime: now + options.windowMs };
        limiter.set(key, data);
      }
      
      // 重置窗口
      if (now > data.resetTime) {
        data.count = 0;
        data.resetTime = now + options.windowMs;
      }
      
      data.count++;
      
      if (data.count > options.maxRequests) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((data.resetTime - now) / 1000)
        });
      }
      
      // 设置响应头
      res.set({
        'X-RateLimit-Limit': options.maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, options.maxRequests - data.count).toString(),
        'X-RateLimit-Reset': Math.ceil(data.resetTime / 1000).toString()
      });
      
      next();
    };
  }
  
  private extractToken(req: Request): string | null {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }
  
  private hasSubscriptionAccess(userTier: string, requiredTier: SubscriptionTier): boolean {
    const tierLevels = {
      'free': 1,
      'premium': 2,
      'enterprise': 3
    };
    
    return tierLevels[userTier] >= tierLevels[requiredTier];
  }
  
  private getRateLimitKey(req: Request): string {
    // 基于用户ID或IP地址生成限制键
    return req.user?.id || req.ip;
  }
}

type SubscriptionTier = 'free' | 'premium' | 'enterprise';

interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
}

interface RateLimitData {
  count: number;
  resetTime: number;
}

// 扩展Express Request类型
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}
```

### 7.3 错误处理

#### 7.3.1 统一错误处理
```typescript
// 错误类型定义
enum ErrorCode {
  // 通用错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  
  // 认证错误
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // 业务错误
  CONTENT_EXTRACTION_FAILED = 'CONTENT_EXTRACTION_FAILED',
  AI_SERVICE_UNAVAILABLE = 'AI_SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUBSCRIPTION_REQUIRED = 'SUBSCRIPTION_REQUIRED',
  
  // 数据错误
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  DATA_CORRUPTION = 'DATA_CORRUPTION'
}

// 自定义错误类
class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly details?: any;
  
  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = details;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 具体错误类
class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(ErrorCode.VALIDATION_ERROR, message, 400, true, details);
  }
}

class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(ErrorCode.AUTHENTICATION_FAILED, message, 401);
  }
}

class AuthorizationError extends AppError {
  constructor(message: string = 'Authorization failed') {
    super(ErrorCode.AUTHORIZATION_FAILED, message, 403);
  }
}

class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(ErrorCode.RESOURCE_NOT_FOUND, `${resource} not found`, 404);
  }
}

class RateLimitError extends AppError {
  constructor(retryAfter: number) {
    super(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      'Rate limit exceeded',
      429,
      true,
      { retryAfter }
    );
  }
}

// 错误处理中间件
class ErrorHandler {
  private logger: Logger;
  
  constructor(logger: Logger) {
    this.logger = logger;
  }
  
  // Express错误处理中间件
  handleError() {
    return (error: Error, req: Request, res: Response, next: NextFunction) => {
      if (error instanceof AppError) {
        this.handleAppError(error, req, res);
      } else {
        this.handleUnknownError(error, req, res);
      }
    };
  }
  
  private handleAppError(error: AppError, req: Request, res: Response): void {
    const errorResponse: ErrorResponse = {
      error: error.code,
      message: error.message,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    };
    
    if (error.details) {
      errorResponse.details = error.details;
    }
    
    // 记录错误日志
    if (error.statusCode >= 500) {
      this.logger.error('Server error', {
        error: error.code,
        message: error.message,
        stack: error.stack,
        request: {
          method: req.method,
          url: req.url,
          headers: req.headers,
          body: req.body
        }
      });
    } else {
      this.logger.warn('Client error', {
        error: error.code,
        message: error.message,
        request: {
          method: req.method,
          url: req.url
        }
      });
    }
    
    res.status(error.statusCode).json(errorResponse);
  }
  
  private handleUnknownError(error: Error, req: Request, res: Response): void {
    // 记录未知错误
    this.logger.error('Unknown error', {
      message: error.message,
      stack: error.stack,
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body
      }
    });
    
    // 返回通用错误响应
    const errorResponse: ErrorResponse = {
      error: ErrorCode.INTERNAL_SERVER_ERROR,
      message: 'Internal server error',
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    };
    
    res.status(500).json(errorResponse);
  }
  
  // 处理未捕获的异常
  handleUncaughtException(): void {
    process.on('uncaughtException', (error: Error) => {
      this.logger.error('Uncaught exception', {
        message: error.message,
        stack: error.stack
      });
      
      // 优雅关闭应用
      process.exit(1);
    });
  }
  
  // 处理未处理的Promise拒绝
  handleUnhandledRejection(): void {
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      this.logger.error('Unhandled rejection', {
        reason: reason?.message || reason,
        stack: reason?.stack
      });
      
      // 优雅关闭应用
      process.exit(1);
    });
  }
}

interface ErrorResponse {
  error: string;
  message: string;
  timestamp: string;
  path: string;
  method: string;
  details?: any;
}

// 客户端错误处理
class ClientErrorHandler {
  private retryConfig: RetryConfig;
  
  constructor(retryConfig: RetryConfig) {
    this.retryConfig = retryConfig;
  }
  
  async handleAPIError(error: APIError, originalRequest: () => Promise<any>): Promise<any> {
    switch (error.status) {
      case 401:
        // Token过期，尝试刷新
        return this.handleAuthError(originalRequest);
        
      case 429:
        // 速率限制，等待后重试
        return this.handleRateLimitError(error, originalRequest);
        
      case 500:
      case 502:
      case 503:
      case 504:
        // 服务器错误，重试
        return this.handleServerError(error, originalRequest);
        
      default:
        throw error;
    }
  }
  
  private async handleAuthError(originalRequest: () => Promise<any>): Promise<any> {
    try {
      // 尝试刷新token
      await this.refreshAuthToken();
      
      // 重新发起请求
      return originalRequest();
    } catch (refreshError) {
      // 刷新失败，跳转到登录页
      this.redirectToLogin();
      throw refreshError;
    }
  }
  
  private async handleRateLimitError(
    error: APIError,
    originalRequest: () => Promise<any>
  ): Promise<any> {
    const retryAfter = this.extractRetryAfter(error);
    
    if (retryAfter && retryAfter <= this.retryConfig.maxRetryDelay) {
      await this.delay(retryAfter * 1000);
      return originalRequest();
    }
    
    throw error;
  }
  
  private async handleServerError(
    error: APIError,
    originalRequest: () => Promise<any>
  ): Promise<any> {
    for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
      const delay = this.calculateBackoffDelay(attempt);
      await this.delay(delay);
      
      try {
        return await originalRequest();
      } catch (retryError) {
        if (attempt === this.retryConfig.maxRetries) {
          throw retryError;
        }
      }
    }
    
    throw error;
  }
  
  private calculateBackoffDelay(attempt: number): number {
    const baseDelay = this.retryConfig.baseDelay;
    const maxDelay = this.retryConfig.maxRetryDelay;
    
    // 指数退避算法
    const delay = baseDelay * Math.pow(2, attempt - 1);
    
    // 添加随机抖动
    const jitter = Math.random() * 0.1 * delay;
    
    return Math.min(delay + jitter, maxDelay);
  }
  
  private extractRetryAfter(error: APIError): number | null {
    // 从错误响应中提取Retry-After头
    try {
      const response = JSON.parse(error.body);
      return response.retryAfter || null;
    } catch {
      return null;
    }
  }
  
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  private async refreshAuthToken(): Promise<void> {
    // 实现token刷新逻辑
  }
  
  private redirectToLogin(): void {
    // 实现登录页跳转逻辑
  }
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxRetryDelay: number;
}
```

## 8. 安全架构

### 8.1 数据安全

#### 8.1.1 数据加密策略
```typescript
// 加密服务
class EncryptionService {
  private algorithm = 'aes-256-gcm';
  private keyDerivationIterations = 100000;
  
  // 生成加密密钥
  async generateKey(password: string, salt: Buffer): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(
        password,
        salt,
        this.keyDerivationIterations,
        32,
        'sha256',
        (err, derivedKey) => {
          if (err) reject(err);
          else resolve(derivedKey);
        }
      );
    });
  }
  
  // 加密数据
  async encrypt(data: string, password: string): Promise<EncryptedData> {
    const salt = crypto.randomBytes(16);
    const iv = crypto.randomBytes(12);
    const key = await this.generateKey(password, salt);
    
    const cipher = crypto.createCipherGCM(this.algorithm, key, iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      salt: salt.toString('hex'),
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
      algorithm: this.algorithm
    };
  }
  
  // 解密数据
  async decrypt(encryptedData: EncryptedData, password: string): Promise<string> {
    const salt = Buffer.from(encryptedData.salt, 'hex');
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const authTag = Buffer.from(encryptedData.authTag, 'hex');
    const key = await this.generateKey(password, salt);
    
    const decipher = crypto.createDecipherGCM(this.algorithm, key, iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  // 生成安全随机字符串
  generateSecureRandom(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
  
  // 哈希密码
  async hashPassword(password: string): Promise<HashedPassword> {
    const salt = crypto.randomBytes(16);
    const hash = await this.generateKey(password, salt);
    
    return {
      hash: hash.toString('hex'),
      salt: salt.toString('hex'),
      iterations: this.keyDerivationIterations
    };
  }
  
  // 验证密码
  async verifyPassword(password: string, hashedPassword: HashedPassword): Promise<boolean> {
    const salt = Buffer.from(hashedPassword.salt, 'hex');
    const hash = await this.generateKey(password, salt);
    
    return crypto.timingSafeEqual(
      Buffer.from(hashedPassword.hash, 'hex'),
      hash
    );
  }
}

interface EncryptedData {
  encrypted: string;
  salt: string;
  iv: string;
  authTag: string;
  algorithm: string;
}

interface HashedPassword {
  hash: string;
  salt: string;
  iterations: number;
}

// 敏感数据处理
class SensitiveDataHandler {
  private encryptionService: EncryptionService;
  private masterKey: string;
  
  constructor(encryptionService: EncryptionService, masterKey: string) {
    this.encryptionService = encryptionService;
    this.masterKey = masterKey;
  }
  
  // 加密用户设置
  async encryptUserSettings(settings: UserSettings): Promise<string> {
    const settingsJson = JSON.stringify(settings);
    const encrypted = await this.encryptionService.encrypt(settingsJson, this.masterKey);
    return JSON.stringify(encrypted);
  }
  
  // 解密用户设置
  async decryptUserSettings(encryptedSettings: string): Promise<UserSettings> {
    const encryptedData = JSON.parse(encryptedSettings) as EncryptedData;
    const decrypted = await this.encryptionService.decrypt(encryptedData, this.masterKey);
    return JSON.parse(decrypted);
  }
  
  // 脱敏处理
  sanitizeForLogging(data: any): any {
    const sensitiveFields = ['password', 'token', 'email', 'apiKey'];
    
    if (typeof data !== 'object' || data === null) {
      return data;
    }
    
    const sanitized = { ...data };
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = this.maskSensitiveValue(sanitized[field]);
      }
    }
    
    // 递归处理嵌套对象
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeForLogging(sanitized[key]);
      }
    }
    
    return sanitized;
  }
  
  private maskSensitiveValue(value: string): string {
    if (!value || value.length <= 4) {
      return '***';
    }
    
    const visibleChars = 2;
    const maskedLength = value.length - visibleChars * 2;
    const mask = '*'.repeat(Math.max(maskedLength, 3));
    
    return value.substring(0, visibleChars) + mask + value.substring(value.length - visibleChars);
  }
}
```

### 8.2 隐私保护

#### 8.2.1 数据最小化原则
```typescript
// 隐私保护管理器
class PrivacyManager {
  private dataRetentionPolicies: Map<string, RetentionPolicy>;
  private consentManager: ConsentManager;
  
  constructor() {
    this.dataRetentionPolicies = new Map();
    this.consentManager = new ConsentManager();
    this.initializeRetentionPolicies();
  }
  
  private initializeRetentionPolicies(): void {
    // 用户活动数据保留30天
    this.dataRetentionPolicies.set('user_activities', {
      retentionDays: 30,
      autoDelete: true,
      anonymizeAfter: 7
    });
    
    // 内容缓存保留7天
    this.dataRetentionPolicies.set('content_cache', {
      retentionDays: 7,
      autoDelete: true,
      anonymizeAfter: 0
    });
    
    // 用户设置永久保留（直到用户删除账户）
    this.dataRetentionPolicies.set('user_settings', {
      retentionDays: -1, // 永久
      autoDelete: false,
      anonymizeAfter: 0
    });
  }
  
  // 数据收集前的同意检查
  async checkDataCollectionConsent(
    userId: string,
    dataType: DataType,
    purpose: DataPurpose
  ): Promise<boolean> {
    const consent = await this.consentManager.getConsent(userId);
    
    return consent.hasConsentFor(dataType, purpose);
  }
  
  // 数据匿名化
  async anonymizeUserData(userId: string, dataType: string): Promise<void> {
    switch (dataType) {
      case 'user_activities':
        await this.anonymizeUserActivities(userId);
        break;
      case 'content_cache':
        await this.anonymizeContentCache(userId);
        break;
      default:
        throw new Error(`Unknown data type: ${dataType}`);
    }
  }
  
  private async anonymizeUserActivities(userId: string): Promise<void> {
    // 将用户ID替换为匿名标识符
    const anonymousId = this.generateAnonymousId();
    
    // 更新数据库中的记录
    await this.updateUserActivities(userId, {
      userId: anonymousId,
      // 移除其他可识别信息
      sessionId: null,
      ipAddress: null,
      userAgent: null
    });
  }
  
  private async anonymizeContentCache(userId: string): Promise<void> {
    // 删除与用户相关的缓存内容
    await this.deleteUserContentCache(userId);
  }
  
  // 数据导出（GDPR合规）
  async exportUserData(userId: string): Promise<UserDataExport> {
    const userData: UserDataExport = {
      userId,
      exportDate: new Date(),
      data: {}
    };
    
    // 导出用户设置
    userData.data.settings = await this.getUserSettings(userId);
    
    // 导出用户活动（匿名化敏感信息）
    userData.data.activities = await this.getUserActivities(userId);
    
    // 导出内容缓存
    userData.data.contentCache = await this.getUserContentCache(userId);
    
    return userData;
  }
  
  // 数据删除（被遗忘权）
  async deleteUserData(userId: string): Promise<DeletionReport> {
    const deletionReport: DeletionReport = {
      userId,
      deletionDate: new Date(),
      deletedDataTypes: [],
      errors: []
    };
    
    try {
      // 删除用户设置
      await this.deleteUserSettings(userId);
      deletionReport.deletedDataTypes.push('settings');
      
      // 删除用户活动
      await this.deleteUserActivities(userId);
      deletionReport.deletedDataTypes.push('activities');
      
      // 删除内容缓存
      await this.deleteUserContentCache(userId);
      deletionReport.deletedDataTypes.push('content_cache');
      
      // 删除用户账户
      await this.deleteUserAccount(userId);
      deletionReport.deletedDataTypes.push('account');
      
    } catch (error) {
      deletionReport.errors.push({
        type: 'deletion_error',
        message: error.message,
        timestamp: new Date()
      });
    }
    
    return deletionReport;
  }
  
  // 自动数据清理
  async runDataRetentionCleanup(): Promise<void> {
    for (const [dataType, policy] of this.dataRetentionPolicies) {
      if (policy.autoDelete) {
        await this.cleanupExpiredData(dataType, policy);
      }
    }
  }
  
  private async cleanupExpiredData(dataType: string, policy: RetentionPolicy): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - policy.retentionDays);
    
    // 删除过期数据
    await this.deleteDataOlderThan(dataType, cutoffDate);
    
    // 匿名化即将过期的数据
    if (policy.anonymizeAfter > 0) {
      const anonymizeDate = new Date();
      anonymizeDate.setDate(anonymizeDate.getDate() - policy.anonymizeAfter);
      
      await this.anonymizeDataOlderThan(dataType, anonymizeDate);
    }
  }
  
  private generateAnonymousId(): string {
    return `anon_${crypto.randomBytes(16).toString('hex')}`;
  }
  
  // 占位符方法（需要实现具体的数据库操作）
  private async getUserSettings(userId: string): Promise<any> { return {}; }
  private async getUserActivities(userId: string): Promise<any[]> { return []; }
  private async getUserContentCache(userId: string): Promise<any[]> { return []; }
  private async deleteUserSettings(userId: string): Promise<void> {}
  private async deleteUserActivities(userId: string): Promise<void> {}
  private async deleteUserContentCache(userId: string): Promise<void> {}
  private async deleteUserAccount(userId: string): Promise<void> {}
  private async updateUserActivities(userId: string, updates: any): Promise<void> {}
  private async deleteDataOlderThan(dataType: string, date: Date): Promise<void> {}
  private async anonymizeDataOlderThan(dataType: string, date: Date): Promise<void> {}
}

// 同意管理器
class ConsentManager {
  async getConsent(userId: string): Promise<UserConsent> {
    // 从数据库获取用户同意记录
    return new UserConsent(userId);
  }
  
  async updateConsent(userId: string, consent: ConsentUpdate): Promise<void> {
    // 更新用户同意记录
  }
}

class UserConsent {
  constructor(private userId: string) {}
  
  hasConsentFor(dataType: DataType, purpose: DataPurpose): boolean {
    // 检查用户是否同意特定数据类型和用途的数据收集
    return true; // 占位符
  }
}

// 类型定义
interface RetentionPolicy {
  retentionDays: number; // -1表示永久保留
  autoDelete: boolean;
  anonymizeAfter: number; // 多少天后匿名化
}

interface UserDataExport {
  userId: string;
  exportDate: Date;
  data: {
    settings?: any;
    activities?: any[];
    contentCache?: any[];
  };
}

interface DeletionReport {
  userId: string;
  deletionDate: Date;
  deletedDataTypes: string[];
  errors: DeletionError[];
}

interface DeletionError {
  type: string;
  message: string;
  timestamp: Date;
}

interface ConsentUpdate {
  dataTypes: DataType[];
  purposes: DataPurpose[];
  granted: boolean;
  timestamp: Date;
}

enum DataType {
  USER_SETTINGS = 'user_settings',
  USER_ACTIVITIES = 'user_activities',
  CONTENT_CACHE = 'content_cache',
  ANALYTICS = 'analytics'
}

enum DataPurpose {
  SERVICE_PROVISION = 'service_provision',
  ANALYTICS = 'analytics',
  MARKETING = 'marketing',
  RESEARCH = 'research'
}
```

### 8.3 权限控制

#### 8.3.1 基于角色的访问控制(RBAC)
```typescript
// 权限系统
class PermissionSystem {
  private roles: Map<string, Role>;
  private permissions: Map<string, Permission>;
  private userRoles: Map<string, string[]>;
  
  constructor() {
    this.roles = new Map();
    this.permissions = new Map();
    this.userRoles = new Map();
    this.initializeDefaultRoles();
  }
  
  private initializeDefaultRoles(): void {
    // 定义权限
    const permissions = [
      new Permission('content:read', 'Read content'),
      new Permission('content:extract', 'Extract content'),
      new Permission('content:enhance', 'Enhance content with AI'),
      new Permission('settings:read', 'Read user settings'),
      new Permission('settings:write', 'Write user settings'),
      new Permission('analytics:read', 'Read analytics data'),
      new Permission('admin:users', 'Manage users'),
      new Permission('admin:system', 'System administration')
    ];
    
    permissions.forEach(p => this.permissions.set(p.name, p));
    
    // 定义角色
    const freeUser = new Role('free_user', 'Free User', [
      'content:read',
      'content:extract',
      'settings:read',
      'settings:write'
    ]);
    
    const premiumUser = new Role('premium_user', 'Premium User', [
      ...freeUser.permissions,
      'content:enhance',
      'analytics:read'
    ]);
    
    const admin = new Role('admin', 'Administrator', [
      ...premiumUser.permissions,
      'admin:users',
      'admin:system'
    ]);
    
    this.roles.set(freeUser.name, freeUser);
    this.roles.set(premiumUser.name, premiumUser);
    this.roles.set(admin.name, admin);
  }
  
  // 检查用户权限
  async hasPermission(userId: string, permissionName: string): Promise<boolean> {
    const userRoles = await this.getUserRoles(userId);
    
    for (const roleName of userRoles) {
      const role = this.roles.get(roleName);
      if (role && role.hasPermission(permissionName)) {
        return true;
      }
    }
    
    return false;
  }
  
  // 检查多个权限
  async hasAllPermissions(userId: string, permissionNames: string[]````markdown path=docs/Architecture_Design_Document.md mode=EDIT
  ): Promise<boolean> {
    for (const permission of permissionNames) {
      if (!(await this.hasPermission(userId, permission))) {
        return false;
      }
    }
    return true;
  }
  
  // 检查任一权限
  async hasAnyPermission(userId: string, permissionNames: string[]): Promise<boolean> {
    for (const permission of permissionNames) {
      if (await this.hasPermission(userId, permission)) {
        return true;
      }
    }
    return false;
  }
  
  // 分配角色给用户
  async assignRole(userId: string, roleName: string): Promise<void> {
    if (!this.roles.has(roleName)) {
      throw new Error(`Role ${roleName} does not exist`);
    }
    
    const userRoles = await this.getUserRoles(userId);
    if (!userRoles.includes(roleName)) {
      userRoles.push(roleName);
      this.userRoles.set(userId, userRoles);
      
      // 记录权限变更日志
      await this.logPermissionChange(userId, 'role_assigned', { role: roleName });
    }
  }
  
  // 移除用户角色
  async removeRole(userId: string, roleName: string): Promise<void> {
    const userRoles = await this.getUserRoles(userId);
    const index = userRoles.indexOf(roleName);
    
    if (index > -1) {
      userRoles.splice(index, 1);
      this.userRoles.set(userId, userRoles);
      
      await this.logPermissionChange(userId, 'role_removed', { role: roleName });
    }
  }
  
  // 获取用户所有权限
  async getUserPermissions(userId: string): Promise<Permission[]> {
    const userRoles = await this.getUserRoles(userId);
    const permissions = new Set<Permission>();
    
    for (const roleName of userRoles) {
      const role = this.roles.get(roleName);
      if (role) {
        for (const permissionName of role.permissions) {
          const permission = this.permissions.get(permissionName);
          if (permission) {
            permissions.add(permission);
          }
        }
      }
    }
    
    return Array.from(permissions);
  }
  
  private async getUserRoles(userId: string): Promise<string[]> {
    return this.userRoles.get(userId) || ['free_user']; // 默认为免费用户
  }
  
  private async logPermissionChange(
    userId: string,
    action: string,
    details: any
  ): Promise<void> {
    // 记录权限变更到审计日志
    console.log(`Permission change: ${action} for user ${userId}`, details);
  }
}

class Role {
  constructor(
    public name: string,
    public displayName: string,
    public permissions: string[]
  ) {}
  
  hasPermission(permissionName: string): boolean {
    return this.permissions.includes(permissionName);
  }
}

class Permission {
  constructor(
    public name: string,
    public description: string
  ) {}
}

// 权限装饰器
function RequirePermission(permission: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const userId = this.getCurrentUserId(); // 需要实现获取当前用户ID的方法
      const permissionSystem = this.getPermissionSystem(); // 需要实现获取权限系统的方法
      
      if (!(await permissionSystem.hasPermission(userId, permission))) {
        throw new AuthorizationError(`Permission ${permission} required`);
      }
      
      return method.apply(this, args);
    };
    
    return descriptor;
  };
}

// 使用示例
class ContentService {
  @RequirePermission('content:enhance')
  async enhanceContent(content: string): Promise<string> {
    // 内容增强逻辑
    return content;
  }
  
  @RequirePermission('content:extract')
  async extractContent(url: string): Promise<ExtractedContent> {
    // 内容提取逻辑
    return {} as ExtractedContent;
  }
}
```

#### 8.3.2 API安全防护
```typescript
// API安全中间件
class APISecurityMiddleware {
  private rateLimiter: RateLimiter;
  private requestValidator: RequestValidator;
  private securityHeaders: SecurityHeaders;
  
  constructor() {
    this.rateLimiter = new RateLimiter();
    this.requestValidator = new RequestValidator();
    this.securityHeaders = new SecurityHeaders();
  }
  
  // CORS配置
  corsMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const allowedOrigins = [
        'chrome-extension://*',
        'moz-extension://*',
        'https://readingassistant.com'
      ];
      
      const origin = req.headers.origin;
      if (origin && this.isOriginAllowed(origin, allowedOrigins)) {
        res.header('Access-Control-Allow-Origin', origin);
      }
      
      res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');
      
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    };
  }
  
  // 安全头设置
  securityHeadersMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      // 防止XSS攻击
      res.header('X-XSS-Protection', '1; mode=block');
      
      // 防止MIME类型嗅探
      res.header('X-Content-Type-Options', 'nosniff');
      
      // 防止点击劫持
      res.header('X-Frame-Options', 'DENY');
      
      // HSTS
      res.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      
      // CSP
      res.header('Content-Security-Policy', this.getCSPPolicy());
      
      // 隐藏服务器信息
      res.removeHeader('X-Powered-By');
      
      next();
    };
  }
  
  // 请求验证中间件
  requestValidationMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        // 验证请求大小
        if (req.headers['content-length']) {
          const contentLength = parseInt(req.headers['content-length']);
          if (contentLength > 10 * 1024 * 1024) { // 10MB限制
            return res.status(413).json({ error: 'Request too large' });
          }
        }
        
        // 验证Content-Type
        if (req.method === 'POST' || req.method === 'PUT') {
          const contentType = req.headers['content-type'];
          if (!contentType || !contentType.includes('application/json')) {
            return res.status(400).json({ error: 'Invalid content type' });
          }
        }
        
        // SQL注入检测
        if (this.containsSQLInjection(req)) {
          return res.status(400).json({ error: 'Invalid request' });
        }
        
        // XSS检测
        if (this.containsXSS(req)) {
          return res.status(400).json({ error: 'Invalid request' });
        }
        
        next();
      } catch (error) {
        res.status(400).json({ error: 'Request validation failed' });
      }
    };
  }
  
  // IP白名单中间件
  ipWhitelistMiddleware(whitelist: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
      const clientIP = this.getClientIP(req);
      
      if (!this.isIPWhitelisted(clientIP, whitelist)) {
        return res.status(403).json({ error: 'IP not allowed' });
      }
      
      next();
    };
  }
  
  // API密钥验证中间件
  apiKeyMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const apiKey = req.headers['x-api-key'] as string;
      
      if (!apiKey) {
        return res.status(401).json({ error: 'API key required' });
      }
      
      if (!this.validateAPIKey(apiKey)) {
        return res.status(401).json({ error: 'Invalid API key' });
      }
      
      next();
    };
  }
  
  private isOriginAllowed(origin: string, allowedOrigins: string[]): boolean {
    return allowedOrigins.some(allowed => {
      if (allowed.includes('*')) {
        const pattern = allowed.replace(/\*/g, '.*');
        return new RegExp(pattern).test(origin);
      }
      return allowed === origin;
    });
  }
  
  private getCSPPolicy(): string {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "connect-src 'self' https://api.readingassistant.com",
      "font-src 'self'",
      "object-src 'none'",
      "media-src 'self'",
      "frame-src 'none'"
    ].join('; ');
  }
  
  private containsSQLInjection(req: Request): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(--|\/\*|\*\/)/,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/i
    ];
    
    const checkString = JSON.stringify(req.body) + JSON.stringify(req.query);
    
    return sqlPatterns.some(pattern => pattern.test(checkString));
  }
  
  private containsXSS(req: Request): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi
    ];
    
    const checkString = JSON.stringify(req.body) + JSON.stringify(req.query);
    
    return xssPatterns.some(pattern => pattern.test(checkString));
  }
  
  private getClientIP(req: Request): string {
    return req.headers['x-forwarded-for'] as string ||
           req.headers['x-real-ip'] as string ||
           req.connection.remoteAddress ||
           req.socket.remoteAddress ||
           '';
  }
  
  private isIPWhitelisted(ip: string, whitelist: string[]): boolean {
    return whitelist.includes(ip) || whitelist.includes('*');
  }
  
  private validateAPIKey(apiKey: string): boolean {
    // 实现API密钥验证逻辑
    return true; // 占位符
  }
}

// 速率限制器
class RateLimiter {
  private limits: Map<string, RateLimit>;
  
  constructor() {
    this.limits = new Map();
  }
  
  middleware(options: RateLimitOptions) {
    return (req: Request, res: Response, next: NextFunction) => {
      const key = this.generateKey(req, options);
      const now = Date.now();
      
      let limit = this.limits.get(key);
      if (!limit) {
        limit = {
          count: 0,
          resetTime: now + options.windowMs,
          blocked: false
        };
        this.limits.set(key, limit);
      }
      
      // 重置窗口
      if (now > limit.resetTime) {
        limit.count = 0;
        limit.resetTime = now + options.windowMs;
        limit.blocked = false;
      }
      
      // 检查是否被阻止
      if (limit.blocked) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((limit.resetTime - now) / 1000)
        });
      }
      
      limit.count++;
      
      // 检查是否超过限制
      if (limit.count > options.maxRequests) {
        limit.blocked = true;
        
        // 记录可疑活动
        this.logSuspiciousActivity(req, key, limit.count);
        
        return res.status(429).json({
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((limit.resetTime - now) / 1000)
        });
      }
      
      // 设置响应头
      res.set({
        'X-RateLimit-Limit': options.maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, options.maxRequests - limit.count).toString(),
        'X-RateLimit-Reset': Math.ceil(limit.resetTime / 1000).toString()
      });
      
      next();
    };
  }
  
  private generateKey(req: Request, options: RateLimitOptions): string {
    const identifier = req.user?.id || req.ip;
    return `${options.keyPrefix || 'rate_limit'}:${identifier}`;
  }
  
  private logSuspiciousActivity(req: Request, key: string, requestCount: number): void {
    console.warn('Suspicious activity detected', {
      key,
      requestCount,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString()
    });
  }
}

interface RateLimit {
  count: number;
  resetTime: number;
  blocked: boolean;
}

interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  keyPrefix?: string;
}

// 请求验证器
class RequestValidator {
  validateContentExtractionRequest(req: Request): ValidationResult {
    const { url, html, options } = req.body;
    const errors: string[] = [];
    
    if (!url && !html) {
      errors.push('Either url or html must be provided');
    }
    
    if (url && !this.isValidURL(url)) {
      errors.push('Invalid URL format');
    }
    
    if (html && html.length > 1024 * 1024) { // 1MB限制
      errors.push('HTML content too large');
    }
    
    if (options && typeof options !== 'object') {
      errors.push('Options must be an object');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  private isValidURL(url: string): boolean {
    try {
      const parsedURL = new URL(url);
      return ['http:', 'https:'].includes(parsedURL.protocol);
    } catch {
      return false;
    }
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// 安全头管理器
class SecurityHeaders {
  getDefaultHeaders(): Record<string, string> {
    return {
      'X-XSS-Protection': '1; mode=block',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
  }
  
  getCSPForExtension(): string {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval'", // 扩展可能需要eval
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "connect-src 'self' https:",
      "font-src 'self'",
      "object-src 'none'"
    ].join('; ');
  }
}
```

## 9. 性能优化

### 9.1 前端性能优化

#### 9.1.1 代码分割与懒加载
```typescript
// 动态导入管理器
class DynamicImportManager {
  private loadedModules: Map<string, any> = new Map();
  private loadingPromises: Map<string, Promise<any>> = new Map();
  
  // 懒加载模块
  async loadModule<T>(moduleName: string, importFn: () => Promise<T>): Promise<T> {
    // 如果已经加载，直接返回
    if (this.loadedModules.has(moduleName)) {
      return this.loadedModules.get(moduleName);
    }
    
    // 如果正在加载，返回现有的Promise
    if (this.loadingPromises.has(moduleName)) {
      return this.loadingPromises.get(moduleName);
    }
    
    // 开始加载
    const loadingPromise = importFn().then(module => {
      this.loadedModules.set(moduleName, module);
      this.loadingPromises.delete(moduleName);
      return module;
    }).catch(error => {
      this.loadingPromises.delete(moduleName);
      throw error;
    });
    
    this.loadingPromises.set(moduleName, loadingPromise);
    return loadingPromise;
  }
  
  // 预加载模块
  async preloadModule(moduleName: string, importFn: () => Promise<any>): Promise<void> {
    if (!this.loadedModules.has(moduleName) && !this.loadingPromises.has(moduleName)) {
      // 在空闲时间预加载
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          this.loadModule(moduleName, importFn);
        });
      } else {
        setTimeout(() => {
          this.loadModule(moduleName, importFn);
        }, 0);
      }
    }
  }
  
  // 卸载模块
  unloadModule(moduleName: string): void {
    this.loadedModules.delete(moduleName);
  }
  
  // 获取加载状态
  getLoadingStatus(): ModuleLoadingStatus {
    return {
      loaded: Array.from(this.loadedModules.keys()),
      loading: Array.from(this.loadingPromises.keys()),
      totalModules: this.loadedModules.size + this.loadingPromises.size
    };
  }
}

interface ModuleLoadingStatus {
  loaded: string[];
  loading: string[];
  totalModules: number;
}

// 组件懒加载
class ComponentLazyLoader {
  private importManager: DynamicImportManager;
  
  constructor() {
    this.importManager = new DynamicImportManager();
  }
  
  // 创建懒加载组件
  createLazyComponent<T>(
    componentName: string,
    importFn: () => Promise<{ default: T }>,
    fallback?: () => HTMLElement
  ): LazyComponent<T> {
    return new LazyComponent(componentName, importFn, fallback, this.importManager);
  }
  
  // 预加载关键组件
  async preloadCriticalComponents(): Promise<void> {
    const criticalComponents = [
      'ContentExtractor',
      'AIEnhancer',
      'SettingsPanel'
    ];
    
    const preloadPromises = criticalComponents.map(async (componentName) => {
      try {
        await this.importManager.preloadModule(
          componentName,
          () => import(`./components/${componentName}`)
        );
      } catch (error) {
        console.warn(`Failed to preload component ${componentName}:`, error);
      }
    });
    
    await Promise.allSettled(preloadPromises);
  }
}

class LazyComponent<T> {
  private element: HTMLElement | null = null;
  private isLoaded = false;
  
  constructor(
    private componentName: string,
    private importFn: () => Promise<{ default: T }>,
    private fallback?: () => HTMLElement,
    private importManager?: DynamicImportManager
  ) {}
  
  async render(container: HTMLElement): Promise<void> {
    // 显示加载状态
    if (this.fallback && !this.isLoaded) {
      const fallbackElement = this.fallback();
      container.appendChild(fallbackElement);
    }
    
    try {
      // 加载组件
      const module = await (this.importManager?.loadModule(
        this.componentName,
        this.importFn
      ) || this.importFn());
      
      // 创建组件实例
      const ComponentClass = module.default as any;
      const component = new ComponentClass();
      
      // 渲染组件
      if (typeof component.render === 'function') {
        this.element = await component.render();
      } else {
        this.element = component;
      }
      
      // 替换fallback
      if (this.fallback) {
        container.innerHTML = '';
      }
      
      container.appendChild(this.element);
      this.isLoaded = true;
      
    } catch (error) {
      console.error(`Failed to load component ${this.componentName}:`, error);
      
      // 显示错误状态
      const errorElement = this.createErrorElement(error);
      container.innerHTML = '';
      container.appendChild(errorElement);
    }
  }
  
  private createErrorElement(error: Error): HTMLElement {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'component-error';
    errorDiv.innerHTML = `
      <div class="error-message">
        <h3>组件加载失败</h3>
        <p>${error.message}</p>
        <button onclick="location.reload()">重新加载</button>
      </div>
    `;
    return errorDiv;
  }
  
  destroy(): void {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.element = null;
    this.isLoaded = false;
  }
}
```

#### 9.1.2 虚拟滚动实现
```typescript
// 虚拟滚动组件
class VirtualScrollList {
  private container: HTMLElement;
  private scrollContainer: HTMLElement;
  private itemHeight: number;
  private bufferSize: number;
  private items: any[];
  private visibleItems: Map<number, HTMLElement> = new Map();
  private startIndex = 0;
  private endIndex = 0;
  private renderItem: (item: any, index: number) => HTMLElement;
  
  constructor(options: VirtualScrollOptions) {
    this.container = options.container;
    this.itemHeight = options.itemHeight;
    this.bufferSize = options.bufferSize || 5;
    this.items = options.items || [];
    this.renderItem = options.renderItem;
    
    this.init();
  }
  
  private init(): void {
    this.setupScrollContainer();
    this.bindEvents();
    this.updateVisibleItems();
  }
  
  private setupScrollContainer(): void {
    this.scrollContainer = document.createElement('div');
    this.scrollContainer.style.cssText = `
      height: ${this.items.length * this.itemHeight}px;
      position: relative;
    `;
    
    this.container.style.cssText = `
      overflow-y: auto;
      position: relative;
    `;
    
    this.container.appendChild(this.scrollContainer);
  }
  
  private bindEvents(): void {
    this.container.addEventListener('scroll', this.handleScroll.bind(this));
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this));
  }
  
  private handleScroll(): void {
    this.updateVisibleItems();
  }
  
  private handleResize(): void {
    // 重新计算可见区域
    this.updateVisibleItems();
  }
  
  private updateVisibleItems(): void {
    const scrollTop = this.container.scrollTop;
    const containerHeight = this.container.clientHeight;
    
    // 计算可见范围
    const newStartIndex = Math.max(0, Math.floor(scrollTop / this.itemHeight) - this.bufferSize);
    const newEndIndex = Math.min(
      this.items.length - 1,
      Math.ceil((scrollTop + containerHeight) / this.itemHeight) + this.bufferSize
    );
    
    // 如果范围没有变化，不需要更新
    if (newStartIndex === this.startIndex && newEndIndex === this.endIndex) {
      return;
    }
    
    // 移除不再可见的元素
    for (const [index, element] of this.visibleItems) {
      if (index < newStartIndex || index > newEndIndex) {
        element.remove();
        this.visibleItems.delete(index);
      }
    }
    
    // 添加新的可见元素
    for (let i = newStartIndex; i <= newEndIndex; i++) {
      if (!this.visibleItems.has(i)) {
        const element = this.createItemElement(i);
        this.visibleItems.set(i, element);
        this.scrollContainer.appendChild(element);
      }
    }
    
    this.startIndex = newStartIndex;
    this.endIndex = newEndIndex;
  }
  
  private createItemElement(index: number): HTMLElement {
    const item = this.items[index];
    const element = this.renderItem(item, index);
    
    element.style.cssText = `
      position: absolute;
      top: ${index * this.itemHeight}px;
      height: ${this.itemHeight}px;
      width: 100%;
      box-sizing: border-box;
    `;
    
    return element;
  }
  
  // 更新数据
  updateItems(newItems: any[]): void {
    this.items = newItems;
    
    // 清除所有可见元素
    for (const element of this.visibleItems.values()) {
      element.remove();
    }
    this.visibleItems.clear();
    
    // 更新容器高度
    this.scrollContainer.style.height = `${this.items.length * this.itemHeight}px`;
    
    // 重新渲染
    this.updateVisibleItems();
  }
  
  // 滚动到指定项
  scrollToItem(index: number): void {
    const targetScrollTop = index * this.itemHeight;
    this.container.scrollTop = targetScrollTop;
  }
  
  // 获取当前可见项的索引范围
  getVisibleRange(): { start: number; end: number } {
    return {
      start: this.startIndex,
      end: this.endIndex
    };
  }
  
  // 销毁组件
  destroy(): void {
    this.container.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.handleResize);
    
    for (const element of this.visibleItems.values()) {
      element.remove();
    }
    
    this.visibleItems.clear();
    this.scrollContainer.remove();
  }
}

interface VirtualScrollOptions {
  container: HTMLElement;
  itemHeight: number;
  bufferSize?: number;
  items: any[];
  renderItem: (item: any, index: number) => HTMLElement;
}

// 使用示例：内容列表虚拟滚动
class ContentListVirtualScroll {
  private virtualScroll: VirtualScrollList;
  private contentItems: ContentItem[] = [];
  
  constructor(container: HTMLElement) {
    this.virtualScroll = new VirtualScrollList({
      container,
      itemHeight: 120,
      bufferSize: 3,
      items: this.contentItems,
      renderItem: this.renderContentItem.bind(this)
    });
  }
  
  private renderContentItem(item: ContentItem, index: number): HTMLElement {
    const element = document.createElement('div');
    element.className = 'content-item';
    element.innerHTML = `
      <div class="content-item-header">
        <h3 class="content-title">${this.escapeHtml(item.title)}</h3>
        <span class="content-date">${this.formatDate(item.createdAt)}</span>
      </div>
      <div class="content-summary">
        ${this.escapeHtml(item.summary)}
      </div>
      <div class="content-actions">
        <button onclick="this.openContent(${index})">打开</button>
        <button onclick="this.deleteContent(${index})">删除</button>
      </div>
    `;
    
    return element;
  }
  
  loadContent(items: ContentItem[]): void {
    this.contentItems = items;
    this.virtualScroll.updateItems(items);
  }
  
  addContent(item: ContentItem): void {
    this.contentItems.push(item);
    this.virtualScroll.updateItems(this.contentItems);
  }
  
  removeContent(index: number): void {
    this.contentItems.splice(index, 1);
    this.virtualScroll.updateItems(this.contentItems);
  }
  
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
  
  private formatDate(date: Date): string {
    return date.toLocaleDateString('zh-CN');
  }
  
  destroy(): void {
    this.virtualScroll.destroy();
  }
}

interface ContentItem {
  id: string;
  title: string;
  summary: string;
  createdAt: Date;
  url: string;
}
```

### 9.2 后端性能优化

#### 9.2.1 数据库优化
```typescript
// 数据库连接池管理
class DatabaseConnectionPool {
  private pool: any;
  private config: PoolConfig;
  private metrics: PoolMetrics;
  
  constructor(config: PoolConfig) {
    this.config = config;
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingRequests: 0,
      totalQueries: 0,
      averageQueryTime: 0
    };
    
    this.initializePool();
  }
  
  private initializePool(): void {
    // 使用pg-pool或类似的连接池库
    this.pool = new Pool({
      host: this.config.host,
      port: this.config.port,
      database: this.config.database,
      user: this.config.user,
      password: this.config.password,
      max: this.config.maxConnections,
      min: this.config.minConnections,
      idleTimeoutMillis: this.config.idleTimeout,
      connectionTimeoutMillis: this.config.connectionTimeout,
      acquireTimeoutMillis: this.config.acquireTimeout
    });
    
    this.setupEventHandlers();
  }
  
  private setupEventHandlers(): void {
    this.pool.on('connect', () => {
      this.metrics.totalConnections++;
      this.metrics.activeConnections++;
    });
    
    this.pool.on('remove', () => {
      this.metrics.totalConnections--;
      this.metrics.activeConnections--;
    });
    
    this.pool.on('error', (err: Error) => {
      console.error('Database pool error:', err);
    });
  }
  
  async query<T>(sql: string, params?: any[]): Promise<QueryResult<T>> {
    const startTime = Date.now();
    
    try {
      this.metrics.waitingRequests++;
      const client = await this.pool.connect();
      this.metrics.waitingRequests--;
      this.metrics.activeConnections++;
      
      try {
        const result = await client.query(sql, params);
        this.updateQueryMetrics(Date.now() - startTime);
        return result;
      } finally {
        client.release();
        this.metrics.activeConnections--;
        this.metrics.idleConnections++;
      }
    } catch (error) {
      this.metrics.waitingRequests--;
      throw error;
    }
  }
  
  async transaction<T>(callback: (client: any) => Promise<T>): Promise<T> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
  
  private updateQueryMetrics(queryTime: number): void {
    this.metrics.totalQueries++;
    this.metrics.averageQueryTime = 
      (this.metrics.averageQueryTime * (this.metrics.totalQueries - 1) + queryTime) / 
      this.metrics.totalQueries;
  }
  
  getMetrics(): PoolMetrics {
    return { ...this.metrics };
  }
  
  async close(): Promise<void> {
    await this.pool.end();
  }
}

interface PoolConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  maxConnections: number;
  minConnections: number;
  idleTimeout: number;
  connectionTimeout: number;
  acquireTimeout: number;
}

interface PoolMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingRequests: number;
  totalQueries: number;
  averageQueryTime: number;
}

interface QueryResult<T> {
  rows: T[];
  rowCount: number;
}

// 查询优化器
class QueryOptimizer {
  private queryCache: Map<string, CachedQuery> = new Map();
  private slowQueryThreshold = 1000; // 1秒
  
  // 查询缓存
  async cachedQuery<T>(
    pool: DatabaseConnectionPool,
    sql: string,
    params: any[] = [],
    ttl: number = 300000 // 5分钟
  ): Promise<QueryResult<T>> {
    const cacheKey = this.generateCacheKey(sql, params);
    const cached = this.queryCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.result;
    }
    
    const result = await pool.query<T>(sql, params);
    
    this.queryCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });
    
    return result;
  }
  
  // 批量查询优化
  async batchQuery<T>(
    pool: DatabaseConnectionPool,
    queries: BatchQuery[]
  ): Promise<QueryResult<T>[]> {
    // 将多个查询合并为一个事务
    return pool.transaction(async (client) => {
      const results: QueryResult<T>[] = [];
      
      for (const query of queries) {
        const result = await client.query(query.sql, query.params);
        results.push(result);
      }
      
      return results;
    });
  }
  
  // 分页查询优化
  async paginatedQuery<T>(
    pool: DatabaseConnectionPool,
    baseQuery: string,
    params: any[],
    page: number,
    pageSize: number
  ): Promise<PaginatedResult<T>> {
    // 使用LIMIT和OFFSET进行分页
    const offset = (page - 1) * pageSize;
    const paginatedSql = `${baseQuery} LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    const paginatedParams = [...params, pageSize, offset];
    
    // 同时获取总数
    const countSql = `SELECT COUNT(*) as total FROM (${baseQuery}) as count_query`;
    
    const [dataResult, countResult] = await Promise.all([
      pool.query<T>(paginatedSql, paginatedParams),
      pool.query<{ total: number }>(countSql, params)
    ]);
    
    const total = parseInt(countResult.rows[0].total.toString());
    const totalPages = Math.ceil(total / pageSize);
    
    return {
      data: dataResult.rows,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }
  
  // 慢查询监控
  async monitoredQuery<T>(
    pool: DatabaseConnectionPool,
    sql: string,
    params?: any[]
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    
    try {
      const result = await pool.query<T>(sql, params);
      const duration = Date.now() - startTime;
      
      if (duration > this.slowQueryThreshold) {
        this.logSlowQuery(sql, params, duration);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logFailedQuery(sql, params, duration, error);
      throw error;
    }
  }
  
  private generateCacheKey(sql: string, params: any[]): string {
    return crypto.createHash('md5')
      .update(sql + JSON.stringify(params))
      .digest('hex');
  }
  
  private logSlowQuery(sql: string, params: any[], duration: number): void {
    console.warn('Slow query detected', {
      sql: sql.substring(0, 200),
      params: params?.slice(0, 5),
      duration,
      timestamp: new Date().toISOString()
    });
  }
  
  private logFailedQuery(sql: string, params: any[], duration: number, error: Error): void {
    console.error('Query failed', {
      sql: sql.substring(0, 200),
      params: params?.slice(0, 5),
      duration,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
  
  // 清理过期缓存
  cleanExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, cached] of this.queryCache) {
      if (now - cached.timestamp > 300000) { // 5分钟过期
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.queryCache.delete(key));
  }
}

interface CachedQuery {
  result: QueryResult<any>;
  timestamp: number;
}

interface BatchQuery {
  sql: string;
  params?: any[];
}

interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

#### 9.2.2 缓存策略优化
```typescript
// 多级缓存管理器
class MultiLevelCacheManager {
  private l1Cache: MemoryCache;        // L1: 内存缓存
  private l2Cache: RedisCache;         // L2: Redis缓存
  private l3Cache: DatabaseCache;      // L3: 数据库缓存
  private cacheStats: CacheStatistics;
  
  constructor(config: CacheConfig) {
    this.l1Cache = new MemoryCache(config.l1);
    this.l2Cache = new RedisCache(config.l2);
    this.l3Cache = new DatabaseCache(config.l3);
    this.cacheStats = new CacheStatistics();
  }
  
  async get<T>(key: string): Promise<T | null> {
    const startTime = Date.now();
    
    try {
      // L1缓存查找
      let result = await this.l1Cache.get<T>(key);
      if (result !== null) {
        this.cacheStats.recordHit('l1', Date.now() - startTime);
        return result;
      }
      
      // L2缓存查找
      result = await this.l2Cache.get<T>(key);
      if (result !== null) {
        // 回填L1缓存
        await this.l1Cache.set(key, result, 300); // 5分钟
        this.cacheStats.recordHit('l2', Date.now() - startTime);
        return result;
      }
      
      // L3缓存查找
      result = await this.l3Cache.get<T>(key);
      if (result !== null) {
        // 回填L1和L2缓存
        await Promise.all([
          this.l1Cache.set(key, result, 300),
          this.l2Cache.set(key, result, 3600) // 1小时
        ]);
        this.cacheStats.recordHit('l3', Date.now() - startTime);
        return result;
      }
      
      this.cacheStats.recordMiss(Date.now() - startTime);
      return null;
      
    } catch (error) {
      this.cacheStats.recordError(error);
      throw error;
    }
  }
  
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const promises: Promise<void>[] = [];
    
    // 写入所有缓存层
    promises.push(this.l1Cache.set(key, value, Math.min(ttl || 300, 300)));
    promises.push(this.l2Cache.set(key, value, Math.min(ttl || 3600, 3600)));
    promises.push(this.l3Cache.set(key, value, ttl));
    
    await Promise.allSettled(promises);
  }
  
  async delete(key: string): Promise<void> {
    const promises = [
      this.l1Cache.delete(key),
      this.l2Cache.delete(key),
      this.l3Cache.delete(key)
    ];
    
    await Promise.allSettled(promises);
  }
  
  async invalidatePattern(pattern: string): Promise<void> {
    const promises = [
      this.l1Cache.invalidatePattern(pattern),
      this.l2Cache.invalidatePattern(pattern),
      this.l3Cache.invalidatePattern(pattern)
    ];
    
    await Promise.allSettled(promises);
  }
  
  getStatistics(): CacheStatistics {
    return this.cacheStats;
  }
  
  async warmup(keys: string[]): Promise<void> {
    // 预热缓存
    const warmupPromises = keys.map(async (key) => {
      try {
        await this.get(key);
      } catch (error) {
        console.warn(`Failed to warmup cache for key ${key}:`, error);
      }
    });
    
    await Promise.allSettled(warmupPromises);
  }
}

// Redis缓存实现
class RedisCache {
  private client: any; // Redis客户端
  private keyPrefix: string;
  
  constructor(config: RedisCacheConfig) {
    this.keyPrefix = config.keyPrefix || 'cache:';
    this.initializeClient(config);
  }
  
  private initializeClient(config: RedisCacheConfig): void {
    // 初始化Redis客户端
    this.client = new Redis({
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.database,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });
    
    this.client.on('error', (error: Error) => {
      console.error('Redis cache error:', error);
    });
  }
  
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(this.getKey(key));
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }
  
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      
      if (ttl) {
        await this.client.setex(this.getKey(key), ttl, serialized);
      } else {
        await this.client.set(this.getKey(key), serialized);
      }
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }
  
  async delete(key: string): Promise<void> {
    try {
      await this.client.del(this.getKey(key));
    } catch (error) {
      console.error('Redis delete error:', error);
    }
  }
  
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.client.keys(this.getKey(pattern));
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
    } catch (error) {
      console.error('Redis invalidate pattern error:', error);
    }
  }
  
  private getKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }
}

// 缓存统计
class CacheStatistics {
  private stats = {
    l1: { hits: 0, misses: 0, totalTime: 0 },
    l2: { hits: 0, misses: 0, totalTime: 0 },
    l3: { hits: 0, misses: 0, totalTime: 0 },
    errors: 0,
    totalRequests: 0
  };
  
  recordHit(level: 'l1' | 'l2' | 'l3', responseTime: number): void {
    this.stats[level].hits++;
    this.stats[level].totalTime += responseTime;
    this.stats.totalRequests++;
  }
  
  recordMiss(responseTime: number): void {
    this.stats.l1.misses++;
    this.stats.l2.misses++;
    this.stats.l3.misses++;
    this.stats.totalRequests++;
  }
  
  recordError(error: Error): void {
    this.stats.errors++;
    console.error('Cache error:', error);
  }
  
  getHitRate(): CacheHitRates {
    const total = this.stats.totalRequests;
    
    return {
      l1: total > 0 ? this.stats.l1.hits / total : 0,
      l2: total > 0 ? this.stats.l2.hits / total : 0,
      l3: total > 0 ? this.stats.l3.hits / total : 0,
      overall: total > 0 ? 
        (this.stats.l1.hits + this.stats.l2.hits + this.stats.l3.hits) / total : 0
    };
  }
  
  getAverageResponseTime(): CacheResponseTimes {
    return {
      l1: this.stats.l1.hits > 0 ? this.stats.l1.totalTime / this.stats.l1.hits : 0,
      l2: this.stats.l2.hits > 0 ? this.stats.l2.totalTime / this.stats.l2.hits : 0,
      l3: this.stats.l3.hits > 0 ? this.stats.l3.totalTime / this.stats.l3.hits : 0
    };
  }
  
  reset(): void {
    this.stats = {
      l1: { hits: 0, misses: 0, totalTime: 0 },
      l2: { hits: 0, misses: 0, totalTime: 0 },
      l3: { hits: 0, misses: 0, totalTime: 0 },
      errors: 0,
      totalRequests: 0
    };
  }
}

// 智能缓存策略
class IntelligentCacheStrategy {
  private accessPatterns: Map<string, AccessPattern> = new Map();
  private cacheManager: MultiLevelCacheManager;
  
  constructor(cacheManager: MultiLevelCacheManager) {
    this.cacheManager = cacheManager;
  }
  
  async get<T>(key: string): Promise<T | null> {
    // 记录访问模式
    this.recordAccess(key);
    
    // 根据访问模式调整缓存策略
    const pattern = this.accessPatterns.get(key);
    if (pattern) {
      await this.adjustCacheStrategy(key, pattern);
    }
    
    return this.cacheManager.get<T>(key);
  }
  
  private recordAccess(key: string): void {
    const now = Date.now();
    let pattern = this.accessPatterns.get(key);
    
    if (!pattern) {
      pattern = {
        accessCount: 0,
        lastAccess: now,
        accessTimes: [],
        frequency: 0
      };
      this.accessPatterns.set(key, pattern);
    }
    
    pattern.accessCount++;
    pattern.accessTimes.push(now);
    pattern.lastAccess = now;
    
    // 保持最近100次访问记录
    if (pattern.accessTimes.length > 100) {
      pattern.accessTimes = pattern.accessTimes.slice(-100);
    }
    
    // 计算访问频率
    this.calculateFrequency(pattern);
  }
  
  private calculateFrequency(pattern: AccessPattern): void {
    if (pattern.accessTimes.length < 2) {
      pattern.frequency = 0;
      return;
    }
    
    const timeSpan = pattern.accessTimes[pattern.accessTimes.length - 1] - pattern.accessTimes[0];
    pattern.frequency = pattern.accessTimes.length / (timeSpan / 1000 / 60); // 每分钟访问次数
  }
  
  private async adjustCacheStrategy(key: string, pattern: AccessPattern): Promise<void> {
    // 高频访问的数据延长缓存时间
    if (pattern.frequency > 10) { // 每分钟超过10次
      await this.cacheManager.set(key, await this.cacheManager.get(key), 7200); // 2小时
    }
    
    // 预测性缓存：如果访问模式规律，提前刷新缓存
    if (this.isPredictablePattern(pattern)) {
      await this.preemptiveRefresh(key);
    }
  }
  
  private isPredictablePattern(pattern: AccessPattern): boolean {
    if (pattern.accessTimes.length < 5) return false;
    
    // 检查访问间隔是否规律
    const intervals = [];
    for (let i = 1; i < pattern.accessTimes.length; i++) {
      intervals.push(pattern.accessTimes[i] - pattern.accessTimes[i - 1]);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => 
      sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    
    // 如果方差小于平均值的20%，认为是规律访问
    return variance < avgInterval * 0.2;
  }
  
  private async preemptiveRefresh(key: string): Promise<void> {
    // 实现预测性缓存刷新逻辑
    // 这里可以根据业务逻辑从数据源重新获取数据
  }
}

// 类型定义
interface CacheConfig {
  l1: MemoryCacheConfig;
  l2: RedisCacheConfig;
  l3: DatabaseCacheConfig;
}

interface MemoryCacheConfig {
  maxSize: number;
  defaultTTL: number;
}

interface RedisCacheConfig {
  host: string;
  port: number;
  password?: string;
  database: number;
  keyPrefix?: string;
}

interface DatabaseCacheConfig {
  connectionString: string;
  tableName: string;
}

interface CacheHitRates {
  l1: number;
  l2: number;
  l3: number;
  overall: number;
}

interface CacheResponseTimes {
  l1: number;
  l2: number;
  l3: number;
}

interface AccessPattern {
  accessCount: number;
  lastAccess: number;
  accessTimes: number[];
  frequency: number;
}
```

## 10. 监控与日志

### 10.1 性能监控

#### 10.1.1 前端性能监控
```typescript
// 性能监控管理器
class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private observers: PerformanceObserver[] = [];
  private reportingInterval: number;
  private reportingTimer: NodeJS.Timeout | null = null;
  
  constructor(config: PerformanceMonitorConfig) {
    this.metrics = new PerformanceMetrics();
    this.reportingInterval = config.reportingInterval || 60000; // 1分钟
    this.init();
  }
  
  private init(): void {
    this.setupPerformanceObservers();
    this.monitorResourceLoading();
    this.monitorUserInteractions();
    this.monitorMemoryUsage();
    this.startReporting();
  }
  
  private setupPerformanceObservers(): void {
    // 监控导航时间
    if ('PerformanceObserver' in window) {
      const navigationObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
          }
        }
      });
      
      navigationObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navigationObserver);
      
      // 监控资源加载
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordResourceMetrics(entry as PerformanceResourceTiming);
        }
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
      
      // 监控长任务
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordLongTask(entry);
        }
      });
      
      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (e) {
        console.warn('Long task monitoring not supported');
      }
    }
  }
  
  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
      timeToInteractive: this.calculateTTI(entry),
      totalLoadTime: entry.loadEventEnd - entry.navigationStart
    };
    
    this.metrics.addNavigationMetrics(metrics);
  }
  
  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    const resourceMetric = {
      name: entry.name,
      type: this.getResourceType(entry.name),
      size: entry.transferSize || 0,
      loadTime: entry.responseEnd - entry.requestStart,
      cached: entry.transferSize === 0 && entry.decodedBodySize > 0
    };
    
    this.metrics.addResourceMetric(resourceMetric);
  }
  
  private recordLongTask(entry: PerformanceEntry): void {
    this.metrics.addLongTask({
      duration: entry.duration,
      startTime: entry.startTime,
      name: entry.name
    });
  }
  
  private monitorResourceLoading(): void {
    // 监控关键资源加载
    const criticalResources = [
      '/js/content-script.js',
      '/js/popup.js',
      '/css/styles.css'
    ];
    
    criticalResources.forEach(resource => {
      const startTime = performance.now();
      
      // 模拟资源加载监控
      const checkResource = () => {
        const entries = performance.getEntriesByName(resource);
        if (entries.length > 0) {
          const loadTime = performance.now() - startTime;
          this.metrics.addCriticalResourceMetric(resource, loadTime);
        }
      };
      
      setTimeout(checkResource, 100);
    });
  }
  
  private monitorUserInteractions(): void {
    // 监控用户交互响应时间
    const interactionEvents = ['click', 'keydown', 'scroll'];
    
    interactionEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        const startTime = performance.now();
        
        // 使用requestAnimationFrame来测量响应时间
        requestAnimationFrame(() => {
          const responseTime = performance.now() - startTime;
          this.metrics.addInteractionMetric(eventType, responseTime);
        });
      }, { passive: true });
    });
  }
  
  private monitorMemoryUsage(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory;
        this.metrics.addMemoryMetric({
          usedJSHeapSize: memInfo.usedJSHeapSize,
          totalJSHeapSize: memInfo.totalJSHeapSize,
          jsHeapSizeLimit: memInfo.jsHeapSizeLimit
        });
      }, 30000); // 每30秒检查一次
    }
  }
  
  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const fpEntry = paintEntries.find(entry => entry.name === 'first-paint');
    return fpEntry ? fpEntry.startTime : 0;
  }
  
  private getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcpEntry ? fcpEntry.startTime : 0;
  }
  
  private calculateTTI(entry: PerformanceNavigationTiming): number {
    // 简化的TTI计算
    return entry.domInteractive - entry.navigationStart;
  }
  
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.includes('.png') || url.includes('.jpg') || url.includes('.gif')) return 'image';
    return 'other';
  }
  
  private startReporting(): void {
    this.reportingTimer = setInterval(() => {
      this.reportMetrics();
    }, this.reportingInterval);
  }
  
  private async reportMetrics(): Promise<void> {
    const report = this.metrics.generateReport();
    
    try {
      // 发送到监控服务
      await this.sendToMonitoringService(report);
      
      // 重置指标
      this.metrics.reset();
    } catch (error) {
      console.error('Failed to report metrics:', error);
    }
  }
  
  private async sendToMonitoringService(report: PerformanceReport): Promise<void> {
    // 发送到监控服务的实现
    await fetch('/api/v1/metrics/performance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(report)
    });
  }
  
  // 手动记录自定义指标
  recordCustomMetric(name: string, value: number, tags?: Record<string, string>): void {
    this.metrics.addCustomMetric(name, value, tags);
  }
  
  // 记录错误
  recordError(error: Error, context?: string): void {
    this.metrics.addError({
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  }
````markdown path=docs/Architecture_Design_Document.md mode=EDIT
  
  // 获取当前性能快照
  getPerformanceSnapshot(): PerformanceSnapshot {
    return {
      navigation: this.metrics.getNavigationSummary(),
      resources: this.metrics.getResourceSummary(),
      interactions: this.metrics.getInteractionSummary(),
      memory: this.metrics.getMemorySummary(),
      errors: this.metrics.getErrorSummary(),
      timestamp: Date.now()
    };
  }
  
  destroy(): void {
    // 清理观察者
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    // 清理定时器
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
      this.reportingTimer = null;
    }
  }
}

// 性能指标收集器
class PerformanceMetrics {
  private navigationMetrics: NavigationMetric[] = [];
  private resourceMetrics: ResourceMetric[] = [];
  private interactionMetrics: InteractionMetric[] = [];
  private memoryMetrics: MemoryMetric[] = [];
  private longTasks: LongTaskMetric[] = [];
  private customMetrics: CustomMetric[] = [];
  private errors: ErrorMetric[] = [];
  
  addNavigationMetrics(metrics: NavigationMetric): void {
    this.navigationMetrics.push({
      ...metrics,
      timestamp: Date.now()
    });
  }
  
  addResourceMetric(metric: ResourceMetric): void {
    this.resourceMetrics.push({
      ...metric,
      timestamp: Date.now()
    });
  }
  
  addInteractionMetric(type: string, responseTime: number): void {
    this.interactionMetrics.push({
      type,
      responseTime,
      timestamp: Date.now()
    });
  }
  
  addMemoryMetric(metric: MemoryMetric): void {
    this.memoryMetrics.push({
      ...metric,
      timestamp: Date.now()
    });
  }
  
  addLongTask(task: LongTaskMetric): void {
    this.longTasks.push({
      ...task,
      timestamp: Date.now()
    });
  }
  
  addCustomMetric(name: string, value: number, tags?: Record<string, string>): void {
    this.customMetrics.push({
      name,
      value,
      tags: tags || {},
      timestamp: Date.now()
    });
  }
  
  addError(error: ErrorMetric): void {
    this.errors.push(error);
  }
  
  addCriticalResourceMetric(resource: string, loadTime: number): void {
    this.addCustomMetric('critical_resource_load_time', loadTime, { resource });
  }
  
  generateReport(): PerformanceReport {
    return {
      navigation: this.getNavigationSummary(),
      resources: this.getResourceSummary(),
      interactions: this.getInteractionSummary(),
      memory: this.getMemorySummary(),
      longTasks: this.getLongTaskSummary(),
      custom: this.getCustomMetricsSummary(),
      errors: this.getErrorSummary(),
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
  }
  
  getNavigationSummary(): NavigationSummary {
    if (this.navigationMetrics.length === 0) {
      return { count: 0, averages: {} };
    }
    
    const latest = this.navigationMetrics[this.navigationMetrics.length - 1];
    return {
      count: this.navigationMetrics.length,
      averages: {
        domContentLoaded: latest.domContentLoaded,
        loadComplete: latest.loadComplete,
        firstPaint: latest.firstPaint,
        firstContentfulPaint: latest.firstContentfulPaint,
        timeToInteractive: latest.timeToInteractive,
        totalLoadTime: latest.totalLoadTime
      }
    };
  }
  
  getResourceSummary(): ResourceSummary {
    const byType = this.groupResourcesByType();
    const totalSize = this.resourceMetrics.reduce((sum, r) => sum + r.size, 0);
    const averageLoadTime = this.resourceMetrics.length > 0 
      ? this.resourceMetrics.reduce((sum, r) => sum + r.loadTime, 0) / this.resourceMetrics.length
      : 0;
    
    return {
      totalCount: this.resourceMetrics.length,
      totalSize,
      averageLoadTime,
      byType,
      cacheHitRate: this.calculateCacheHitRate()
    };
  }
  
  getInteractionSummary(): InteractionSummary {
    const byType = this.groupInteractionsByType();
    const totalInteractions = this.interactionMetrics.length;
    const averageResponseTime = totalInteractions > 0
      ? this.interactionMetrics.reduce((sum, i) => sum + i.responseTime, 0) / totalInteractions
      : 0;
    
    return {
      totalCount: totalInteractions,
      averageResponseTime,
      byType
    };
  }
  
  getMemorySummary(): MemorySummary {
    if (this.memoryMetrics.length === 0) {
      return { samples: 0, current: {}, peak: {}, average: {} };
    }
    
    const latest = this.memoryMetrics[this.memoryMetrics.length - 1];
    const peak = this.calculatePeakMemory();
    const average = this.calculateAverageMemory();
    
    return {
      samples: this.memoryMetrics.length,
      current: {
        usedJSHeapSize: latest.usedJSHeapSize,
        totalJSHeapSize: latest.totalJSHeapSize,
        jsHeapSizeLimit: latest.jsHeapSizeLimit
      },
      peak,
      average
    };
  }
  
  getLongTaskSummary(): LongTaskSummary {
    const totalTasks = this.longTasks.length;
    const totalDuration = this.longTasks.reduce((sum, task) => sum + task.duration, 0);
    const averageDuration = totalTasks > 0 ? totalDuration / totalTasks : 0;
    const maxDuration = totalTasks > 0 ? Math.max(...this.longTasks.map(t => t.duration)) : 0;
    
    return {
      count: totalTasks,
      totalDuration,
      averageDuration,
      maxDuration
    };
  }
  
  getCustomMetricsSummary(): CustomMetricsSummary {
    const grouped = this.groupCustomMetrics();
    const summary: CustomMetricsSummary = {};
    
    for (const [name, metrics] of Object.entries(grouped)) {
      const values = metrics.map(m => m.value);
      summary[name] = {
        count: values.length,
        sum: values.reduce((a, b) => a + b, 0),
        average: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values)
      };
    }
    
    return summary;
  }
  
  getErrorSummary(): ErrorSummary {
    const grouped = this.groupErrorsByType();
    
    return {
      totalCount: this.errors.length,
      byType: grouped,
      recent: this.errors.slice(-10) // 最近10个错误
    };
  }
  
  private groupResourcesByType(): Record<string, ResourceTypeSummary> {
    const grouped: Record<string, ResourceMetric[]> = {};
    
    this.resourceMetrics.forEach(resource => {
      if (!grouped[resource.type]) {
        grouped[resource.type] = [];
      }
      grouped[resource.type].push(resource);
    });
    
    const summary: Record<string, ResourceTypeSummary> = {};
    for (const [type, resources] of Object.entries(grouped)) {
      summary[type] = {
        count: resources.length,
        totalSize: resources.reduce((sum, r) => sum + r.size, 0),
        averageLoadTime: resources.reduce((sum, r) => sum + r.loadTime, 0) / resources.length
      };
    }
    
    return summary;
  }
  
  private groupInteractionsByType(): Record<string, InteractionTypeSummary> {
    const grouped: Record<string, InteractionMetric[]> = {};
    
    this.interactionMetrics.forEach(interaction => {
      if (!grouped[interaction.type]) {
        grouped[interaction.type] = [];
      }
      grouped[interaction.type].push(interaction);
    });
    
    const summary: Record<string, InteractionTypeSummary> = {};
    for (const [type, interactions] of Object.entries(grouped)) {
      const responseTimes = interactions.map(i => i.responseTime);
      summary[type] = {
        count: interactions.length,
        averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        maxResponseTime: Math.max(...responseTimes),
        minResponseTime: Math.min(...responseTimes)
      };
    }
    
    return summary;
  }
  
  private groupCustomMetrics(): Record<string, CustomMetric[]> {
    const grouped: Record<string, CustomMetric[]> = {};
    
    this.customMetrics.forEach(metric => {
      if (!grouped[metric.name]) {
        grouped[metric.name] = [];
      }
      grouped[metric.name].push(metric);
    });
    
    return grouped;
  }
  
  private groupErrorsByType(): Record<string, number> {
    const grouped: Record<string, number> = {};
    
    this.errors.forEach(error => {
      const type = error.context || 'unknown';
      grouped[type] = (grouped[type] || 0) + 1;
    });
    
    return grouped;
  }
  
  private calculateCacheHitRate(): number {
    const cachedResources = this.resourceMetrics.filter(r => r.cached).length;
    return this.resourceMetrics.length > 0 ? cachedResources / this.resourceMetrics.length : 0;
  }
  
  private calculatePeakMemory(): MemoryUsage {
    const usedHeapSizes = this.memoryMetrics.map(m => m.usedJSHeapSize);
    const totalHeapSizes = this.memoryMetrics.map(m => m.totalJSHeapSize);
    
    return {
      usedJSHeapSize: Math.max(...usedHeapSizes),
      totalJSHeapSize: Math.max(...totalHeapSizes),
      jsHeapSizeLimit: this.memoryMetrics[0]?.jsHeapSizeLimit || 0
    };
  }
  
  private calculateAverageMemory(): MemoryUsage {
    const count = this.memoryMetrics.length;
    if (count === 0) return { usedJSHeapSize: 0, totalJSHeapSize: 0, jsHeapSizeLimit: 0 };
    
    return {
      usedJSHeapSize: this.memoryMetrics.reduce((sum, m) => sum + m.usedJSHeapSize, 0) / count,
      totalJSHeapSize: this.memoryMetrics.reduce((sum, m) => sum + m.totalJSHeapSize, 0) / count,
      jsHeapSizeLimit: this.memoryMetrics[0]?.jsHeapSizeLimit || 0
    };
  }
  
  private getSessionId(): string {
    // 生成或获取会话ID
    let sessionId = sessionStorage.getItem('performance_session_id');
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2, 15);
      sessionStorage.setItem('performance_session_id', sessionId);
    }
    return sessionId;
  }
  
  reset(): void {
    this.navigationMetrics = [];
    this.resourceMetrics = [];
    this.interactionMetrics = [];
    this.memoryMetrics = [];
    this.longTasks = [];
    this.customMetrics = [];
    this.errors = [];
  }
}

// 类型定义
interface PerformanceMonitorConfig {
  reportingInterval?: number;
  enableResourceMonitoring?: boolean;
  enableInteractionMonitoring?: boolean;
  enableMemoryMonitoring?: boolean;
}

interface NavigationMetric {
  domContentLoaded: number;
  loadComplete: number;
  firstPaint: number;
  firstContentfulPaint: number;
  timeToInteractive: number;
  totalLoadTime: number;
  timestamp?: number;
}

interface ResourceMetric {
  name: string;
  type: string;
  size: number;
  loadTime: number;
  cached: boolean;
  timestamp?: number;
}

interface InteractionMetric {
  type: string;
  responseTime: number;
  timestamp: number;
}

interface MemoryMetric {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp?: number;
}

interface LongTaskMetric {
  duration: number;
  startTime: number;
  name: string;
  timestamp?: number;
}

interface CustomMetric {
  name: string;
  value: number;
  tags: Record<string, string>;
  timestamp: number;
}

interface ErrorMetric {
  message: string;
  stack?: string;
  context?: string;
  timestamp: number;
  userAgent: string;
  url: string;
}

interface PerformanceReport {
  navigation: NavigationSummary;
  resources: ResourceSummary;
  interactions: InteractionSummary;
  memory: MemorySummary;
  longTasks: LongTaskSummary;
  custom: CustomMetricsSummary;
  errors: ErrorSummary;
  timestamp: number;
  sessionId: string;
  userAgent: string;
  url: string;
}

interface NavigationSummary {
  count: number;
  averages: Partial<NavigationMetric>;
}

interface ResourceSummary {
  totalCount: number;
  totalSize: number;
  averageLoadTime: number;
  byType: Record<string, ResourceTypeSummary>;
  cacheHitRate: number;
}

interface ResourceTypeSummary {
  count: number;
  totalSize: number;
  averageLoadTime: number;
}

interface InteractionSummary {
  totalCount: number;
  averageResponseTime: number;
  byType: Record<string, InteractionTypeSummary>;
}

interface InteractionTypeSummary {
  count: number;
  averageResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
}

interface MemorySummary {
  samples: number;
  current: MemoryUsage;
  peak: MemoryUsage;
  average: MemoryUsage;
}

interface MemoryUsage {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

interface LongTaskSummary {
  count: number;
  totalDuration: number;
  averageDuration: number;
  maxDuration: number;
}

interface CustomMetricsSummary {
  [metricName: string]: {
    count: number;
    sum: number;
    average: number;
    min: number;
    max: number;
  };
}

interface ErrorSummary {
  totalCount: number;
  byType: Record<string, number>;
  recent: ErrorMetric[];
}

interface PerformanceSnapshot {
  navigation: NavigationSummary;
  resources: ResourceSummary;
  interactions: InteractionSummary;
  memory: MemorySummary;
  errors: ErrorSummary;
  timestamp: number;
}
```

#### 10.1.2 后端性能监控
```typescript
// 后端性能监控器
class BackendPerformanceMonitor {
  private metrics: BackendMetrics;
  private httpMetrics: HttpMetrics;
  private databaseMetrics: DatabaseMetrics;
  private systemMetrics: SystemMetrics;
  private alertManager: AlertManager;
  
  constructor(config: BackendMonitorConfig) {
    this.metrics = new BackendMetrics();
    this.httpMetrics = new HttpMetrics();
    this.databaseMetrics = new DatabaseMetrics();
    this.systemMetrics = new SystemMetrics();
    this.alertManager = new AlertManager(config.alerts);
    
    this.init();
  }
  
  private init(): void {
    this.setupHttpMonitoring();
    this.setupDatabaseMonitoring();
    this.setupSystemMonitoring();
    this.startMetricsCollection();
  }
  
  private setupHttpMonitoring(): void {
    // Express中间件示例
    this.httpMiddleware = (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const startCpuUsage = process.cpuUsage();
      
      // 记录请求开始
      this.httpMetrics.recordRequestStart(req);
      
      // 监听响应完成
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        const cpuUsage = process.cpuUsage(startCpuUsage);
        
        this.httpMetrics.recordRequestEnd({
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration,
          cpuTime: cpuUsage.user + cpuUsage.system,
          contentLength: parseInt(res.get('content-length') || '0'),
          userAgent: req.get('user-agent') || '',
          ip: req.ip
        });
        
        // 检查是否需要告警
        this.checkHttpAlerts(duration, res.statusCode);
      });
      
      next();
    };
  }
  
  private setupDatabaseMonitoring(): void {
    // 数据库查询监控装饰器
    this.queryMonitorDecorator = (originalQuery: Function) => {
      return async (...args: any[]) => {
        const startTime = Date.now();
        const queryText = args[0];
        
        try {
          const result = await originalQuery.apply(this, args);
          const duration = Date.now() - startTime;
          
          this.databaseMetrics.recordQuery({
            query: this.sanitizeQuery(queryText),
            duration,
            success: true,
            rowCount: result.rowCount || 0
          });
          
          // 检查慢查询
          if (duration > 1000) { // 超过1秒
            this.alertManager.triggerAlert('slow_query', {
              query: queryText,
              duration,
              timestamp: new Date()
            });
          }
          
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          
          this.databaseMetrics.recordQuery({
            query: this.sanitizeQuery(queryText),
            duration,
            success: false,
            error: error.message
          });
          
          throw error;
        }
      };
    };
  }
  
  private setupSystemMonitoring(): void {
    // 系统资源监控
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // 每30秒收集一次
  }
  
  private async collectSystemMetrics(): Promise<void> {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    // 获取系统信息
    const systemInfo = {
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: process.uptime(),
      loadAverage: os.loadavg(),
      freeMemory: os.freemem(),
      totalMemory: os.totalmem()
    };
    
    this.systemMetrics.record(systemInfo);
    
    // 检查系统资源告警
    this.checkSystemAlerts(systemInfo);
  }
  
  private checkHttpAlerts(duration: number, statusCode: number): void {
    // 响应时间告警
    if (duration > 5000) { // 超过5秒
      this.alertManager.triggerAlert('slow_response', {
        duration,
        statusCode,
        timestamp: new Date()
      });
    }
    
    // 错误率告警
    if (statusCode >= 500) {
      this.alertManager.triggerAlert('server_error', {
        statusCode,
        timestamp: new Date()
      });
    }
  }
  
  private checkSystemAlerts(systemInfo: any): void {
    // 内存使用率告警
    const memoryUsagePercent = (systemInfo.memory.heapUsed / systemInfo.memory.heapTotal) * 100;
    if (memoryUsagePercent > 90) {
      this.alertManager.triggerAlert('high_memory_usage', {
        usage: memoryUsagePercent,
        timestamp: new Date()
      });
    }
    
    // CPU负载告警
    const avgLoad = systemInfo.loadAverage[0];
    const cpuCount = os.cpus().length;
    if (avgLoad > cpuCount * 0.8) {
      this.alertManager.triggerAlert('high_cpu_load', {
        load: avgLoad,
        cpuCount,
        timestamp: new Date()
      });
    }
  }
  
  private sanitizeQuery(query: string): string {
    // 移除敏感信息，只保留查询结构
    return query.replace(/\$\d+/g, '?').substring(0, 200);
  }
  
  private startMetricsCollection(): void {
    // 定期生成性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 60000); // 每分钟生成一次报告
  }
  
  private async generatePerformanceReport(): Promise<void> {
    const report = {
      timestamp: new Date(),
      http: this.httpMetrics.getSummary(),
      database: this.databaseMetrics.getSummary(),
      system: this.systemMetrics.getSummary(),
      alerts: this.alertManager.getRecentAlerts()
    };
    
    // 发送到监控服务
    await this.sendToMonitoringService(report);
    
    // 重置指标
    this.resetMetrics();
  }
  
  private async sendToMonitoringService(report: any): Promise<void> {
    try {
      // 发送到外部监控服务（如Prometheus、DataDog等）
      await fetch('http://monitoring-service/api/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(report)
      });
    } catch (error) {
      console.error('Failed to send metrics to monitoring service:', error);
    }
  }
  
  private resetMetrics(): void {
    this.httpMetrics.reset();
    this.databaseMetrics.reset();
    this.systemMetrics.reset();
  }
  
  // 获取实时指标
  getRealTimeMetrics(): RealTimeMetrics {
    return {
      http: this.httpMetrics.getRealTimeStats(),
      database: this.databaseMetrics.getRealTimeStats(),
      system: this.systemMetrics.getCurrentStats(),
      timestamp: new Date()
    };
  }
  
  // 获取健康状态
  getHealthStatus(): HealthStatus {
    const httpHealth = this.httpMetrics.getHealthStatus();
    const dbHealth = this.databaseMetrics.getHealthStatus();
    const systemHealth = this.systemMetrics.getHealthStatus();
    
    const overall = httpHealth.status === 'healthy' && 
                   dbHealth.status === 'healthy' && 
                   systemHealth.status === 'healthy' ? 'healthy' : 'unhealthy';
    
    return {
      overall,
      components: {
        http: httpHealth,
        database: dbHealth,
        system: systemHealth
      },
      timestamp: new Date()
    };
  }
}

// HTTP指标收集器
class HttpMetrics {
  private requests: RequestMetric[] = [];
  private activeRequests = 0;
  private totalRequests = 0;
  private errorCount = 0;
  
  recordRequestStart(req: Request): void {
    this.activeRequests++;
    this.totalRequests++;
  }
  
  recordRequestEnd(metric: RequestMetric): void {
    this.activeRequests--;
    this.requests.push({
      ...metric,
      timestamp: new Date()
    });
    
    if (metric.statusCode >= 400) {
      this.errorCount++;
    }
    
    // 保持最近1000个请求记录
    if (this.requests.length > 1000) {
      this.requests = this.requests.slice(-1000);
    }
  }
  
  getSummary(): HttpMetricsSummary {
    const recentRequests = this.getRecentRequests(300000); // 最近5分钟
    const totalDuration = recentRequests.reduce((sum, r) => sum + r.duration, 0);
    const avgResponseTime = recentRequests.length > 0 ? totalDuration / recentRequests.length : 0;
    
    const statusCodes = this.groupByStatusCode(recentRequests);
    const endpoints = this.groupByEndpoint(recentRequests);
    
    return {
      totalRequests: this.totalRequests,
      activeRequests: this.activeRequests,
      errorRate: this.totalRequests > 0 ? this.errorCount / this.totalRequests : 0,
      averageResponseTime: avgResponseTime,
      requestsPerMinute: recentRequests.length / 5,
      statusCodes,
      topEndpoints: endpoints.slice(0, 10)
    };
  }
  
  getRealTimeStats(): HttpRealTimeStats {
    const last1min = this.getRecentRequests(60000);
    const last5min = this.getRecentRequests(300000);
    
    return {
      activeRequests: this.activeRequests,
      requestsLast1Min: last1min.length,
      requestsLast5Min: last5min.length,
      avgResponseTimeLast1Min: this.calculateAvgResponseTime(last1min),
      avgResponseTimeLast5Min: this.calculateAvgResponseTime(last5min),
      errorRateLast5Min: this.calculateErrorRate(last5min)
    };
  }
  
  getHealthStatus(): ComponentHealth {
    const recentRequests = this.getRecentRequests(300000);
    const errorRate = this.calculateErrorRate(recentRequests);
    const avgResponseTime = this.calculateAvgResponseTime(recentRequests);
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    const issues: string[] = [];
    
    if (errorRate > 0.1) { // 错误率超过10%
      status = 'critical';
      issues.push(`High error rate: ${(errorRate * 100).toFixed(2)}%`);
    } else if (errorRate > 0.05) { // 错误率超过5%
      status = 'warning';
      issues.push(`Elevated error rate: ${(errorRate * 100).toFixed(2)}%`);
    }
    
    if (avgResponseTime > 2000) { // 平均响应时间超过2秒
      status = status === 'critical' ? 'critical' : 'warning';
      issues.push(`Slow response time: ${avgResponseTime.toFixed(0)}ms`);
    }
    
    return { status, issues };
  }
  
  private getRecentRequests(timeWindow: number): RequestMetric[] {
    const cutoff = Date.now() - timeWindow;
    return this.requests.filter(r => r.timestamp && r.timestamp.getTime() > cutoff);
  }
  
  private groupByStatusCode(requests: RequestMetric[]): Record<string, number> {
    const grouped: Record<string, number> = {};
    requests.forEach(r => {
      const code = Math.floor(r.statusCode / 100) * 100; // 2xx, 3xx, 4xx, 5xx
      const key = `${code}xx`;
      grouped[key] = (grouped[key] || 0) + 1;
    });
    return grouped;
  }
  
  private groupByEndpoint(requests: RequestMetric[]): EndpointMetric[] {
    const grouped: Record<string, RequestMetric[]> = {};
    
    requests.forEach(r => {
      const key = `${r.method} ${r.path}`;
      if (!grouped[key]) grouped[key] = [];
      grouped[key].push(r);
    });
    
    return Object.entries(grouped)
      .map(([endpoint, reqs]) => ({
        endpoint,
        count: reqs.length,
        averageResponseTime: this.calculateAvgResponseTime(reqs),
        errorRate: this.calculateErrorRate(reqs)
      }))
      .sort((a, b) => b.count - a.count);
  }
  
  private calculateAvgResponseTime(requests: RequestMetric[]): number {
    if (requests.length === 0) return 0;
    const total = requests.reduce((sum, r) => sum + r.duration, 0);
    return total / requests.length;
  }
  
  private calculateErrorRate(requests: RequestMetric[]): number {
    if (requests.length === 0) return 0;
    const errors = requests.filter(r => r.statusCode >= 400).length;
    return errors / requests.length;
  }
  
  reset(): void {
    this.requests = [];
    this.errorCount = 0;
    // 保留totalRequests和activeRequests用于长期统计
  }
}

// 数据库指标收集器
class DatabaseMetrics {
  private queries: QueryMetric[] = [];
  private activeQueries = 0;
  private totalQueries = 0;
  private failedQueries = 0;
  
  recordQuery(metric: QueryMetric): void {
    this.totalQueries++;
    if (!metric.success) {
      this.failedQueries++;
    }
    
    this.queries.push({
      ...metric,
      timestamp: new Date()
    });
    
    // 保持最近1000个查询记录
    if (this.queries.length > 1000) {
      this.queries = this.queries.slice(-1000);
    }
  }
  
  getSummary(): DatabaseMetricsSummary {
    const recentQueries = this.getRecentQueries(300000); // 最近5分钟
    const totalDuration = recentQueries.reduce((sum, q) => sum + q.duration, 0);
    const avgQueryTime = recentQueries.length > 0 ? totalDuration / recentQueries.length : 0;
    
    const slowQueries = recentQueries.filter(q => q.duration > 1000);
    const failedQueries = recentQueries.filter(q => !q.success);
    
    return {
      totalQueries: this.totalQueries,
      activeQueries: this.activeQueries,
      failureRate: this.totalQueries > 0 ? this.failedQueries / this.totalQueries : 0,
      averageQueryTime: avgQueryTime,
      queriesPerMinute: recentQueries.length / 5,
      slowQueryCount: slowQueries.length,
      recentFailures: failedQueries.slice(-10)
    };
  }
  
  getRealTimeStats(): DatabaseRealTimeStats {
    const last1min = this.getRecentQueries(60000);
    const last5min = this.getRecentQueries(300000);
    
    return {
      activeQueries: this.activeQueries,
      queriesLast1Min: last1min.length,
      queriesLast5Min: last5min.length,
      avgQueryTimeLast1Min: this.calculateAvgQueryTime(last1min),
      avgQueryTimeLast5Min: this.calculateAvgQueryTime(last5min),
      slowQueriesLast5Min: last5min.filter(q => q.duration > 1000).length
    };
  }
  
  getHealthStatus(): ComponentHealth {
    const recentQueries = this.getRecentQueries(300000);
    const failureRate = this.calculateFailureRate(recentQueries);
    const avgQueryTime = this.calculateAvgQueryTime(recentQueries);
    const slowQueryRate = recentQueries.filter(q => q.duration > 1000).length / recentQueries.length;
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    const issues: string[] = [];
    
    if (failureRate > 0.05) { // 失败率超过5%
      status = 'critical';
      issues.push(`High failure rate: ${(failureRate * 100).toFixed(2)}%`);
    }
    
    if (slowQueryRate > 0.1) { // 慢查询率超过10%
      status = status === 'critical' ? 'critical' : 'warning';
      issues.push(`High slow query rate: ${(slowQueryRate * 100).toFixed(2)}%`);
    }
    
    if (avgQueryTime > 500) { // 平均查询时间超过500ms
      status = status === 'critical' ? 'critical' : 'warning';
      issues.push(`Slow average query time: ${avgQueryTime.toFixed(0)}ms`);
    }
    
    return { status, issues };
  }
  
  private getRecentQueries(timeWindow: number): QueryMetric[] {
    const cutoff = Date.now() - timeWindow;
    return this.queries.filter(q => q.timestamp && q.timestamp.getTime() > cutoff);
  }
  
  private calculateAvgQueryTime(queries: QueryMetric[]): number {
    if (queries.length === 0) return 0;
    const total = queries.reduce((sum, q) => sum + q.duration, 0);
    return total / queries.length;
  }
  
  private calculateFailureRate(queries: QueryMetric[]): number {
    if (queries.length === 0) return 0;
    const failures = queries.filter(q => !q.success).length;
    return failures / queries.length;
  }
  
  reset(): void {
    this.queries = [];
    this.failedQueries = 0;
  }
}

// 告警管理器
class AlertManager {
  private alerts: Alert[] = [];
  private alertRules: AlertRule[];
  private notificationChannels: NotificationChannel[];
  
  constructor(config: AlertConfig) {
    this.alertRules = config.rules || [];
    this.notificationChannels = config.channels || [];
  }
  
  triggerAlert(type: string, data: any): void {
    const rule = this.alertRules.find(r => r.type === type);
    if (!rule) return;
    
    const alert: Alert = {
      id: this.generateAlertId(),
      type,
      severity: rule.severity,
      message: this.formatAlertMessage(rule.template, data),
      data,
      timestamp: new Date(),
      status: 'active'
    };
    
    this.alerts.push(alert);
    
    // 发送通知
    this.sendNotifications(alert);
    
    // 保持最近100个告警
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }
  
  private async sendNotifications(alert: Alert): Promise<void> {
    const applicableChannels = this.notificationChannels.filter(
      channel => channel.severities.includes(alert.severity)
    );
    
    const notifications = applicableChannels.map(channel => 
      this.sendToChannel(channel, alert)
    );
    
    await Promise.allSettled(notifications);
  }
  
  private async sendToChannel(channel: NotificationChannel, alert: Alert): Promise<void> {
    try {
      switch (channel.type) {
        case 'email':
          await this.sendEmailNotification(channel, alert);
          break;
        case 'slack':
          await this.sendSlackNotification(channel, alert);
          break;
        case 'webhook':
          await this.sendWebhookNotification(channel, alert);
          break;
      }
    } catch (error) {
      console.error(`Failed to send notification to ${channel.type}:`, error);
    }
  }
  
  private async sendEmailNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // 实现邮件通知
  }
  
  private async sendSlackNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // 实现Slack通知
  }
  
  private async sendWebhookNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    await fetch(channel.url!, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alert)
    });
  }
  
  private formatAlertMessage(template: string, data: any): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return data[key] || match;
    });
  }
  
  private generateAlertId(): string {
    return Math.random().toString(36).substring(2, 15);
  }
  
  getRecentAlerts(limit: number = 10): Alert[] {
    return this.alerts.slice(-limit);
  }
  
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(a => a.status === 'active');
  }
  
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.status = 'resolved';
      alert.resolvedAt = new Date();
    }
  }
}

// 类型定义
interface BackendMonitorConfig {
  alerts: AlertConfig;
  metrics: {
    http: boolean;
    database: boolean;
    system: boolean;
  };
}

interface AlertConfig {
  rules: AlertRule[];
  channels: NotificationChannel[];
}

interface AlertRule {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  template: string;
  threshold?: number;
}

interface NotificationChannel {
  type: 'email' | 'slack' | 'webhook';
  severities: string[];
  url?: string;
  recipients?: string[];
}

interface Alert {
  id: string;
  type: string;
  severity: string;
  message: string;
  data: any;
  timestamp: Date;
  status: 'active' | 'resolved';
  resolvedAt?: Date;
}

interface RequestMetric {
  method: string;
  path: string;
  statusCode: number;
  duration: number;
  cpuTime: number;
  contentLength: number;
  userAgent: string;
  ip: string;
  timestamp?: Date;
}

interface QueryMetric {
  query: string;
  duration: number;
  success: boolean;
  rowCount?: number;
  error?: string;
  timestamp?: Date;
}

interface HttpMetricsSummary {
  totalRequests: number;
  activeRequests: number;
  errorRate: number;
  averageResponseTime: number;
  requestsPerMinute: number;
  statusCodes: Record<string, number>;
  topEndpoints: EndpointMetric[];
}

interface DatabaseMetricsSummary {
  totalQueries: number;
  activeQueries: number;
  failureRate: number;
  averageQueryTime: number;
  queriesPerMinute: number;
  slowQueryCount: number;
  recentFailures: QueryMetric[];
}

interface EndpointMetric {
  endpoint: string;
  count: number;
  averageResponseTime: number;
  errorRate: number;
}

interface HttpRealTimeStats {
  activeRequests: number;
  requestsLast1Min: number;
  requestsLast5Min: number;
  avgResponseTimeLast1Min: number;
  avgResponseTimeLast5Min: number;
  errorRateLast5Min: number;
}

interface DatabaseRealTimeStats {
  activeQueries: number;
  queriesLast1Min: number;
  queriesLast5Min: number;
  avgQueryTimeLast1Min: number;
  avgQueryTimeLast5Min: number;
  slowQueriesLast5Min: number;
}

interface ComponentHealth {
  status: 'healthy' | 'warning' | 'critical';
  issues: string[];
}

interface HealthStatus {
  overall: 'healthy' | 'unhealthy';
  components: {
    http: ComponentHealth;
    database: ComponentHealth;
    system: ComponentHealth;
  };
  timestamp: Date;
}

interface RealTimeMetrics {
  http: HttpRealTimeStats;
  database: DatabaseRealTimeStats;
  system: any;
  timestamp: Date;
}
```

### 10.2 日志系统

#### 10.2.1 结构化日志
```typescript
// 结构化日志系统
class StructuredLogger {
  private logLevel: LogLevel;
  private outputs: LogOutput[];
  private context: LogContext;
  private formatters: Map<string, LogFormatter>;
  
  constructor(config: LoggerConfig) {
    this.logLevel = config.level || LogLevel.INFO;
    this.outputs = config.outputs || [new ConsoleOutput()];
    this.context = config.context || {};
    this.formatters = new Map();
    
    this.setupFormatters();
  }
  
  private setupFormatters(): void {
    this.formatters.set('json', new JsonFormatter());
    this.formatters.set('text', new TextFormatter());
    this.formatters.set('structured', new StructuredFormatter());
  }
  
  // 基础日志方法
  debug(message: string, meta?: LogMeta): void {
    this.log(LogLevel.DEBUG, message, meta);
  }
  
  info(message: string, meta?: LogMeta): void {
    this.log(LogLevel.INFO, message, meta);
  }
  
  warn(message: string, meta?: LogMeta): void {
    this.log(LogLevel.WARN, message, meta);
  }
  
  error(message: string, error?: Error, meta?: LogMeta): void {
    const errorMeta = error ? {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } : {};
    
    this.log(LogLevel.ERROR, message, { ...meta, ...errorMeta });
  }
  
  fatal(message: string, error?: Error, meta?: LogMeta): void {
    const errorMeta = error ? {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } : {};
    
    this.log(LogLevel.FATAL, message, { ...meta, ...errorMeta });
  }
  
  private log(level: LogLevel, message: string, meta: LogMeta = {}): void {
    if (level < this.logLevel) return;
    
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level: LogLevel[level],
      message,
      meta: { ...this.context, ...meta },
      traceId: this.generateTraceId(),
      service: 'reading-assistant',
      version: process.env.APP_VERSION || '1.0.0'
    };
    
    this.outputs.forEach(output => {
      try {
        output.write(logEntry);
      } catch (error) {
        console.error('Failed to write log:', error);
      }
    });
  }
  
  // 创建子日志器
  child(context: LogContext): StructuredLogger {
    return new StructuredLogger({
      level: this.logLevel,
      outputs: this.outputs,
      context: { ...this.context, ...context }
    });
  }
  
  // 性能日志
  performance(operation: string, duration: number, meta?: LogMeta): void {
    this.info(`Performance: ${operation}`, {
      ...meta,
      performance: {
        operation,
        duration,
        slow: duration > 1000
      }
    });
  }
  
  // 安全日志
  security(event: string, meta?: LogMeta): void {
    this.warn(`Security: ${event}`, {
      ...meta,
      security: true,
      severity: 'high'
    });
  }
  
  // 业务日志
  business(event: string, meta?: LogMeta): void {
    this.info(`Business: ${event}`, {
      ...meta,
      business: true
    });
  }
  
  // 审计日志
  audit(action: string, user: string, resource: string, meta?: LogMeta): void {
    this.info(`Audit: ${action}`, {
      ...meta,
      audit: {
        action,
        user,
        resource,
        timestamp: new Date().toISOString()
      }
    });
  }
  
  private generateTraceId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
  
  // 设置日志级别
  setLevel(level: LogLevel): void {
    this.logLevel = level;
  }
  
  // 添加输出
  addOutput(output: LogOutput): void {
    this.outputs.push(output);
  }
  
  // 移除输出
  removeOutput(output: LogOutput): void {
    const index = this.outputs.indexOf(output);
    if (index > -1) {
      this.outputs.splice(index, 1);
    }
  }
}

// 日志输出接口
abstract class LogOutput {
  protected formatter: LogFormatter;
  
  constructor(formatter: LogFormatter) {
    this.formatter = formatter;
  }
  
  abstract write(entry: LogEntry): void;
  
  setFormatter(formatter: LogFormatter): void {
    this.formatter = formatter;
  }
}

// 控制台输出
class ConsoleOutput extends LogOutput {
  constructor() {
    super(new TextFormatter());
  }
  
  write(entry: LogEntry): void {
    const formatted = this.formatter.format(entry);
    
    switch (entry.level) {
      case 'DEBUG':
        console.debug(formatted);
        break;
      case 'INFO':
        console.info(formatted);
        break;
      case 'WARN':
        console.warn(formatted);
        break;
      case 'ERROR':
      case 'FATAL':
        console.error(formatted);
        break;
      default:
        console.log(formatted);
    }
  }
}

// 文件输出
class FileOutput extends LogOutput {
  private filePath: string;
  private maxSize: number;
  private maxFiles: number;
  private currentSize = 0;
  
  constructor(filePath: string, options: FileOutputOptions = {}) {
    super(new JsonFormatter());
    this.filePath = filePath;
    this.maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
  }
  
  write(entry: LogEntry): void {
    const formatted = this.formatter.format(entry) + '\n';
    
    // 检查文件大小
    if (this.currentSize + formatted.length > this.maxSize) {
      this.rotateFile();
    }
    
    fs.appendFileSync(this.filePath, formatted);
    this.currentSize += formatted.length;
  }
  
  private rotateFile(): void {
    // 实现日志文件轮转
    for (let i = this.maxFiles - 1; i > 0; i--) {
      const oldFile = `${this.filePath}.${i}`;
      const newFile = `${this.filePath}.${i + 1}`;
      
      if (fs.existsSync(oldFile)) {
        if (i === this.maxFiles - 1) {
          fs.unlinkSync(oldFile);
        } else {
          fs.renameSync(oldFile, newFile);
        }
      }
    }
    
    if (fs.existsSync(this.filePath)) {
      fs.renameSync(this.filePath, `${this.filePath}.1`);
    }
    
    this.currentSize = 0;
  }
}

// 远程输出
class RemoteOutput extends LogOutput {
  private endpoint: string;
  private buffer: LogEntry[] = [];
  private bufferSize: number;
  private flushInterval: number;
  private flushTimer: NodeJS.Timeout | null = null;
  
  constructor(endpoint: string, options: RemoteOutputOptions = {}) {
    super(new JsonFormatter());
    this.endpoint = endpoint;
    this.bufferSize = options.bufferSize || 100;
    this.flushInterval = options.flushInterval || 5000; // 5秒
    
    this.startFlushTimer();
  }
  
  write(entry: LogEntry): void {
    this.buffer.push(entry);
    
    if (this.buffer.length >= this.bufferSize) {
      this.flush();
    }
  }
  
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      if (this.buffer.length > 0) {
        this.flush();
      }
    }, this.flushInterval);
  }
  
  private async flush(): Promise<void> {
    if (this.buffer.length === 0) return;
    
    const entries = this.buffer.splice(0);
    const payload = entries.map(entry => this.formatter.format(entry));
    
    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ logs: payload })
      });
    } catch (error) {
      console.error('Failed to send logs to remote endpoint:', error);
      // 重新加入缓冲区
      this.buffer.unshift(...entries);
    }
  }
  
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    // 最后一次刷新
    this.flush();
  }
}

// 日志格式化器
abstract class LogFormatter {
  abstract format(entry: LogEntry): string;
}

// JSON格式化器
class JsonFormatter extends LogFormatter {
  format(entry: LogEntry): string {
    return JSON.stringify(entry);
  }
}

// 文本格式化器
class TextFormatter extends LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = entry.level.padEnd(5);
    const message = entry.message;
    const meta = Object.keys(entry.meta).length > 0 ? 
      ` | ${JSON.stringify(entry.meta)}` : '';
    
    return `${timestamp} [${level}] ${message}${meta}`;
  }
}

// 结构化格式化器
class StructuredFormatter extends LogFormatter {
  format(entry: LogEntry): string {
    const base = {
      '@timestamp': entry.timestamp.toISOString(),
      level: entry.level,
      message: entry.message,
      service: entry.service,
      version: entry.version,
      traceId: entry.traceId
    };
    
    return JSON.stringify({ ...base, ...entry.meta });
  }
}

// 日志中间件
class LoggingMiddleware {
  private logger: StructuredLogger;
  
  constructor(logger: StructuredLogger) {
    this.logger = logger;
  }
  
  // Express中间件
  express() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const requestId = this.generateRequestId();
      
      // 添加请求ID到响应头
      res.setHeader('X-Request-ID', requestId);
      
      // 创建请求专用的日志器
      const requestLogger = this.logger.child({
        requestId,
        method: req.method,
        path: req.path,
        userAgent: req.get('user-agent'),
        ip: req.ip
      });
      
      // 将日志器添加到请求对象
      (req as any).logger = requestLogger;
      
      // 记录请求开始
      requestLogger.info('Request started', {
        query: req.query,
        headers: this.sanitizeHeaders(req.headers)
      });
      
      // 监听响应完成
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        
        requestLogger.info('Request completed', {
          statusCode: res.statusCode,
          duration,
          contentLength: res.get('content-length')
        });
        
        // 记录性能指标
        if (duration > 1000) {
          requestLogger.performance('slow_request', duration, {
            statusCode: res.statusCode
          });
        }
      });
      
      next();
    };
  }
  
  private generateRequestId(): string {
    return Math.random().toString(36).substring(2, 15);
  }
  
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    // 移除敏感信息
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    
    return sanitized;
  }
}

// 日志聚合器
class LogAggregator {
  private logs: LogEntry[] = [];
  private aggregationRules: AggregationRule[] = [];
  private aggregatedMetrics: Map<string, AggregatedMetric> = new Map();
  
  constructor(rules: AggregationRule[] = []) {
    this.aggregationRules = rules;
    this.startAggregation();
  }
  
  addLog(entry: LogEntry): void {
    this.logs.push(entry);
    this.processLog(entry);
  }
  
  private processLog(entry: LogEntry): void {
    this.aggregationRules.forEach(rule => {
      if (this.matchesRule(entry, rule)) {
        this.updateMetric(rule.name, entry, rule);
      }
    });
  }
  
  private matchesRule(entry: LogEntry, rule: AggregationRule): boolean {
    if (rule.level && entry.level !== rule.level) return false;
    if (rule.service && entry.service !== rule.service) return false;
    if (rule.pattern && !new RegExp(rule.pattern).test(entry.message)) return false;
    
    return true;
  }
  
  private updateMetric(name: string, entry: LogEntry, rule: AggregationRule): void {
    let metric = this.aggregatedMetrics.get(name);
    
    if (!metric) {
      metric = {
        name,
        count: 0,
        firstSeen: entry.timestamp,
        lastSeen: entry.timestamp,
        samples: []
      };
      this.aggregatedMetrics.set(name, metric);
    }
    
    metric.count++;
    metric.lastSeen = entry.timestamp;
    metric.samples.push(entry);
    
    // 保持最近的样本
    if (metric.samples.length > 100) {
      metric.samples = metric.samples.slice(-100);
    }
  }
  
  private startAggregation(): void {
    // 定期生成聚合报告
    setInterval(() => {
      this.generateAggregationReport();
    }, 60000); // 每分钟
  }
  
  private generateAggregationReport(): void {
    const report: AggregationReport = {
      timestamp: new Date(),
      metrics: Array.from(this.aggregatedMetrics.values()),
      summary: this.generateSummary()
    };
    
    // 发送报告
    this.sendReport(report);
    
    // 重置指标
    this.resetMetrics();
  }
  
  private generateSummary(): LogSummary {
    const totalLogs = this.logs.length;
    const errorLogs = this.logs.filter(l => l.level === 'ERROR' || l.level === 'FATAL').length;
    const warnLogs = this.logs.filter(l => l.level === 'WARN').length;
    
    return {
      totalLogs,
      errorLogs,
      warnLogs,
      errorRate: totalLogs > 0 ? errorLogs / totalLogs : 0,
      timeRange: {
        start: this.logs[0]?.timestamp || new Date(),
        end: this.logs[this.logs.length - 1]?.timestamp || new Date()
      }
    };
  }
  
  private async sendReport(report: AggregationReport): Promise<void> {
    try {
      await fetch('/api/v1/logs/aggregation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(report)
      });
    } catch (error) {
      console.error('Failed to send aggregation report:', error);
    }
  }
  
  private resetMetrics(): void {
    this.aggregatedMetrics.clear();
    this.logs = [];
  }
  
  getMetrics(): AggregatedMetric[] {
    return Array.from(this.aggregatedMetrics.values());
  }
}

// 类型定义
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

interface LoggerConfig {
  level?: LogLevel;
  outputs?: LogOutput[];
  context?: LogContext;
}

interface LogContext {
  [key: string]: any;
}

interface LogMeta {
  [key: string]: any;
}

interface LogEntry {
  timestamp: Date;
  level: string;
  message: string;
  meta: LogMeta;
  traceId: string;
  service: string;
  version: string;
}

interface FileOutputOptions {
  maxSize?: number;
  maxFiles?: number;
}

interface RemoteOutputOptions {
  bufferSize?: number;
  flushInterval?: number;
}

interface AggregationRule {
  name: string;
  level?: string;
  service?: string;
  pattern?: string;
}

interface AggregatedMetric {
  name: string;
  count: number;
  firstSeen: Date;
  lastSeen: Date;
  samples: LogEntry[];
}

interface AggregationReport {
  timestamp: Date;
  metrics: AggregatedMetric[];
  summary: LogSummary;
}

interface LogSummary {
  totalLogs: number;
  errorLogs: number;
  warnLogs: number;
  errorRate: number;
  timeRange: {
    start: Date;
    end: Date;
  };
}
```

## 11. 部署与运维

### 11.1 容器化部署

#### 11.1.1 Docker配置
```dockerfile
# 多阶段构建 Dockerfile
# 阶段1: 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY tsconfig.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/
COPY public/ ./public/

# 构建应用
RUN npm run build

# 阶段2: 运行阶段
FROM node:18-alpine AS runtime

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# 安装运行时依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 从构建阶段复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["node", "dist/server.js"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************/reading_assistant
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - API_KEY=${API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    ````yaml path=docs/Architecture_Design_Document.md mode=EDIT
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 数据库服务
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=reading_assistant
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d reading_assistant"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - app-network

  # 可视化监控
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - app-network

  # 日志收集
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped
    networks:
      - app-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - app-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  app-network:
    driver: bridge
```

#### 11.1.2 Kubernetes部署配置
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: reading-assistant
  labels:
    name: reading-assistant

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: reading-assistant
data:
  NODE_ENV: "production"
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "reading_assistant"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  LOG_LEVEL: "info"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: reading-assistant
type: Opaque
data:
  DATABASE_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-jwt-secret>
  API_KEY: <base64-encoded-api-key>
  REDIS_PASSWORD: <base64-encoded-redis-password>

---
# k8s/postgres-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: reading-assistant
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: DATABASE_NAME
        - name: POSTGRES_USER
          value: "user"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: DATABASE_PASSWORD
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - user
            - -d
            - reading_assistant
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - user
            - -d
            - reading_assistant
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
# k8s/postgres-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: reading-assistant
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
# k8s/postgres-pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: reading-assistant
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
# k8s/redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: reading-assistant
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --appendonly
        - "yes"
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc

---
# k8s/redis-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: reading-assistant
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
# k8s/redis-pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: reading-assistant
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
# k8s/app-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: reading-assistant-app
  namespace: reading-assistant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: reading-assistant-app
  template:
    metadata:
      labels:
        app: reading-assistant-app
    spec:
      containers:
      - name: app
        image: reading-assistant:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: app-config
        env:
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: DATABASE_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: JWT_SECRET
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: API_KEY
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs-volume
          mountPath: /app/logs
        - name: uploads-volume
          mountPath: /app/uploads
      volumes:
      - name: logs-volume
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: uploads-volume
        persistentVolumeClaim:
          claimName: uploads-pvc

---
# k8s/app-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: reading-assistant-service
  namespace: reading-assistant
spec:
  selector:
    app: reading-assistant-app
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# k8s/app-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: reading-assistant-hpa
  namespace: reading-assistant
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: reading-assistant-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: reading-assistant-ingress
  namespace: reading-assistant
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - reading-assistant.example.com
    secretName: reading-assistant-tls
  rules:
  - host: reading-assistant.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: reading-assistant-service
            port:
              number: 80
```

### 11.2 CI/CD流水线

#### 11.2.1 GitHub Actions配置
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  lint-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: npm run lint
      
    - name: Run type checking
      run: npm run type-check
      
    - name: Run unit tests
      run: npm run test:unit
      
    - name: Run integration tests
      run: npm run test:integration
      env:
        DATABASE_URL: postgresql://test:test@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        
    - name: Generate test coverage
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: Run npm audit
      run: npm audit --audit-level high

  # 构建和推送镜像
  build-and-push:
    needs: [lint-and-test, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    permissions:
      contents: read
      packages: write
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: spdx-json
        output-file: sbom.spdx.json
        
    - name: Upload SBOM
      uses: actions/upload-artifact@v3
      with:
        name: sbom
        path: sbom.spdx.json

  # 部署到测试环境
  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        
    - name: Deploy to staging
      run: |
        export KUBECONFIG=kubeconfig
        envsubst < k8s/app-deployment.yaml | kubectl apply -f -
        kubectl set image deployment/reading-assistant-app app=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} -n reading-assistant-staging
        kubectl rollout status deployment/reading-assistant-app -n reading-assistant-staging
        
    - name: Run smoke tests
      run: |
        npm run test:smoke -- --baseUrl=https://staging.reading-assistant.example.com
        
    - name: Run performance tests
      run: |
        npm run test:performance -- --baseUrl=https://staging.reading-assistant.example.com

  # 部署到生产环境
  deploy-production:
    needs: [build-and-push, deploy-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        
    - name: Deploy to production
      run: |
        export KUBECONFIG=kubeconfig
        envsubst < k8s/app-deployment.yaml | kubectl apply -f -
        kubectl set image deployment/reading-assistant-app app=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} -n reading-assistant
        kubectl rollout status deployment/reading-assistant-app -n reading-assistant
        
    - name: Verify deployment
      run: |
        export KUBECONFIG=kubeconfig
        kubectl get pods -n reading-assistant
        kubectl get services -n reading-assistant
        
    - name: Run post-deployment tests
      run: |
        npm run test:smoke -- --baseUrl=https://reading-assistant.example.com
        
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        message: |
          🚀 Production deployment completed!
          Version: ${{ github.sha }}
          Environment: Production
          Status: ${{ job.status }}

  # 回滚部署
  rollback:
    runs-on: ubuntu-latest
    if: failure()
    environment: production
    
    steps:
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        
    - name: Rollback deployment
      run: |
        export KUBECONFIG=kubeconfig
        kubectl rollout undo deployment/reading-assistant-app -n reading-assistant
        kubectl rollout status deployment/reading-assistant-app -n reading-assistant
        
    - name: Notify rollback
      uses: 8398a7/action-slack@v3
      with:
        status: 'warning'
        channel: '#alerts'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        message: |
          ⚠️ Production rollback executed!
          Reason: Deployment failure
          Previous version restored
```

#### 11.2.2 部署脚本
```bash
#!/bin/bash
# scripts/deploy.sh

set -euo pipefail

# 配置变量
ENVIRONMENT=${1:-staging}
IMAGE_TAG=${2:-latest}
NAMESPACE="reading-assistant"
if [ "$ENVIRONMENT" = "staging" ]; then
    NAMESPACE="reading-assistant-staging"
fi

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_info "Dependencies check passed"
}

# 创建命名空间
create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warn "Namespace $NAMESPACE already exists"
    else
        kubectl create namespace "$NAMESPACE"
        log_info "Namespace $NAMESPACE created"
    fi
}

# 应用配置
apply_configs() {
    log_info "Applying configurations..."
    
    # 应用ConfigMap
    envsubst < k8s/configmap.yaml | kubectl apply -f - -n "$NAMESPACE"
    
    # 应用Secrets (如果不存在)
    if ! kubectl get secret app-secrets -n "$NAMESPACE" &> /dev/null; then
        log_warn "Secrets not found, please create them manually"
        log_warn "kubectl create secret generic app-secrets --from-env-file=.env.production -n $NAMESPACE"
    fi
    
    # 应用PVC
    kubectl apply -f k8s/postgres-pvc.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/redis-pvc.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/logs-pvc.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/uploads-pvc.yaml -n "$NAMESPACE"
    
    log_info "Configurations applied"
}

# 部署数据库
deploy_database() {
    log_info "Deploying database..."
    
    kubectl apply -f k8s/postgres-deployment.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/postgres-service.yaml -n "$NAMESPACE"
    
    # 等待数据库就绪
    log_info "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n "$NAMESPACE" --timeout=300s
    
    log_info "Database deployed successfully"
}

# 部署Redis
deploy_redis() {
    log_info "Deploying Redis..."
    
    kubectl apply -f k8s/redis-deployment.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/redis-service.yaml -n "$NAMESPACE"
    
    # 等待Redis就绪
    log_info "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n "$NAMESPACE" --timeout=300s
    
    log_info "Redis deployed successfully"
}

# 运行数据库迁移
run_migrations() {
    log_info "Running database migrations..."
    
    # 创建迁移Job
    cat <<EOF | kubectl apply -f - -n "$NAMESPACE"
apiVersion: batch/v1
kind: Job
metadata:
  name: db-migration-$(date +%s)
  namespace: $NAMESPACE
spec:
  template:
    spec:
      containers:
      - name: migration
        image: ghcr.io/reading-assistant:$IMAGE_TAG
        command: ["npm", "run", "migrate"]
        envFrom:
        - configMapRef:
            name: app-config
        env:
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: DATABASE_PASSWORD
      restartPolicy: Never
  backoffLimit: 3
EOF
    
    # 等待迁移完成
    log_info "Waiting for migration to complete..."
    kubectl wait --for=condition=complete job -l job-name=db-migration --timeout=300s -n "$NAMESPACE"
    
    log_info "Database migration completed"
}

# 部署应用
deploy_app() {
    log_info "Deploying application..."
    
    # 更新镜像标签
    sed "s|image: reading-assistant:latest|image: ghcr.io/reading-assistant:$IMAGE_TAG|g" k8s/app-deployment.yaml | kubectl apply -f - -n "$NAMESPACE"
    kubectl apply -f k8s/app-service.yaml -n "$NAMESPACE"
    kubectl apply -f k8s/app-hpa.yaml -n "$NAMESPACE"
    
    # 等待部署完成
    log_info "Waiting for application deployment to complete..."
    kubectl rollout status deployment/reading-assistant-app -n "$NAMESPACE" --timeout=600s
    
    log_info "Application deployed successfully"
}

# 部署Ingress
deploy_ingress() {
    log_info "Deploying Ingress..."
    
    # 根据环境选择不同的Ingress配置
    if [ "$ENVIRONMENT" = "production" ]; then
        kubectl apply -f k8s/ingress-production.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/ingress-staging.yaml -n "$NAMESPACE"
    fi
    
    log_info "Ingress deployed successfully"
}

# 健康检查
health_check() {
    log_info "Performing health check..."
    
    # 获取服务端点
    if [ "$ENVIRONMENT" = "production" ]; then
        ENDPOINT="https://reading-assistant.example.com"
    else
        ENDPOINT="https://staging.reading-assistant.example.com"
    fi
    
    # 等待服务可用
    for i in {1..30}; do
        if curl -f "$ENDPOINT/health" &> /dev/null; then
            log_info "Health check passed"
            return 0
        fi
        log_warn "Health check failed, retrying in 10 seconds... ($i/30)"
        sleep 10
    done
    
    log_error "Health check failed after 30 attempts"
    return 1
}

# 清理旧资源
cleanup() {
    log_info "Cleaning up old resources..."
    
    # 删除已完成的Jobs
    kubectl delete jobs --field-selector status.successful=1 -n "$NAMESPACE" || true
    
    # 删除旧的ReplicaSets
    kubectl delete rs --field-selector status.replicas=0 -n "$NAMESPACE" || true
    
    log_info "Cleanup completed"
}

# 回滚函数
rollback() {
    log_warn "Rolling back deployment..."
    
    kubectl rollout undo deployment/reading-assistant-app -n "$NAMESPACE"
    kubectl rollout status deployment/reading-assistant-app -n "$NAMESPACE" --timeout=300s
    
    log_info "Rollback completed"
}

# 主部署流程
main() {
    log_info "Starting deployment to $ENVIRONMENT environment with image tag: $IMAGE_TAG"
    
    # 设置错误处理
    trap 'log_error "Deployment failed"; rollback; exit 1' ERR
    
    check_dependencies
    create_namespace
    apply_configs
    deploy_database
    deploy_redis
    run_migrations
    deploy_app
    deploy_ingress
    
    if health_check; then
        cleanup
        log_info "Deployment to $ENVIRONMENT completed successfully!"
    else
        log_error "Deployment failed health check"
        rollback
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

```bash
#!/bin/bash
# scripts/monitoring-setup.sh

set -euo pipefail

NAMESPACE="monitoring"

log_info() {
    echo -e "\033[0;32m[INFO]\033[0m $1"
}

log_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1"
}

# 创建监控命名空间
create_monitoring_namespace() {
    log_info "Creating monitoring namespace..."
    
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
}

# 安装Prometheus
install_prometheus() {
    log_info "Installing Prometheus..."
    
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace "$NAMESPACE" \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --set grafana.adminPassword=admin123 \
        --set grafana.persistence.enabled=true \
        --set grafana.persistence.size=10Gi \
        --set alertmanager.alertmanagerSpec.storage.volumeClaimTemplate.spec.resources.requests.storage=10Gi
}

# 配置ServiceMonitor
setup_service_monitors() {
    log_info "Setting up ServiceMonitors..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: reading-assistant-metrics
  namespace: $NAMESPACE
spec:
  selector:
    matchLabels:
      app: reading-assistant-app
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
EOF
}

# 安装日志收集
install_logging() {
    log_info "Installing ELK stack..."
    
    helm repo add elastic https://helm.elastic.co
    helm repo update
    
    # Elasticsearch
    helm upgrade --install elasticsearch elastic/elasticsearch \
        --namespace "$NAMESPACE" \
        --set replicas=1 \
        --set minimumMasterNodes=1 \
        --set resources.requests.memory=1Gi \
        --set resources.limits.memory=2Gi \
        --set volumeClaimTemplate.resources.requests.storage=30Gi
    
    # Kibana
    helm upgrade --install kibana elastic/kibana \
        --namespace "$NAMESPACE" \
        --set resources.requests.memory=512Mi \
        --set resources.limits.memory=1Gi
    
    # Filebeat
    helm upgrade --install filebeat elastic/filebeat \
        --namespace "$NAMESPACE" \
        --set daemonset.enabled=true
}

# 配置告警规则
setup_alerts() {
    log_info "Setting up alert rules..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: reading-assistant-alerts
  namespace: $NAMESPACE
spec:
  groups:
  - name: reading-assistant
    rules:
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate detected
        description: "Error rate is {{ \$value }} errors per second"
    
    - alert: HighResponseTime
      expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High response time detected
        description: "95th percentile response time is {{ \$value }} seconds"
    
    - alert: PodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: Pod is crash looping
        description: "Pod {{ \$labels.pod }} is crash looping"
EOF
}

# 主函数
main() {
    log_info "Setting up monitoring infrastructure..."
    
    create_monitoring_namespace
    install_prometheus
    setup_service_monitors
    install_logging
    setup_alerts
    
    log_info "Monitoring setup completed!"
    log_info "Access Grafana at: kubectl port-forward svc/prometheus-grafana 3000:80 -n $NAMESPACE"
    log_info "Access Kibana at: kubectl port-forward svc/kibana-kibana 5601:5601 -n $NAMESPACE"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

### 11.3 监控告警

#### 11.3.1 Prometheus配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用指标
  - job_name: 'reading-assistant'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  # Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
      
  # PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
      
  # Redis Exporter
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
      
  # Nginx Exporter
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']

# monitoring/alert_rules.yml
groups:
  - name: application
    rules:
      # 应用可用性告警
      - alert: ApplicationDown
        expr: up{job="reading-assistant"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Application is down"
          description: "Reading Assistant application has been down for more than 1 minute"
          
      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          
      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is {{ $value }}s"
          
      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / 1024 / 1024) > 1024
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}MB"

  - name: infrastructure
    rules:
      # 数据库连接告警
      - alert: DatabaseConnectionFailed
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failed"
          description: "Cannot connect to PostgreSQL database"
          
      # 数据库慢查询告警
      - alert: DatabaseSlowQueries
        expr: rate(pg_stat_activity_max_tx_duration[5m]) > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database slow queries detected"
          description: "Slow queries detected in PostgreSQL"
          
      # Redis连接告警
      - alert: RedisConnectionFailed
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection failed"
          description: "Cannot connect to Redis"
          
      # 磁盘空间告警
      - alert: DiskSpaceHigh
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space running low"
          description: "Disk space is {{ $value | humanizePercentage }} full"
          
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}%"
```

#### 11.3.2 Grafana仪表板
```json
{
  "dashboard": {
    "id": null,
    "title": "Reading Assistant Dashboard",
    "tags": ["reading-assistant"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps"
          }
        }
      },
      {
        "id": 2,
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percentunit",
            "thresholds": {
              "steps": [
                {"color": "green", "value": 0},
                {"color": "yellow", "value": 0.01},
                {"color": "red", "value": 0.05}
              ]
            }
          }
        }
      },
      {
        "id": 3,
        "title": "Response Time",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "99th percentile"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s"
          }
        }
      },
      {
        "id": 4,
        "title": "Memory Usage",
        "type": "timeseries",
        "targets": [
          {
            "expr": "process_resident_memory_bytes / 1024 / 1024",
            "legendFormat": "Memory Usage (MB)"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "decbytes"
          }
        }
      },
      {
        "id": 5,
        "title": "Database Connections",
        "type": "timeseries",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "Active Connections"
          }
        ]
      },
      {
        "id": 6,
        "title": "Cache Hit Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total)",
            "legendFormat": "Cache Hit Rate"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percentunit"
          }
        }
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

这个架构设计文档涵盖了阅读助手系统的完整技术架构，包括：

1. **系统架构概述** - 整体架构设计和技术栈选择
2. **前端架构** - React组件设计、状态管理、路由配置
3. **后端架构** - Node.js API设计、数据库设计、缓存策略
4. **数据库设计** - 完整的数据模型和关系设计
5. **API设计** - RESTful API规范和接口定义
6. **安全架构** - 认证授权、数据安全、API安全
7. **缓存策略** - Redis缓存设计和优化策略
8. **文件存储** - 文件上传、存储和管理方案
9. **搜索功能** - Elasticsearch集成和搜索优化
10. **性能监控** - 前后端性能监控和日志系统
11. **部署运维** - 容器化部署、CI/CD流水线、监控告警

整个架构设计注重可扩展性、可维护性和高可用性，采用现代化的技术栈和最佳实践，为阅读助手系统提供了坚实的技术基础。

​      
