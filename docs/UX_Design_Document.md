# 跨浏览器网页内容提取与重排版插件 - UX设计文档

## 文档信息

| 项目名称 | 跨浏览器网页内容提取与重排版插件 |
|---------|---------------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2024-01-15 |
| 最后更新 | 2024-01-15 |
| 文档类型 | 用户体验设计文档 |
| 负责人 | UX设计团队 |

## 目录

1. [设计概述](#1-设计概述)
2. [用户研究](#2-用户研究)
3. [设计原则](#3-设计原则)
4. [信息架构](#4-信息架构)
5. [交互设计](#5-交互设计)
6. [视觉设计系统](#6-视觉设计系统)
7. [响应式设计](#7-响应式设计)
8. [组件设计规范](#8-组件设计规范)
9. [用户流程设计](#9-用户流程设计)
10. [可用性测试](#10-可用性测试)
11. [无障碍设计](#11-无障碍设计)
12. [设计交付](#12-设计交付)

## 1. 设计概述

### 1.1 设计目标

#### 主要目标
- **提升阅读体验**：创造清洁、舒适、专注的阅读环境
- **个性化定制**：满足不同用户的个性化阅读需求
- **操作简便**：降低学习成本，提供直观的操作体验
- **跨平台一致**：确保在不同设备和浏览器上的体验一致性

#### 设计价值
- **用户价值**：减少阅读干扰，提高阅读效率和舒适度
- **商业价值**：通过优秀的用户体验建立产品竞争优势
- **技术价值**：建立可扩展的设计系统，支持产品长期发展

### 1.2 设计范围

#### 核心界面
- **重排版阅读页面**：全屏沉浸式阅读界面
- **设置面板**：个性化设置控制面板
- **顶部控制栏**：快速操作和状态显示
- **进度指示器**：阅读进度和导航辅助

#### 交互场景
- **内容提取和显示**：从触发到显示的完整流程
- **个性化设置**：设置调整和实时预览
- **快捷操作**：键盘快捷键和手势操作
- **状态反馈**：加载、错误、成功等状态提示

### 1.3 设计约束

#### 技术约束
- 必须在iframe环境中运行
- 需要兼容4种主流浏览器
- 内存使用限制在50MB以内
- 加载时间不超过2秒

#### 业务约束
- 开发周期14周
- 团队资源有限
- 需要支持国际化
- 必须符合无障碍标准

## 2. 用户研究

### 2.1 用户调研结果

#### 调研方法
- **用户访谈**：深度访谈20名目标用户
- **问卷调查**：收集500份有效问卷
- **竞品分析**：分析5款同类产品
- **可用性测试**：测试现有解决方案

#### 关键发现

##### 用户痛点
1. **内容干扰严重**：85%用户反映网页广告和无关内容影响阅读
2. **排版体验差**：78%用户认为原网页排版不适合长时间阅读
3. **个性化不足**：72%用户希望能够自定义阅读界面
4. **操作复杂**：65%用户认为现有工具操作过于复杂

##### 用户需求
1. **一键清洁阅读**：快速获得干净的阅读环境
2. **丰富个性化选项**：字体、颜色、布局等全方位定制
3. **跨设备一致性**：在不同设备上保持相同的阅读体验
4. **智能优化**：自动优化内容质量和阅读体验

### 2.2 用户画像细化

#### 主要用户：知识工作者（40%）
- **基本信息**：25-45岁，高学历，收入中上
- **使用场景**：工作时间阅读技术文档、行业报告
- **设备偏好**：主要使用桌面端，偶尔使用平板
- **痛点**：信息密度大，需要长时间专注阅读
- **需求**：专业的阅读工具，支持高效信息获取

#### 次要用户：学生群体（35%）
- **基本信息**：18-28岁，在校学生或刚毕业
- **使用场景**：学习时间阅读学术文章、课程资料
- **设备偏好**：移动端和桌面端并重
- **痛点**：注意力容易分散，需要护眼功能
- **需求**：简洁的界面，良好的移动端体验

#### 一般用户：内容消费者（25%）
- **基本信息**：20-50岁，各行各业
- **使用场景**：休闲时间阅读新闻、博客、娱乐内容
- **设备偏好**：主要使用移动端
- **痛点**：碎片化阅读，界面复杂
- **需求**：简单易用，快速上手

### 2.3 用户旅程地图

#### 发现阶段
```
触发点：遇到阅读困难的网页
情绪：😤 烦躁，被广告和混乱排版干扰
行为：寻找解决方案，搜索相关工具
需求：快速找到可靠的解决方案
机会：通过搜索引擎优化和口碑传播获得用户
```

#### 首次使用阶段
```
触发点：安装插件，首次使用
情绪：🤔 好奇，对新工具的期待和担忧
行为：尝试基础功能，探索界面
需求：快速理解功能，获得良好的第一印象
机会：通过引导流程和优秀的默认体验留住用户
```

#### 深度使用阶段
```
触发点：多次使用，开始个性化设置
情绪：😊 满意，开始依赖产品
行为：调整设置，探索高级功能
需求：更多个性化选项，稳定的体验
机会：通过丰富的功能和稳定性建立用户忠诚度
```

#### 推荐阶段
```
触发点：体验良好，愿意推荐给他人
情绪：😍 喜爱，成为产品的拥护者
行为：主动推荐，提供反馈建议
需求：持续的产品改进，新功能更新
机会：通过用户推荐获得新用户，收集改进建议
```

## 3. 设计原则

### 3.1 核心设计原则

#### 1. 简洁至上（Simplicity First）
- **理念**：复杂的功能，简单的操作
- **应用**：隐藏复杂性，突出核心功能
- **示例**：一键启动重排版，设置面板可折叠

#### 2. 内容为王（Content is King）
- **理念**：一切设计服务于内容阅读
- **应用**：最小化界面元素，最大化内容空间
- **示例**：全屏阅读模式，可隐藏的控制栏

#### 3. 个性化优先（Personalization First）
- **理念**：每个用户都有独特的阅读偏好
- **应用**：提供丰富的个性化选项
- **示例**：多种主题、字体、布局选择

#### 4. 响应式设计（Responsive Design）
- **理念**：一套设计适配所有设备
- **应用**：流式布局，自适应组件
- **示例**：移动端单栏，桌面端双栏

#### 5. 可访问性（Accessibility）
- **理念**：产品应该为所有人设计
- **应用**：支持键盘导航，屏幕阅读器
- **示例**：高对比度模式，语义化标签

### 3.2 交互设计原则

#### 1. 即时反馈（Immediate Feedback）
- **原则**：用户操作应该立即得到反馈
- **应用**：设置变更实时生效，加载状态显示
- **实现**：CSS过渡动画，状态指示器

#### 2. 容错性（Error Prevention）
- **原则**：预防错误比处理错误更重要
- **应用**：合理的默认值，输入验证
- **实现**：范围限制，智能建议

#### 3. 一致性（Consistency）
- **原则**：相同的操作产生相同的结果
- **应用**：统一的交互模式，一致的视觉语言
- **实现**：设计系统，组件库

#### 4. 可预测性（Predictability）
- **原则**：用户应该能够预测操作的结果
- **应用**：清晰的标签，明确的状态
- **实现**：图标+文字，状态说明

### 3.3 视觉设计原则

#### 1. 视觉层次（Visual Hierarchy）
- **原则**：重要信息应该更突出
- **应用**：大小、颜色、位置的层次化
- **实现**：标题层级，颜色对比

#### 2. 留白平衡（White Space Balance）
- **原则**：适当的留白提升阅读体验
- **应用**：内容间距，页面边距
- **实现**：间距系统，呼吸感设计

#### 3. 色彩心理（Color Psychology）
- **原则**：颜色传达情感和信息
- **应用**：主题色彩，状态颜色
- **实现**：色彩系统，情感化设计

## 4. 信息架构

### 4.1 功能架构图

```
重排版阅读器
├── 内容显示区域
│   ├── 文章标题
│   ├── 文章正文
│   ├── 图片媒体
│   └── 导航链接
├── 顶部控制栏
│   ├── 关闭按钮
│   ├── AI增强按钮
│   ├── 设置按钮
│   └── 全屏按钮
├── 设置面板
│   ├── 文字设置
│   │   ├── 字号调节
│   │   ├── 行高设置
│   │   └── 字体选择
│   ├── 主题设置
│   │   ├── 背景主题
│   │   └── 颜色方案
│   ├── 布局设置
│   │   ├── 页面宽度
│   │   ├── 栏数设置
│   │   └── 翻页模式
│   └── 快捷键帮助
└── 进度指示器
    ├── 阅读进度
    └── 页面导航
```

### 4.2 信息层级结构

#### 第一层级：核心功能
- **内容显示**：主要阅读区域，占据最大空间
- **基础控制**：关闭、设置等必需功能

#### 第二层级：增强功能
- **AI增强**：内容优化功能
- **个性化设置**：用户定制选项

#### 第三层级：辅助功能
- **进度指示**：阅读辅助信息
- **快捷键**：高效操作方式

### 4.3 导航结构

#### 主导航
- **线性导航**：上一页/下一页
- **跳转导航**：进度条点击跳转
- **快捷导航**：键盘快捷键

#### 设置导航
- **分组导航**：按功能分组的设置项
- **搜索导航**：设置项快速搜索
- **收藏导航**：常用设置快速访问

## 5. 交互设计

### 5.1 核心交互流程

#### 启动重排版流程
```
用户触发 → 内容检测 → 提取处理 → 重排版显示 → 用户阅读
    ↓           ↓           ↓           ↓           ↓
点击图标     分析页面     算法处理     界面渲染     开始阅读
快捷键      内容识别     格式化      动画过渡     个性化设置
右键菜单    质量评估     优化布局     状态反馈     功能使用
```

#### 设置调整流程
```
打开设置 → 选择分类 → 调整参数 → 实时预览 → 确认保存
    ↓           ↓           ↓           ↓           ↓
点击按钮     文字/主题     滑块/选择     即时生效     自动保存
快捷键      布局/其他     输入/切换     视觉反馈     云端同步
手势操作    搜索定位     批量设置     撤销重做     导入导出
```

### 5.2 交互状态设计

#### 按钮状态
```css
/* 按钮状态设计规范 */
.button {
  /* 默认状态 */
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--button-border);
  transition: all 0.2s ease;
}

.button:hover {
  /* 悬停状态 */
  background: var(--button-bg-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.button:active {
  /* 激活状态 */
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.button:disabled {
  /* 禁用状态 */
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.button.loading {
  /* 加载状态 */
  position: relative;
  color: transparent;
}

.button.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
```

#### 输入控件状态
```css
/* 输入控件状态设计 */
.input-control {
  /* 默认状态 */
  border: 2px solid var(--input-border);
  background: var(--input-bg);
  transition: all 0.2s ease;
}

.input-control:focus {
  /* 聚焦状态 */
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.input-control.error {
  /* 错误状态 */
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-control.success {
  /* 成功状态 */
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}
```

### 5.3 手势和快捷键设计

#### 键盘快捷键映射
```typescript
// 快捷键设计规范
const keyboardShortcuts = {
  // 导航操作
  navigation: {
    'Escape': '关闭重排版',
    'ArrowLeft': '上一页',
    'ArrowRight': '下一页',
    'PageUp': '快速上翻',
    'PageDown': '快速下翻',
    'Home': '回到顶部',
    'End': '跳到底部'
  },
  
  // 设置操作
  settings: {
    'Ctrl/Cmd + =': '增大字号',
    'Ctrl/Cmd + -': '减小字号',
    'Ctrl/Cmd + 0': '重置字号',
    'T': '切换主题',
    'W': '调节宽度',
    'S': '打开设置'
  },
  
  // 功能操作
  features: {
    'A': 'AI增强切换',
    'F11': '全屏切换',
    'Ctrl/Cmd + R': '重新加载',
    'Ctrl/Cmd + P': '打印页面',
    '?': '显示帮助'
  }
}
```

#### 触摸手势设计
```typescript
// 移动端手势设计
const touchGestures = {
  // 单指手势
  singleTouch: {
    'tap': '选择/激活',
    'longPress': '显示上下文菜单',
    'swipeLeft': '下一页',
    'swipeRight': '上一页',
    'swipeUp': '向下滚动',
    'swipeDown': '向上滚动'
  },
  
  // 双指手势
  multiTouch: {
    'pinchIn': '缩小字号',
    'pinchOut': '放大字号',
    'twoFingerTap': '显示设置',
    'threeFingerTap': '切换主题'
  }
}
```

### 5.4 微交互设计

#### 加载动画
```css
/* 加载状态微交互 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 内容加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-enter {
  animation: fadeInUp 0.5s ease-out;
}
```

#### 状态切换动画
```css
/* 主题切换动画 */
.theme-transition {
  transition: 
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* 设置面板展开动画 */
.settings-panel {
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.settings-panel.open {
  transform: translateX(0);
}

/* 按钮点击反馈 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.button-ripple {
  position: relative;
  overflow: hidden;
}

.button-ripple::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
}
```

## 6. 视觉设计系统

### 6.1 色彩系统

#### 主色彩方案
```css
:root {
  /* 主品牌色 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* 主色 */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  
  /* 辅助色彩 */
  --secondary-500: #6b7280;
  --accent-500: #f59e0b;
  
  /* 功能色彩 */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;
}
```

#### 主题色彩方案
```css
/* 默认主题 */
.theme-default {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
}

/* 护眼主题 */
.theme-eye-care {
  --bg-primary: #f5f5f5;
  --bg-secondary: #eeeeee;
  --text-primary: #424242;
  --text-secondary: #757575;
  --border-color: #d0d0d0;
}

/* 纸质书主题 */
.theme-paper {
  --bg-primary: #fdf6e3;
  --bg-secondary: #f7f1d3;
  --text-primary: #5c4317;
  --text-secondary: #8b7355;
  --border-color: #e6d5a8;
}

/* 自然主题 */
.theme-nature {
  --bg-primary: #f0f8f0;
  --bg-secondary: #e8f5e8;
  --text-primary: #2d5016;
  --text-secondary: #4a7c59;
  --border-color: #c8e6c9;
}

/* 温馨主题 */
.theme-warm {
  --bg-primary: #fdf2f8;
  --bg-secondary: #fce7f3;
  --text-primary: #7c2d12;
  --text-secondary: #be185d;
  --border-color: #fce7f3;
}

/* 夜间主题 */
.theme-dark {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --border-color: #404040;
}
```

### 6.2 字体系统

#### 字体层级
```css
/* 字体大小系统 */
:root {
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;
  
  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
}

/* 字体族系统 */
:root {
  /* 系统字体 */
  --font-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                 'Helvetica Neue', Arial, sans-serif;
  
  /* 中文字体 */
  --font-chinese-sans: 'Source Han Sans CN', 'Noto Sans CJK SC', 
                       'PingFang SC', 'Microsoft YaHei', sans-serif;
  --font-chinese-serif: 'Source Han Serif CN', 'Noto Serif CJK SC', 
                        'Songti SC', 'SimSun', serif;
  
  /* 英文字体 */
  --font-english-serif: Georgia, 'Times New Roman', Times, serif;
  --font-english-sans: 'Source Sans Pro', 'Helvetica Neue', Arial, sans-serif;
  --font-english-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', 
                       Consolas, monospace;
}
```

#### 字体应用规范
```css
/* 标题字体 */
.heading-1 {
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: var(--leading-tight);
  letter-spacing: -0.025em;
}

.heading-2 {
  font-size: var(--text-3xl);
  font-weight: 600;
  line-height: var(--leading-tight);
  letter-spacing: -0.025em;
}

.heading-3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: var(--leading-snug);
}
/* 正文字体 */
.body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
}

.body-normal {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

.body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
}

/* 辅助文字 */
.caption {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  color: var(--text-secondary);
}

/* 代码字体 */
.code {
  font-family: var(--font-english-mono);
  font-size: 0.875em;
  background: var(--bg-secondary);
  padding: 0.125em 0.25em;
  border-radius: 0.25em;
}
```

### 6.3 间距系统

#### 间距标准
```css
:root {
  /* 基础间距单位 */
  --space-0: 0;
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
  --space-20: 5rem;    /* 80px */
  --space-24: 6rem;    /* 96px */
}

/* 语义化间距 */
:root {
  --content-padding: var(--space-6);
  --section-gap: var(--space-8);
  --component-gap: var(--space-4);
  --element-gap: var(--space-2);
}
```

#### 布局间距应用
```css
/* 页面布局间距 */
.page-container {
  padding: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

.content-section {
  margin-bottom: var(--section-gap);
}

.component-group {
  gap: var(--component-gap);
}

/* 文字间距 */
.text-content h1,
.text-content h2,
.text-content h3 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-4);
}

.text-content p {
  margin-bottom: var(--space-4);
}

.text-content ul,
.text-content ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

.text-content li {
  margin-bottom: var(--space-2);
}
```

### 6.4 阴影和圆角系统

#### 阴影层级
```css
:root {
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 
                 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
               0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
               0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
               0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* 内阴影 */
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

/* 圆角系统 */
:root {
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;
}
```

#### 组件阴影应用
```css
/* 卡片组件 */
.card {
  box-shadow: var(--shadow-base);
  border-radius: var(--radius-lg);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* 按钮组件 */
.button {
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.button:hover {
  box-shadow: var(--shadow-base);
}

/* 输入框组件 */
.input {
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-inner);
}

.input:focus {
  box-shadow: var(--shadow-inner), 
              0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 弹出层组件 */
.modal,
.dropdown {
  box-shadow: var(--shadow-xl);
  border-radius: var(--radius-xl);
}
```

## 7. 响应式设计

### 7.1 断点系统

#### 断点定义
```css
:root {
  /* 断点系统 */
  --breakpoint-xs: 320px;   /* 小屏手机 */
  --breakpoint-sm: 640px;   /* 大屏手机 */
  --breakpoint-md: 768px;   /* 平板 */
  --breakpoint-lg: 1024px;  /* 小屏桌面 */
  --breakpoint-xl: 1280px;  /* 大屏桌面 */
  --breakpoint-2xl: 1536px; /* 超大屏 */
}

/* 媒体查询混合器 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

#### 容器系统
```css
/* 响应式容器 */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding: 0 var(--space-6);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding: 0 var(--space-8);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1200px;
  }
}
```

### 7.2 布局适配策略

#### 移动端优先设计
```css
/* 移动端基础布局 */
.reading-layout {
  /* 默认单栏布局 */
  display: flex;
  flex-direction: column;
  padding: var(--space-4);
  max-width: 100%;
}

.content-area {
  width: 100%;
  max-width: none;
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
}

.control-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--space-3);
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
}

/* 平板适配 */
@media (min-width: 768px) {
  .reading-layout {
    padding: var(--space-6);
  }
  
  .content-area {
    max-width: 65ch; /* 最佳阅读宽度 */
    margin: 0 auto;
    font-size: var(--text-lg);
  }
  
  .control-bar {
    position: static;
    border-top: none;
    border-bottom: 1px solid var(--border-color);
  }
}

/* 桌面端适配 */
@media (min-width: 1024px) {
  .reading-layout {
    flex-direction: row;
    padding: var(--space-8);
    gap: var(--space-8);
  }
  
  .content-area {
    flex: 1;
    max-width: 70ch;
  }
  
  .sidebar {
    width: 300px;
    flex-shrink: 0;
  }
  
  .control-bar {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    bottom: auto;
    left: auto;
    width: auto;
    background: transparent;
    border: none;
  }
}

/* 双栏布局选项 */
@media (min-width: 1280px) {
  .reading-layout.two-column .content-area {
    column-count: 2;
    column-gap: var(--space-8);
    column-rule: 1px solid var(--border-color);
    max-width: none;
  }
}
```

#### 字体响应式缩放
```css
/* 响应式字体大小 */
.content-text {
  font-size: clamp(14px, 2.5vw, 18px);
  line-height: clamp(1.4, 1.5, 1.6);
}

.heading-responsive {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

/* 设备特定优化 */
@media (max-width: 640px) {
  .content-text {
    font-size: 16px; /* iOS最小字体 */
    line-height: 1.6;
  }
}

@media (min-width: 1024px) {
  .content-text {
    font-size: 18px;
    line-height: 1.7;
  }
}
```

### 7.3 交互适配

#### 触摸友好设计
```css
/* 触摸目标最小尺寸 */
.touch-target {
  min-height: 44px; /* iOS推荐 */
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  .button {
    min-height: 48px; /* Android推荐 */
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
  }
  
  .input {
    min-height: 44px;
    padding: var(--space-3);
    font-size: 16px; /* 防止iOS缩放 */
  }
}
```

#### 手势操作区域
```css
/* 滑动操作区域 */
.swipe-area {
  position: relative;
  touch-action: pan-x; /* 只允许水平滑动 */
}

.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.swipe-area:active .swipe-indicator {
  opacity: 0.5;
}

/* 长按操作反馈 */
.long-press-target {
  position: relative;
  user-select: none;
}

.long-press-target::after {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.long-press-target.pressing::after {
  opacity: 1;
}
```

## 8. 组件设计规范

### 8.1 按钮组件

#### 按钮类型和状态
```css
/* 主要按钮 */
.btn-primary {
  background: var(--primary-500);
  color: white;
  border: 1px solid var(--primary-500);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-600);
  border-color: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 次要按钮 */
.btn-secondary {
  background: transparent;
  color: var(--primary-500);
  border: 1px solid var(--primary-500);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--primary-50);
  color: var(--primary-600);
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-500);
  border: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-base);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-text:hover {
  background: var(--primary-50);
  color: var(--primary-600);
}

/* 图标按钮 */
.btn-icon {
  background: transparent;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  color: var(--text-secondary);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
}
```

#### 按钮组合
```css
/* 按钮组 */
.btn-group {
  display: flex;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-right-width: 1px;
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  background: var(--primary-500);
  color: white;
  border: none;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}
```

### 8.2 输入组件

#### 文本输入框
```css
/* 基础输入框 */
.input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input::placeholder {
  color: var(--text-secondary);
}

/* 输入框状态 */
.input.error {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input.success {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.input:disabled {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* 输入框组 */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group .input {
  padding-right: var(--space-10);
}

.input-group .input-icon {
  position: absolute;
  right: var(--space-3);
  color: var(--text-secondary);
  pointer-events: none;
}

.input-group .input-action {
  position: absolute;
  right: var(--space-2);
  pointer-events: auto;
}
```

#### 滑块组件
```css
/* 滑块容器 */
.slider {
  position: relative;
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}

/* 滑块轨道 */
.slider-track {
  position: absolute;
  width: 100%;
  height: 4px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

/* 滑块填充 */
.slider-fill {
  position: absolute;
  height: 4px;
  background: var(--primary-500);
  border-radius: var(--radius-full);
  transition: width 0.1s ease;
}

/* 滑块手柄 */
.slider-thumb {
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid var(--primary-500);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.2s ease;
  transform: translateX(-50%);
}

.slider-thumb:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: var(--shadow-md);
}

.slider-thumb:active {
  transform: translateX(-50%) scale(1.2);
  box-shadow: var(--shadow-lg);
}

/* 滑块标签 */
.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}
```

### 8.3 卡片组件

#### 基础卡片
```css
/* 卡片容器 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

/* 卡片内容 */
.card-content {
  padding: var(--space-6);
}

.card-content p:last-child {
  margin-bottom: 0;
}

/* 卡片底部 */
.card-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 卡片变体 */
.card-outlined {
  box-shadow: none;
  border: 2px solid var(--border-color);
}

.card-elevated {
  border: none;
  box-shadow: var(--shadow-lg);
}

.card-flat {
  box-shadow: none;
  border: none;
  background: transparent;
}
```

### 8.4 模态框组件

#### 模态框结构
```css
/* 模态框遮罩 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* 模态框容器 */
.modal {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s ease;
}

.modal-overlay.open .modal {
  transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 模态框内容 */
.modal-content {
  padding: var(--space-6);
  overflow-y: auto;
  max-height: 60vh;
}

/* 模态框底部 */
.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* 响应式模态框 */
@media (max-width: 640px) {
  .modal {
    max-width: 95vw;
    max-height: 95vh;
    margin: var(--space-4);
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: var(--space-4);
  }
}
```

## 9. 用户流程设计

### 9.1 核心用户流程

#### 首次使用流程
```mermaid
graph TD
    A[用户访问网页] --> B[点击插件图标]
    B --> C[显示欢迎引导]
    C --> D[内容提取中]
    D --> E[重排版显示]
    E --> F[引导设置面板]
    F --> G[用户个性化设置]
    G --> H[开始阅读]
    H --> I[完成首次体验]
```

#### 日常使用流程
```mermaid
graph TD
    A[用户访问网页] --> B[识别可提取内容]
    B --> C[显示插件提示]
    C --> D[用户触发提取]
    D --> E[快速内容提取]
    E --> F[应用用户设置]
    F --> G[重排版显示]
    G --> H[用户阅读]
    H --> I[调整设置/关闭]
```

### 9.2 设置配置流程

#### 个性化设置流程
```mermaid
graph TD
    A[打开设置面板] --> B[选择设置分类]
    B --> C[调整参数]
    C --> D[实时预览效果]
    D --> E{满意效果?}
    E -->|是| F[自动保存设置]
    E -->|否| C
    F --> G[继续其他设置]
    G --> H[完成设置]
```

#### 主题切换流程
```mermaid
graph TD
    A[点击主题按钮] --> B[显示主题选项]
    B --> C[预览主题效果]
    C --> D[选择主题]
    D --> E[应用主题]
    E --> F[保存用户偏好]
    F --> G[更新界面]
```

### 9.3 错误处理流程

#### 内容提取失败流程
```mermaid
graph TD
    A[开始内容提取] --> B[基础算法提取]
    B --> C{提取成功?}
    C -->|是| D[显示内容]
    C -->|否| E[尝试AI增强]
    E --> F{AI提取成功?}
    F -->|是| D
    F -->|否| G[显示错误提示]
    G --> H[提供手动选择]
    H --> I[用户选择内容]
    I --> D
```

#### 网络错误处理流程
```mermaid
graph TD
    A[发起网络请求] --> B{网络连接正常?}
    B -->|是| C[处理响应]
    B -->|否| D[显示离线提示]
    D --> E[提供重试选项]
    E --> F[用户选择重试]
    F --> A
    C --> G{响应成功?}
    G -->|是| H[正常处理]
    G -->|否| I[显示错误信息]
    I --> J[提供解决方案]
```

### 9.4 用户引导设计

#### 新手引导步骤
```typescript
// 引导步骤配置
const onboardingSteps = [
  {
    target: '.plugin-icon',
    title: '欢迎使用阅读助手',
    content: '点击这里开始清洁阅读体验',
    position: 'bottom',
    showSkip: true
  },
  {
    target: '.content-area',
    title: '清洁的阅读界面',
    content: '我们已经为您提取了页面的主要内容，去除了广告和干扰元素',
    position: 'center',
    showSkip: true
  },
  {
    target: '.settings-button',
    title: '个性化设置',
    content: '点击这里可以调整字体、主题、布局等设置',
    position: 'left',
    showSkip: true
  },
  {
    target: '.ai-enhance-button',
    title: 'AI增强功能',
    content: '使用AI技术进一步优化内容质量和阅读体验',
    position: 'bottom',
    showSkip: true
  },
  {
    target: '.keyboard-shortcuts',
    title: '快捷键操作',
    content: '使用键盘快捷键可以更高效地控制阅读器',
    position: 'center',
    showSkip: false,
    isLast: true
  }
];
```

#### 功能发现机制
```css
/* 功能提示动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.feature-hint {
  position: relative;
}

.feature-hint::after {
  content: '';
  position: absolute;
  inset: -4px;
  border: 2px solid var(--primary-500);
  border-radius: inherit;
  animation: pulse 2s infinite;
  pointer-events: none;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--text-primary);
  color: var(--bg-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-base);
  font-size: var(--text-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--text-primary);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-4px);
}
```

## 10. 可用性测试

### 10.1 测试计划

#### 测试目标
- **易用性评估**：验证产品是否易于学习和使用
- **效率测试**：测量用户完成任务的时间和成功率
- **满意度调查**：收集用户对产品的主观评价
- **问题发现**：识别设计和交互中的问题点

#### 测试方法
- **任务导向测试**：设计具体任务场景进行测试
- **A/B测试**：对比不同设计方案的效果
- **眼动追踪**：分析用户的视觉注意力分布
- **访谈调研**：深入了解用户的想法和感受

### 10.2 测试场景设计

#### 核心任务测试
```typescript
// 测试任务配置
const usabilityTasks = [
  {
    id: 'task-1',
    title: '首次使用体验',
    description: '在一个复杂的新闻网站上首次使用插件',
    steps: [
      '访问指定的新闻网站',
      '找到并点击插件图标',
      '完成内容提取和重排版',
      '阅读文章内容'
    ],
    successCriteria: [
      '能够在30秒内找到插件入口',
      '成功提取并显示内容',
      '用户表示满意阅读体验'
    ],
    metrics: ['完成时间', '错误次数', '满意度评分']
  },
  {
    id: 'task-2',
    title: '个性化设置',
    description: '调整阅读界面以适应个人偏好',
    steps: [
      '打开设置面板',
      '调整字体大小',
      '切换主题',
      '调整页面宽度',
      '保存设置'
    ],
    successCriteria: [
      '能够找到所有设置选项',
      '设置变更实时生效',
      '设置成功保存'
    ],
    metrics: ['设置完成时间', '设置准确性', '用户满意度']
  },
  {
    id: 'task-3',
    title: '快捷键使用',
    description: '使用键盘快捷键控制阅读器',
    steps: [
      '使用ESC关闭重排版',
      '重新打开重排版',
      '使用快捷键调整字号',
      '使用快捷键切换主题'
    ],
    successCriteria: [
      '快捷键响应正常',
      '操作结果符合预期',
      '用户认为快捷键有用'
    ],
    metrics: ['快捷键记忆率', '使用成功率', '效率提升']
  }
];
```

#### 错误场景测试
```typescript
// 错误处理测试场景
const errorScenarios = [
  {
    scenario: '内容提取失败',
    trigger: '访问无法提取内容的页面',
    expectedBehavior: '显示友好的错误提示和解决方案',
    testPoints: [
      '错误信息是否清晰',
      '是否提供解决方案',
      '用户是否能够理解和处理'
    ]
  },
  {
    scenario: '网络连接问题',
    trigger: '在网络不稳定环境下使用AI功能',
    expectedBehavior: '显示网络错误提示和重试选项',
    testPoints: [
      '错误检测是否及时',
      '重试机制是否有效',
      '用户体验是否流畅'
    ]
  },
  {
    scenario: '浏览器兼容性问题',
    trigger: '在不同浏览器中使用插件',
    expectedBehavior: '功能正常或显示兼容性提示',
    testPoints: [
      '核心功能是否正常',
      '界面显示是否正确',
      '性能是否可接受'
    ]
  }
];
```

### 10.3 测试指标

#### 定量指标
```typescript
// 可用性测试指标
interface UsabilityMetrics {
  // 效率指标
  taskCompletionTime: number;      // 任务完成时间
  taskSuccessRate: number;         // 任务成功率
  errorRate: number;               // 错误率
  learnabilityScore: number;       // 学习难度评分
  
  // 效果指标
  userSatisfactionScore: number;   // 用户满意度
  systemUsabilityScale: number;   // SUS可用性量表评分
  netPromoterScore: number;        // 净推荐值
  retentionRate: number;           // 用户留存率
  
  // 性能指标
  loadTime: number;                // 加载时间
  responseTime: number;            // 响应时间
  memoryUsage: number;             // 内存使用量
  cpuUsage: number;                // CPU使用率
}
```

#### 定性指标
```typescript
// 用户反馈收集
interface UserFeedback {
  // 主观评价
  overallExperience: string;       // 整体体验描述
  mostLikedFeatures: string[];     // 最喜欢的功能
  mostDislikedFeatures: string[];  // 最不喜欢的功能
  improvementSuggestions: string[]; // 改进建议
  
  // 情感反应
  frustrationPoints: string[];     // 挫折点
  delightMoments: string[];        // 惊喜时刻
  confidenceLevel: number;         // 使用信心
  recommendationWillingness: number; // 推荐意愿
}
```

### 10.4 测试执行计划

#### 测试阶段
```typescript
// 测试执行计划
const testingPhases = [
  {
    phase: 'Alpha测试',
    duration: '2周',
    participants: '内部团队成员',
    focus: '基础功能验证',
    methods: ['专家评审', '启发式评估'],
    deliverables: ['问题清单', '改进建议']
  },
  {
    phase: 'Beta测试',
    duration: '3周',
    participants: '20名目标用户',
    focus: '用户体验验证',
    methods: ['任务测试', '用户访谈'],
    deliverables: ['测试报告', '用户反馈汇总']
  },
  {
    phase: '发布前测试',
    duration: '1周',
    participants: '50名真实用户',
    focus: '最终验证',
    methods: ['A/B测试', '满意度调查'],
    deliverables: ['发布建议', '监控指标']
  }
];
```

## 11. 无障碍设计

### 11.1 无障碍设计原则

#### WCAG 2.1 合规性
- **可感知性（Perceivable）**：信息和UI组件必须以用户能够感知的方式呈现
- **可操作性（Operable）**：UI组件和导航必须是可操作的
- **可理解性（Understandable）**：信息和UI操作必须是可理解的
- **健壮性（Robust）**：内容必须足够健壮，能被各种用户代理解释

#### 设计目标
- 达到WCAG 2.1 AA级标准
- 支持主流屏幕阅读器
- 提供完整的键盘导航
- 确保色彩对比度合规

### 11.2 视觉无障碍设计

#### 色彩对比度
```css
/* 色彩对比度标准 */
:root {
  /* AA级标准：4.5:1 */
  --text-on-light: #1f2937;     /* 对比度 16.84:1 */
  --text-secondary-on-light: #6b7280; /* 对比度 7.07:1 */
  --text-on-dark: #f9fafb;      /* 对比度 18.69:1 */
  --text-secondary-on-dark: #d1d5db;  /* 对比度 9.74:1 */
  
  /* AAA级标准：7:1 */
  --text-high-contrast: #000000; /* 对比度 21:1 */
  --bg-high-contrast: #ffffff;   /* 对比度 21:1 */
}

/* 高对比度模式 */
.high-contrast-mode {
  --bg-primary: var(--bg-high-contrast);
  --text-primary: var(--text-high-contrast);
  --border-color: #000000;
  --primary-500: #0000ff;
  --error-500: #ff0000;
  --success-500: #008000;
}

/* 色彩对比度检查 */
.contrast-check {
  background: var(--bg-primary);
  color: var(--text-primary);
  /* 确保对比度至少4.5:1 */
}
```

#### 字体和排版
```css
/* 无障碍字体设置 */
.accessible-text {
  /* 最小字体大小 */
  font-size: max(16px, 1rem);
  
  /* 适当的行高 */
  line-height: 1.5;
  
  /* 字符间距 */
  letter-spacing: 0.02em;
  
  /* 段落间距 */
  margin-bottom: 1em;
}

/* 大字体模式 */
.large-text-mode {
  font-size: 1.25em;
  line-height: 1.6;
}

/* 阅读障碍友好字体 */
.dyslexia-friendly {
  font-family: 'OpenDyslexic', 'Comic Sans MS', sans-serif;
  font-weight: 400;
  letter-spacing: 0.05em;
  word-spacing: 0.1em;
}
```

### 11.3 键盘导航

#### 焦点管理
```css
/* 焦点指示器 */
.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-base);
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-500);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* 焦点陷阱 */
.focus-trap {
  /* 确保焦点在模态框内循环 */
}

.focus-trap [tabindex="-1"]:focus {
  outline: none;
}
```

#### 键盘导航实现
```typescript
// 键盘导航管理
class KeyboardNavigation {
  private focusableElements: HTMLElement[] = [];
  private currentFocusIndex: number = 0;
  
  constructor(container: HTMLElement) {
    this.updateFocusableElements(container);
    this.bindKeyboardEvents();
  }
  
  private updateFocusableElements(container: HTMLElement) {
    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');
    
    this.focusableElements = Array.from(
      container.querySelectorAll(selector)
    ) as HTMLElement[];
  }
  
  private bindKeyboardEvents() {
    document.addEventListener('keydown', (event) => {
      switch (event.key) {
        case 'Tab':
          this.handleTabNavigation(event);
          break;
        case 'ArrowDown':
        case 'ArrowUp':
          this.handleArrowNavigation(event);
          break;
        case 'Home':
          this.focusFirst();
          event.preventDefault();
          break;
        case 'End':
          this.focusLast();
          event.preventDefault();
          break;
      }
    });
  }
  
  private handleTabNavigation(event: KeyboardEvent) {
    if (event.shiftKey) {
      this.focusPrevious();
    } else {
      this.focusNext();
    }
  }
  
  private focusNext() {
    this.currentFocusIndex = 
      (this.currentFocusIndex + 1) % this.focusableElements.length;
    this.focusableElements[this.currentFocusIndex].focus();
  }
  
  private focusPrevious() {
    this.currentFocusIndex = 
      (this.currentFocusIndex - 1 + this.focusableElements.length) % 
      this.focusableElements.length;
    this.focusableElements[this.currentFocusIndex].focus();
  }
}
```

### 11.4 屏幕阅读器支持

#### ARIA标签和属性
```html
<!-- 语义化HTML结构 -->
<main role="main" aria-label="重排版阅读器">
  <header role="banner">
    <h1 id="article-title">文章标题</h1>
    <nav role="navigation" aria-label="阅读器控制">
      <button 
        aria-label="关闭阅读器" 
        aria-describedby="close-help"
      >
        <span aria-hidden="true">×</span>
      </button>
      <div id="close-help" class="sr-only">
        按ESC键也可以关闭阅读器
      </div>
    </nav>
  </header>
  
  <article role="article" aria-labelledby="article-title">
    <div class="content" aria-live="polite">
      <!-- 文章内容 -->
    </div>
  </article>
  
  <aside role="complementary" aria-label="阅读设置">
    <h2 id="settings-title">个性化设置</h2>
    <div role="group" aria-labelledby="settings-title">
      <label for="font-size">字体大小</label>
      <input 
        type="range" 
        id="font-size"
        min="12" 
        max="24" 
        value="16"
        aria-describedby="font-size-help"
        aria-valuetext="16像素"
      >
      <div id="font-size-help" class="sr-only">
        使用左右箭头键调整字体大小
      </div>
    </div>
  </aside>
</main>

<!-- 状态公告 -->
<div 
  role="status" 
  aria-live="polite" 
  aria-atomic="true"
  class="sr-only"
  id="status-announcements"
>
  <!-- 动态状态更新 -->
</div>
```

#### 屏幕阅读器优化
```typescript
// 屏幕阅读器支持类
class ScreenReaderSupport {
  private announcer: HTMLElement;
  
  constructor() {
    this.createAnnouncer();
  }
  
  private createAnnouncer() {
    this.announcer = document.createElement('div');
    this.announcer.setAttribute('role', 'status');
    this.announcer.setAttribute('aria-live', 'polite');
    this.announcer.setAttribute('aria-atomic', 'true');
    this.announcer.className = 'sr-only';
    document.body.appendChild(this.announcer);
  }
  
  announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    this.announcer.setAttribute('aria-live', priority);
    this.announcer.textContent = message;
    
    // 清除消息以便下次公告
    setTimeout(() => {
      this.announcer.textContent = '';
    }, 1000);
  }
  
  announcePageChange(title: string) {
    this.announce(`页面已切换到：${title}`, 'assertive');
  }
  
  announceSettingChange(setting: string, value: string) {
    this.announce(`${setting}已更改为${value}`);
  }
  
  announceError(error: string) {
    this.announce(`错误：${error}`, 'assertive');
  }
}

// 使用示例
const screenReader = new ScreenReaderSupport();

// 内容加载完成
screenReader.announce('文章内容已加载完成');

// 设置更改
screenReader.announceSettingChange('字体大小', '18像素');

// 错误处理
screenReader.announceError('无法提取页面内容，请尝试刷新页面');
```

### 11.5 运动和动画控制

#### 减少动画偏好
```css
/* 尊重用户的动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* 保留必要的功能性动画 */
  .loading-spinner {
    animation: none;
  }
  
  .loading-spinner::after {
    content: '加载中...';
  }
}

/* 可选的动画控制 */
.animations-disabled * {
  animation: none !important;
  transition: none !important;
}

/* 安全的动画实现 */
@keyframes safe-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.safe-animation {
  animation: safe-fade-in 0.3s ease-out;
}

@media (prefers-reduced-motion: reduce) {
  .safe-animation {
    animation: none;
    opacity: 1;
  }
}
```

## 12. 设计交付

### 12.1 设计资产

#### 设计文件结构
```
design-assets/
├── ui-kit/
│   ├── components.fig          # Figma组件库
│   ├── design-tokens.json      # 设计令牌
│   └── icon-library.svg        # 图标库
├── prototypes/
│   ├── desktop-prototype.fig   # 桌面端原型
│   ├── mobile-prototype.fig    # 移动端原型
│   └── interaction-flows.fig   # 交互流程
├── specifications/
│   ├── component-specs.pdf     # 组件规范
│   ├── layout-specs.pdf        # 布局规范
│   └── interaction-specs.pdf   # 交互规范
└── assets/
    ├── icons/                  # 图标资源
    ├── images/                 # 图片资源
    └── fonts/                  # 字体文件
```

#### 设计令牌导出
```json
{
  "color": {
    "primary": {
      "50": { "value": "#eff6ff" },
      "100": { "value": "#dbeafe" },
      "500": { "value": "#3b82f6" },
      "900": { "value": "#1e3a8a" }
    },
    "semantic": {
      "success": { "value": "#10b981" },
      "warning": { "value": "#f59e0b" },
      "error": { "value": "#ef4444" }
    }
  },
  "spacing": {
    "xs": { "value": "4px" },
    "sm": { "value": "8px" },
    "md": { "value": "16px" },
    "lg": { "value": "24px" },
    "xl": { "value": "32px" }
  },
  "typography": {
    "fontFamily": {
      "sans": { "value": "Inter, system-ui, sans-serif" },
      "mono": { "value": "SF Mono, Consolas, monospace" }
    },
    "fontSize": {
      "sm": { "value": "14px" },
      "base": { "value": "16px" },
      "lg": { "value": "18px" },
      "xl": { "value": "20px" }
    }
  },
  "borderRadius": {
    "sm": { "value": "4px" },
    "md": { "value": "6px" },
    "lg": { "value": "8px" },
    "full": { "value": "9999px" }
  },
  "boxShadow": {
    "sm": { "value": "0 1px 2px 0 rgba(0, 0, 0, 0.05)" },
    "base": { "value": "0 1px 3px 0 rgba(0, 0, 0, 0.1)" },
    "lg": { "value": "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }
  }
}
```

### 12.2 开发交接

#### 组件开发指南
```typescript
// 组件开发规范示例
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'text' | 'icon';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  'aria-label'?: string;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  children,
  onClick,
  'aria-label': ariaLabel,
  ...props
}) => {
  const baseClasses = 'btn';
  const variantClasses = `btn-${variant}`;
  const sizeClasses = `btn-${size}`;
  const stateClasses = [
    disabled && 'btn-disabled',
    loading && 'btn-loading'
  ].filter(Boolean).join(' ');
  
  return (
    <button
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${stateClasses}`}
      disabled={disabled || loading}
      onClick={onClick}
      aria-label={ariaLabel}
      {...props}
    >
      {loading && <LoadingSpinner />}
      <span className={loading ? 'sr-only' : ''}>{children}</span>
    </button>
  );
};
```

#### CSS实现指南
```css
/* 按钮组件CSS实现 */
.btn {
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  
  /* 无障碍 */
  &:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
  
  /* 禁用状态 */
  &.btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  /* 加载状态 */
  &.btn-loading {
    cursor: wait;
    position: relative;
  }
}

/* 变体样式 */
.btn-primary {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  
  &:hover:not(.btn-disabled) {
    background: var(--primary-600);
    border-color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

/* 尺寸样式 */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-base);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
  border-radius: var(--radius-lg);
}
```

### 12.3 质量保证

#### 设计审查清单
```typescript
// 设计质量检查清单
interface DesignQualityChecklist {
  accessibility: {
    colorContrast: boolean;        // 色彩对比度符合WCAG AA
    keyboardNavigation: boolean;   // 支持键盘导航
    screenReaderSupport: boolean;  // 屏幕阅读器支持
    focusIndicators: boolean;      // 焦点指示器清晰
    altText: boolean;              // 图片有替代文本
  };
  
  responsiveness: {
    mobileOptimized: boolean;      // 移动端优化
    tabletSupport: boolean;        // 平板支持
    desktopLayout: boolean;        // 桌面布局
    touchTargets: boolean;         // 触摸目标大小合适
    textReadability: boolean;      // 文字可读性
  };
  
  consistency: {
    designSystem: boolean;         // 遵循设计系统
    componentReuse: boolean;       // 组件复用
    visualHierarchy: boolean;      // 视觉层次清晰
    brandAlignment: boolean;       // 品牌一致性
    interactionPatterns: boolean;  // 交互模式一致
  };
  
  usability: {
    userFlowLogical: boolean;      // 用户流程逻辑
    errorHandling: boolean;        // 错误处理完善
    feedbackClear: boolean;        // 反馈信息清晰
    loadingStates: boolean;        // 加载状态明确
    emptyStates: boolean;          // 空状态设计
  };
}
```

#### 设计验收标准
```typescript
// 设计验收标准
const designAcceptanceCriteria = {
  visual: {
    pixelPerfect: '与设计稿误差<2px',
    colorAccuracy: '颜色值完全匹配',
    typographyConsistent: '字体、字号、行高一致',
    spacingAccurate: '间距符合设计规范',
    animationSmooth: '动画流畅，60fps'
  },
  
  functional: {
    allInteractionsWork: '所有交互功能正常',
    responsiveLayout: '响应式布局正确',
    keyboardAccessible: '键盘操作完整',
    screenReaderCompatible: '屏幕阅读器兼容',
    performanceOptimal: '性能指标达标'
  },
  
  content: {
    textAccurate: '文案内容准确',
    imagesOptimized: '图片优化完成',
    iconsConsistent: '图标风格一致',
    copywritingApproved: '文案已审核通过',
    localizationReady: '国际化准备就绪'
  }
};
```