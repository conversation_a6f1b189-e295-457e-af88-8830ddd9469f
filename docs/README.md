# 阅读助手浏览器扩展 - React+TypeScript版本

> 跨浏览器网页内容提取与重排版插件，现代化技术栈重构版本

## 🚀 技术栈升级

本项目已从原生JavaScript升级为现代化的技术栈：

- **React 18** - 现代化UI框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具
- **Zustand** - 轻量级状态管理
- **ESLint + Prettier** - 代码质量保证

## 📁 项目结构

```
src/
├── background/          # 后台脚本
├── content/            # 内容脚本
│   ├── extractors/     # 内容提取器
│   ├── renderers/      # 渲染器
│   └── handlers/       # 事件处理器
├── popup/              # 弹出窗口
├── options/            # 选项页面
├── components/         # 共享组件
├── hooks/              # React Hooks
├── stores/             # 状态管理
├── types/              # TypeScript类型定义
└── utils/              # 工具函数
```

## 🛠️ 开发环境

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
```

### 代码格式化

```bash
npm run format
npm run format:check
```

## 🔧 浏览器扩展开发

### 加载扩展到浏览器

1. 运行 `npm run build` 构建项目
2. 打开浏览器扩展管理页面
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录

### 支持的浏览器

- ✅ Chrome (Manifest V3)
- ✅ Firefox (Manifest V3)
- ✅ Edge (Manifest V3)
- ✅ Safari (部分功能)

## 📋 功能特性

### 已实现功能 (MVP)

- ✅ 智能内容提取
- ✅ 阅读模式渲染
- ✅ 基础个性化设置
- ✅ 键盘快捷键支持
- ✅ 跨浏览器兼容

### 计划功能 (Beta)

- 🔄 AI增强内容提取
- 🔄 完整主题系统
- 🔄 高级布局设置
- 🔄 用户反馈机制
- 🔄 性能优化

## 🎨 设计系统

项目遵循统一的设计系统：

- **主题**: 6种预设主题（明亮、深色、护眼等）
- **字体**: 支持中英文字体选择
- **布局**: 响应式设计，支持多种屏幕尺寸
- **交互**: 微动画和过渡效果

## 🧪 测试

### 单元测试

```bash
npm run test
```

### E2E测试

```bash
npm run test:e2e
```

## 📝 开发规范

### 代码风格

- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 使用Prettier格式化代码
- 组件使用函数式组件和Hooks

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

## 🔍 调试

### 开发者工具

1. 在浏览器中打开开发者工具
2. 切换到"扩展"或"Extensions"面板
3. 查看console输出和网络请求

### 常见问题

1. **扩展无法加载**: 检查manifest.json语法
2. **内容脚本不工作**: 检查权限配置
3. **样式不生效**: 检查CSS文件路径

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系

如有问题，请通过GitHub Issues联系我们。
