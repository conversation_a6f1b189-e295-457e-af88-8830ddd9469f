// Options主组件
import React, { useEffect, useState } from 'react';
import { useSettings } from '@/hooks/useSettings';
import {
  THEME_CONFIGS,
  FONT_OPTIONS,
  WIDTH_OPTIONS,
  LINE_HEIGHT_OPTIONS,
  FONT_SIZE_RANGE,
} from '@/utils/constants';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, Download, Upload, RotateCcw, Palette, Settings2 } from 'lucide-react';

const OptionsApp: React.FC = () => {
  const { settings, updateSetting, resetToDefaults, exportSettings, importSettings } =
    useSettings();
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  // 处理设置变更
  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    updateSetting(key, value);
    setSaveStatus('saving');

    // 显示保存状态
    setTimeout(() => setSaveStatus('saved'), 500);
    setTimeout(() => setSaveStatus('idle'), 2000);
  };

  // 重置设置
  const handleReset = () => {
    if (confirm('确定要重置所有设置到默认值吗？')) {
      resetToDefaults();
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 2000);
    }
  };

  // 导出设置
  const handleExport = () => {
    try {
      const settingsJson = exportSettings();
      const blob = new Blob([settingsJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'reading-assistant-settings.json';
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出设置失败:', error);
      alert('导出设置失败');
    }
  };

  // 导入设置
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = e => {
          try {
            const settingsJson = e.target?.result as string;
            importSettings(settingsJson);
            setSaveStatus('saved');
            setTimeout(() => setSaveStatus('idle'), 2000);
          } catch (error) {
            console.error('导入设置失败:', error);
            alert('导入设置失败：文件格式不正确');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-5">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-bold">阅读助手设置</CardTitle>
              <div className="flex items-center gap-2">
                {saveStatus === 'saving' && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    <Save className="w-3 h-3 mr-1" />
                    保存中...
                  </Badge>
                )}
                {saveStatus === 'saved' && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    已保存
                  </Badge>
                )}
                {saveStatus === 'error' && <Badge variant="destructive">保存失败</Badge>}
              </div>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 外观设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5" />
                外观设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <Label>主题</Label>
                <div className="grid grid-cols-3 gap-2">
                  {Object.entries(THEME_CONFIGS).map(([key, config]) => (
                    <Button
                      key={key}
                      variant={settings.theme === key ? 'default' : 'outline'}
                      className="h-auto p-3 flex flex-col items-center gap-1"
                      onClick={() => handleSettingChange('theme', key)}
                    >
                      <div
                        className="w-6 h-6 rounded-full border-2"
                        style={{ backgroundColor: config.bg, borderColor: config.border }}
                      />
                      <span className="text-xs">
                        {key === 'light' && '明亮'}
                        {key === 'dark' && '深色'}
                        {key === 'sepia' && '护眼'}
                        {key === 'nature' && '自然'}
                        {key === 'warm' && '温馨'}
                        {key === 'eyecare' && '纸质'}
                      </span>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <Label>字体大小: {settings.fontSize}px</Label>
                <Slider
                  value={[settings.fontSize]}
                  onValueChange={value => handleSettingChange('fontSize', value[0])}
                  min={FONT_SIZE_RANGE.min}
                  max={FONT_SIZE_RANGE.max}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="space-y-3">
                <Label>字体</Label>
                <Select
                  value={settings.fontFamily}
                  onValueChange={value => handleSettingChange('fontFamily', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FONT_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label>行高: {settings.lineHeight}</Label>
                <Select
                  value={settings.lineHeight.toString()}
                  onValueChange={value => handleSettingChange('lineHeight', parseFloat(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {LINE_HEIGHT_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label} ({option.value})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label>内容宽度: {settings.contentWidth}%</Label>
                <Select
                  value={settings.contentWidth.toString()}
                  onValueChange={value => handleSettingChange('contentWidth', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {WIDTH_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 功能设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings2 className="w-5 h-5" />
                功能设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableAI"
                  checked={settings.enableAI}
                  onCheckedChange={checked => handleSettingChange('enableAI', checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="enableAI"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    启用AI增强功能
                  </Label>
                  <p className="text-xs text-muted-foreground">使用AI技术优化内容提取和排版效果</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoExtract"
                  checked={settings.autoExtract}
                  onCheckedChange={checked => handleSettingChange('autoExtract', checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="autoExtract"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    自动提取内容
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    页面加载完成后自动分析并提取主要内容
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="keyboardShortcuts"
                  checked={settings.keyboardShortcuts}
                  onCheckedChange={checked => handleSettingChange('keyboardShortcuts', checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="keyboardShortcuts"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    启用键盘快捷键
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    支持Esc关闭、Ctrl+/-调节字号等快捷键
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="showProgress"
                  checked={settings.showProgress}
                  onCheckedChange={checked => handleSettingChange('showProgress', checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor="showProgress"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    显示进度指示
                  </Label>
                  <p className="text-xs text-muted-foreground">在内容提取和处理时显示进度条</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 设置管理 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings2 className="w-5 h-5" />
              设置管理
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button onClick={handleExport} variant="outline" className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                导出设置
              </Button>
              <Button onClick={handleImport} variant="outline" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                导入设置
              </Button>
              <Button
                onClick={handleReset}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <RotateCcw className="w-4 h-4" />
                重置设置
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 关于信息 */}
        <Card>
          <CardHeader>
            <CardTitle>关于</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>
                <strong className="text-foreground">阅读助手</strong> v0.2.0
              </p>
              <p>跨浏览器网页内容提取与重排版插件</p>
              <p>React + TypeScript 现代化版本</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OptionsApp;
