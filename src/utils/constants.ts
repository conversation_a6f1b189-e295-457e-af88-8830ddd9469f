// 应用常量定义
import type { ThemeConfig } from '@/types';

// 主题配置映射
export const THEME_CONFIGS: Record<string, ThemeConfig> = {
  light: {
    bg: '#ffffff',
    secondary: '#f9fafb',
    text: '#1f2937',
    border: '#e5e7eb'
  },
  dark: {
    bg: '#1f2937',
    secondary: '#374151',
    text: '#f9fafb',
    border: '#4b5563'
  },
  sepia: {
    bg: '#f7f3e9',
    secondary: '#f0e6d2',
    text: '#5d4e37',
    border: '#d4c4a8'
  },
  nature: {
    bg: '#f0f8f0',
    secondary: '#e8f5e8',
    text: '#2d5016',
    border: '#c8e6c9'
  },
  warm: {
    bg: '#fdf2f8',
    secondary: '#fce7f3',
    text: '#7c2d12',
    border: '#fce7f3'
  },
  eyecare: {
    bg: '#f5f5f5',
    secondary: '#eeeeee',
    text: '#424242',
    border: '#d0d0d0'
  }
};

// 字体选项
export const FONT_OPTIONS = [
  { value: 'system-ui, sans-serif', label: '系统默认' },
  { value: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif', label: '系统字体' },
  { value: '"Source Han Sans CN", "Noto Sans CJK SC", "PingFang SC", "Microsoft YaHei", sans-serif', label: '中文无衬线' },
  { value: '"Source Han Serif CN", "Noto Serif CJK SC", "Songti SC", "SimSun", serif', label: '中文衬线' },
  { value: 'Georgia, "Times New Roman", Times, serif', label: '英文衬线' },
  { value: '"Source Sans Pro", "Helvetica Neue", Arial, sans-serif', label: '英文无衬线' }
];

// 内容宽度选项
export const WIDTH_OPTIONS = [
  { value: 45, label: '窄' },
  { value: 55, label: '较窄' },
  { value: 65, label: '标准' },
  { value: 75, label: '较宽' },
  { value: 85, label: '宽' }
];

// 行高选项
export const LINE_HEIGHT_OPTIONS = [
  { value: 1.4, label: '紧凑' },
  { value: 1.6, label: '标准' },
  { value: 1.8, label: '舒适' },
  { value: 2.0, label: '宽松' }
];

// 字号范围
export const FONT_SIZE_RANGE = {
  min: 12,
  max: 24,
  default: 16
};

// 键盘快捷键映射
export const KEYBOARD_SHORTCUTS = {
  CLOSE_READER: 'Escape',
  INCREASE_FONT: 'Equal',
  DECREASE_FONT: 'Minus',
  TOGGLE_THEME: 'KeyT',
  TOGGLE_WIDTH: 'KeyW',
  OPEN_SETTINGS: 'KeyS',
  TOGGLE_AI: 'KeyA',
  TOGGLE_FULLSCREEN: 'F11'
};

// 内容提取选择器 - 按优先级排序，针对复杂网站优化
export const CONTENT_SELECTORS = [
  // 语义化标签（最高优先级）
  'article',
  'main',
  '[role="main"]',
  '[role="article"]',
  '[role="document"]',

  // 新闻网站专用选择器
  '.story-body',
  '.article-body',
  '.story-content',
  '.article-content',
  '.news-content',
  '.story-text',
  '.article-text',
  '.content-body',
  '.post-content',
  '.entry-content',

  // Medium和现代博客平台
  '.postArticle-content',
  '.section-content',
  '.markup',
  '.post-body',
  '.entry-body',
  '.blog-content',
  '.blog-post',
  '.post-text',
  '.entry-text',

  // 技术文档和学术网站
  '.documentation',
  '.doc-content',
  '.wiki-content',
  '.paper-content',
  '.abstract',
  '.full-text',
  '.research-content',

  // 电商产品页面
  '.product-description',
  '.product-details',
  '.product-content',
  '.item-description',
  '.product-info',

  // 通用内容容器
  '.main-content',
  '.page-content',
  '.content-area',
  '.primary-content',
  '.content-wrapper',
  '.content-container',

  // 通用选择器
  '.content',
  '#content',
  '.text',
  '.article',
  '.post',
  '.entry',
  '.story',

  // 特定CMS和平台
  '.wp-content',
  '.drupal-content',
  '.joomla-content',
  '.ghost-content',
  '.medium-content',

  // 动态内容容器
  '[data-content]',
  '[data-article]',
  '[data-post]',

  // 通配符模式（最低优先级）
  '[class*="content"]',
  '[class*="article"]',
  '[class*="post"]',
  '[class*="story"]',
  '[class*="text"]',
  '[id*="content"]',
  '[id*="article"]',
  '[id*="post"]'
];

// 需要移除的元素选择器
export const REMOVE_SELECTORS = [
  // 脚本和样式
  'script',
  'style',
  'noscript',
  'link[rel="stylesheet"]',

  // 嵌入内容
  'iframe',
  'object',
  'embed',
  'video',
  'audio',

  // 广告相关
  '.advertisement',
  '.ad',
  '.ads',
  '.advert',
  '.ad-container',
  '.ad-banner',
  '.ad-block',
  '.google-ad',
  '.adsense',
  '[class*="ad-"]',
  '[id*="ad-"]',
  '[class*="ads"]',
  '[id*="ads"]',

  // 导航和菜单
  'nav',
  '.navigation',
  '.nav',
  '.navbar',
  '.menu',
  '.breadcrumb',
  '.breadcrumbs',
  '.pagination',
  '.pager',

  // 页眉页脚
  'header',
  'footer',
  '.header',
  '.footer',
  '.site-header',
  '.site-footer',
  '.page-header',
  '.page-footer',

  // 侧边栏
  'aside',
  '.sidebar',
  '.side-bar',
  '.widget',
  '.widgets',

  // 社交和分享
  '.social',
  '.social-share',
  '.share',
  '.sharing',
  '.social-media',
  '.social-buttons',

  // 评论系统
  '.comments',
  '.comment',
  '.comment-section',
  '.disqus',
  '.facebook-comments',

  // 相关内容
  '.related',
  '.related-posts',
  '.related-articles',
  '.recommended',
  '.suggestions',
  '.more-stories',

  // 表单和输入
  'form',
  'input',
  'textarea',
  'select',
  'button[type="submit"]',
  '.search',
  '.search-form',
  '.newsletter',
  '.subscribe',

  // 其他干扰元素
  '.popup',
  '.modal',
  '.overlay',
  '.banner',
  '.notice',
  '.alert',
  '.notification',
  '.cookie-notice',
  '.gdpr',
  '.promo',
  '.promotion',
  '.sponsor',
  '.sponsored',
  '.affiliate',

  // 隐藏元素
  '[style*="display: none"]',
  '[style*="visibility: hidden"]',
  '.hidden',
  '.invisible',
  '.sr-only',

  // 特定平台
  '.twitter-tweet',
  '.instagram-media',
  '.facebook-post',
  '.youtube-player',

  // 新增：更精确的干扰元素过滤
  // 导航相关
  '.nav-menu',
  '.main-nav',
  '.site-nav',
  '.top-nav',
  '.bottom-nav',
  '.nav-links',
  '.menu-item',
  '.menu-list',

  // 广告和推广（更精确）
  '.ad-wrapper',
  '.ad-content',
  '.ad-space',
  '.advertisement-wrapper',
  '.sponsored-content',
  '.promo-box',
  '.promotion-banner',
  '.affiliate-link',
  '.affiliate-disclosure',

  // 社交和分享（更全面）
  '.social-links',
  '.social-icons',
  '.share-buttons',
  '.sharing-tools',
  '.follow-buttons',
  '.social-follow',
  '.like-button',
  '.tweet-button',

  // 评论和互动
  '.comment-form',
  '.comment-list',
  '.comment-thread',
  '.discussion',
  '.feedback',
  '.rating',
  '.vote',
  '.poll',

  // 相关内容和推荐
  '.related-articles',
  '.recommended-posts',
  '.you-might-like',
  '.more-from-author',
  '.trending',
  '.popular',
  '.latest',
  '.recent-posts',

  // 订阅和通知
  '.newsletter-signup',
  '.email-signup',
  '.subscription',
  '.notification',
  '.alert-bar',
  '.announcement',
  '.cookie-banner',
  '.gdpr-notice',

  // 电商相关
  '.price',
  '.buy-button',
  '.add-to-cart',
  '.checkout',
  '.shipping',
  '.reviews',
  '.rating-stars',
  '.product-options',

  // 技术和调试
  '.debug',
  '.dev-tools',
  '.admin-bar',
  '.edit-link',
  '.version-info'
];

// 性能配置
export const PERFORMANCE_CONFIG = {
  EXTRACTION_TIMEOUT: 5000, // 内容提取超时时间（毫秒）
  RENDER_TIMEOUT: 2000,      // 渲染超时时间（毫秒）
  MAX_CONTENT_LENGTH: 1000000, // 最大内容长度
  CACHE_DURATION: 7 * 24 * 60 * 60 * 1000, // 缓存持续时间（7天）
};

// 错误消息
export const ERROR_MESSAGES = {
  EXTRACTION_FAILED: '内容提取失败，请尝试刷新页面',
  SPECIAL_PAGE: '无法在此类页面启用阅读模式',
  PERMISSION_DENIED: '权限不足，无法访问页面内容',
  NETWORK_ERROR: '网络连接错误，请检查网络设置',
  AI_SERVICE_ERROR: 'AI服务暂时不可用，已切换到基础模式',
  SETTINGS_SAVE_ERROR: '设置保存失败，请重试'
};

// 应用元数据
export const APP_METADATA = {
  NAME: '阅读助手',
  VERSION: '0.2.0',
  DESCRIPTION: '智能网页阅读助手',
  AUTHOR: '阅读助手团队',
  HOMEPAGE: 'https://github.com/reading-assistant/extension'
};
