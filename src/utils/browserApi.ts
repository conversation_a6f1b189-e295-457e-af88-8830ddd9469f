// 浏览器API适配层 - 基于架构文档设计
import type { BrowserMessage } from '@/types';

// 声明全局browser对象（Firefox等浏览器）
declare global {
  // eslint-disable-next-line no-unused-vars
  const browser: typeof chrome;
}

/**
 * 统一的浏览器API适配器
 * 支持Chrome、Firefox、Safari、Edge等主流浏览器
 */
export class BrowserAPI {
  private static instance: BrowserAPI;
  
  static getInstance(): BrowserAPI {
    if (!BrowserAPI.instance) {
      BrowserAPI.instance = new BrowserAPI();
    }
    return BrowserAPI.instance;
  }
  
  /**
   * 统一的存储API
   */
  async setStorage(key: string, value: any): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        return chrome.storage.local.set({ [key]: value });
      } else if (typeof browser !== 'undefined' && browser.storage) {
        return browser.storage.local.set({ [key]: value });
      } else {
        localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error('Failed to set storage:', error);
      throw error;
    }
  }
  
  async getStorage(key: string): Promise<any> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(key);
        return result[key];
      } else if (typeof browser !== 'undefined' && browser.storage) {
        const result = await browser.storage.local.get(key);
        return result[key];
      } else {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : undefined;
      }
    } catch (error) {
      console.error('Failed to get storage:', error);
      return undefined;
    }
  }
  
  async removeStorage(key: string): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        return chrome.storage.local.remove(key);
      } else if (typeof browser !== 'undefined' && browser.storage) {
        return browser.storage.local.remove(key);
      } else {
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Failed to remove storage:', error);
      throw error;
    }
  }
  
  async clearStorage(): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        return chrome.storage.local.clear();
      } else if (typeof browser !== 'undefined' && browser.storage) {
        return browser.storage.local.clear();
      } else {
        localStorage.clear();
      }
    } catch (error) {
      console.error('Failed to clear storage:', error);
      throw error;
    }
  }
  
  /**
   * 统一的消息传递API
   */
  async sendMessage(message: BrowserMessage): Promise<any> {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        return chrome.runtime.sendMessage(message);
      } else if (typeof browser !== 'undefined' && browser.runtime) {
        return browser.runtime.sendMessage(message);
      } else {
        throw new Error('Browser API not available');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前活动标签页
   */
  async getCurrentTab(): Promise<any> {
    try {
      if (typeof chrome !== 'undefined' && chrome.tabs) {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        return tabs[0];
      } else if (typeof browser !== 'undefined' && browser.tabs) {
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        return tabs[0];
      } else {
        throw new Error('Tabs API not available');
      }
    } catch (error) {
      console.error('Failed to get current tab:', error);
      throw error;
    }
  }
  
  /**
   * 向标签页发送消息
   */
  async sendTabMessage(tabId: number, message: BrowserMessage): Promise<any> {
    try {
      if (typeof chrome !== 'undefined' && chrome.tabs) {
        return chrome.tabs.sendMessage(tabId, message);
      } else if (typeof browser !== 'undefined' && browser.tabs) {
        return browser.tabs.sendMessage(tabId, message);
      } else {
        throw new Error('Tabs API not available');
      }
    } catch (error) {
      console.error('Failed to send tab message:', error);
      throw error;
    }
  }
  
  /**
   * 检查是否是特殊页面（无法注入脚本的页面）
   */
  isSpecialPage(url: string): boolean {
    const specialProtocols = [
      'chrome://',
      'chrome-extension://',
      'moz-extension://',
      'edge://',
      'about:',
      'data:',
      'file://'
    ];
    
    return specialProtocols.some(protocol => url.startsWith(protocol));
  }
}
