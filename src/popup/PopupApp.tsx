// Popup主组件
import React, { useState, useEffect } from 'react';
import { useAppStore } from '@/stores/appStore';
import { BrowserAPI } from '@/utils/browserApi';
import { ERROR_MESSAGES } from '@/utils/constants';
import type { BrowserMessage } from '@/types';

const PopupApp: React.FC = () => {
  const { isLoading, setLoading, error, setError } = useAppStore();
  const [currentTab, setCurrentTab] = useState<any>(null);

  // 获取当前标签页信息
  useEffect(() => {
    const getCurrentTab = async () => {
      try {
        const browserAPI = BrowserAPI.getInstance();
        const tab = await browserAPI.getCurrentTab();
        setCurrentTab(tab);
      } catch (error) {
        console.error('获取当前标签页失败:', error);
        setError('无法获取当前页面信息');
      }
    };

    getCurrentTab();
  }, [setError]);

  // 切换阅读模式
  const handleToggleReader = async () => {
    if (!currentTab?.id) {
      setError('无法获取当前页面信息');
      return;
    }

    // 检查是否是特殊页面
    const browserAPI = BrowserAPI.getInstance();
    if (currentTab.url && browserAPI.isSpecialPage(currentTab.url)) {
      setError(ERROR_MESSAGES.SPECIAL_PAGE);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('[阅读助手] 开始处理阅读模式切换');

      // 首先尝试注入content script（确保脚本存在）
      try {
        await chrome.scripting.executeScript({
          target: { tabId: currentTab.id },
          files: ['content.js'],
        });

        await chrome.scripting.insertCSS({
          target: { tabId: currentTab.id },
          files: ['content.css'],
        });

        console.log('[阅读助手] Content script注入成功');
      } catch (injectionError) {
        console.warn('[阅读助手] Content script注入失败:', injectionError);
        // 继续尝试，可能脚本已经存在
      }

      // 等待脚本初始化
      await new Promise(resolve => setTimeout(resolve, 500));

      // 发送切换消息
      const message: BrowserMessage = { type: 'TOGGLE_READER' };

      // 重试机制
      let retryCount = 0;
      const maxRetries = 5;
      let lastError: any;

      while (retryCount < maxRetries) {
        try {
          console.log(`[阅读助手] 尝试发送消息 (${retryCount + 1}/${maxRetries})`);

          const response = await chrome.tabs.sendMessage(currentTab.id, message);
          console.log('[阅读助手] 消息发送成功，响应:', response);

          // 检查响应状态
          if (response && response.success === false) {
            throw new Error(response.error || '阅读模式启动失败');
          }

          // 关闭弹出窗口
          window.close();
          return;
        } catch (error) {
          lastError = error;
          retryCount++;
          console.warn(`[阅读助手] 消息发送失败 (${retryCount}/${maxRetries}):`, error);

          if (retryCount < maxRetries) {
            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
      }

      // 所有重试都失败了
      throw new Error(
        `消息发送失败，已重试${maxRetries}次。最后错误: ${lastError?.message || lastError}`
      );
    } catch (error) {
      console.error('[阅读助手] 处理失败:', error);
      setError('无法启动阅读模式，请刷新页面后重试');
    } finally {
      setLoading(false);
    }
  };

  // 打开选项页面
  const handleOpenOptions = () => {
    chrome.runtime.openOptionsPage();
    window.close();
  };

  return (
    <div className="popup-container">
      <div className="popup-header">
        <h1 className="popup-title">阅读助手</h1>
        <span className="popup-version">v0.2.0</span>
      </div>

      <div className="popup-content">
        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span className="error-text">{error}</span>
          </div>
        )}

        <div className="popup-actions">
          <button
            className="action-button primary"
            onClick={handleToggleReader}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="loading-spinner"></span>
                处理中...
              </>
            ) : (
              '开启/关闭 阅读模式'
            )}
          </button>

          <button
            className="action-button secondary"
            onClick={handleOpenOptions}
            disabled={isLoading}
          >
            设置选项
          </button>
        </div>

        <div className="popup-tips">
          <p className="tip-text">
            💡 提示：可在页面中按 <kbd>Esc</kbd> 关闭阅读模式
          </p>
          {currentTab?.url && (
            <p className="current-url">当前页面：{new URL(currentTab.url).hostname}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PopupApp;
