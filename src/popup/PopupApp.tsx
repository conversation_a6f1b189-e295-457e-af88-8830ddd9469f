// Popup主组件
import React from 'react';
import { useBrowserAPI } from '@/hooks/useBrowserAPI';
import { ERROR_MESSAGES } from '@/utils/constants';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Settings, BookOpen, AlertTriangle } from 'lucide-react';
import ErrorBoundary from '@/components/shared/ErrorBoundary';

const PopupApp: React.FC = () => {
  const { currentTab, isLoading, error, setError, sendMessage, isSpecialPage, openOptionsPage } =
    useBrowserAPI();

  // 切换阅读模式
  const handleToggleReader = async () => {
    if (!currentTab?.id) {
      setError('无法获取当前页面信息');
      return;
    }

    // 检查是否是特殊页面
    const browserAPI = BrowserAPI.getInstance();
    if (currentTab.url && browserAPI.isSpecialPage(currentTab.url)) {
      setError(ERROR_MESSAGES.SPECIAL_PAGE);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('[阅读助手] 开始处理阅读模式切换');

      // 首先尝试注入content script（确保脚本存在）
      try {
        await chrome.scripting.executeScript({
          target: { tabId: currentTab.id },
          files: ['content.js'],
        });

        await chrome.scripting.insertCSS({
          target: { tabId: currentTab.id },
          files: ['content.css'],
        });

        console.log('[阅读助手] Content script注入成功');
      } catch (injectionError) {
        console.warn('[阅读助手] Content script注入失败:', injectionError);
        // 继续尝试，可能脚本已经存在
      }

      // 等待脚本初始化
      await new Promise(resolve => setTimeout(resolve, 500));

      // 发送切换消息
      const message: BrowserMessage = { type: 'TOGGLE_READER' };

      // 重试机制
      let retryCount = 0;
      const maxRetries = 5;
      let lastError: any;

      while (retryCount < maxRetries) {
        try {
          console.log(`[阅读助手] 尝试发送消息 (${retryCount + 1}/${maxRetries})`);

          const response = await chrome.tabs.sendMessage(currentTab.id, message);
          console.log('[阅读助手] 消息发送成功，响应:', response);

          // 检查响应状态
          if (response && response.success === false) {
            throw new Error(response.error || '阅读模式启动失败');
          }

          // 关闭弹出窗口
          window.close();
          return;
        } catch (error) {
          lastError = error;
          retryCount++;
          console.warn(`[阅读助手] 消息发送失败 (${retryCount}/${maxRetries}):`, error);

          if (retryCount < maxRetries) {
            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
      }

      // 所有重试都失败了
      throw new Error(
        `消息发送失败，已重试${maxRetries}次。最后错误: ${lastError?.message || lastError}`
      );
    } catch (error) {
      console.error('[阅读助手] 处理失败:', error);
      setError('无法启动阅读模式，请刷新页面后重试');
    } finally {
      setLoading(false);
    }
  };

  // 打开选项页面
  const handleOpenOptions = () => {
    openOptionsPage();
    window.close();
  };

  return (
    <ErrorBoundary>
      <Card className="w-80 min-h-[200px] overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">阅读助手</CardTitle>
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
              v0.2.0
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="p-5 space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <Button className="w-full h-11" onClick={handleToggleReader} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <BookOpen className="mr-2 h-4 w-4" />
                  开启/关闭 阅读模式
                </>
              )}
            </Button>

            <Button
              variant="outline"
              className="w-full"
              onClick={handleOpenOptions}
              disabled={isLoading}
            >
              <Settings className="mr-2 h-4 w-4" />
              设置选项
            </Button>
          </div>

          <div className="border-t pt-4 space-y-2">
            <p className="text-sm text-muted-foreground">
              💡 提示：可在页面中按 <kbd className="px-2 py-1 bg-muted rounded text-xs">Esc</kbd>{' '}
              关闭阅读模式
            </p>
            {currentTab?.url && (
              <p className="text-xs text-muted-foreground break-all">
                当前页面：{new URL(currentTab.url).hostname}
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </ErrorBoundary>
  );
};

export default PopupApp;
