// 基于架构设计文档的核心数据类型定义

// 用户设置接口
export interface UserSettings {
  fontSize: number;
  lineHeight: number;
  contentWidth: number;
  theme: 'light' | 'dark' | 'sepia' | 'nature' | 'warm' | 'eyecare';
  fontFamily: string;
  enableAI: boolean;
  autoExtract: boolean;
  keyboardShortcuts: boolean;
  showProgress: boolean;
  enableSync: boolean;
  language: string;
}

// 内容元数据
export interface ContentMetadata {
  author?: string;
  publishDate?: Date;
  wordCount: number;
  readingTime: number;
  language: string;
  contentType: 'article' | 'blog' | 'news' | 'documentation' | 'other';
  tags: string[];
  images: ImageMetadata[];
}

// 图片元数据
export interface ImageMetadata {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  caption?: string;
}

// 提取的内容
export interface ExtractedContent {
  id: string;
  url: string;
  urlHash: string;
  title: string;
  content: string;
  originalContent?: string;
  aiEnhancedContent?: string;
  metadata: ContentMetadata;
  extractedAt: Date;
  confidence: number;
  extractionMethod: 'basic' | 'enhanced' | 'ai' | 'manual';
}

// AI增强选项
export interface AIEnhanceOptions {
  improveReadability?: boolean;
  fixFormatting?: boolean;
  enhanceStructure?: boolean;
  language?: string;
}

// AI增强结果
export interface AIEnhanceResult {
  enhanced_content: string;
  analysis: {
    readability_score: number;
    structure_quality: number;
    content_type: string;
    improvements_made: string[];
  };
  processing_time: number;
  confidence: number;
}

// 浏览器消息类型
export interface BrowserMessage {
  type: 'TOGGLE_READER' | 'EXTRACT_CONTENT' | 'UPDATE_SETTINGS' | 'GET_SETTINGS' | 'GET_ACTIVE_TAB';
  payload?: any;
}

// 应用状态接口
export interface AppState {
  // 用户设置
  settings: UserSettings;
  updateSettings: (_settings: Partial<UserSettings>) => void;

  // 内容状态
  content: ExtractedContent | null;
  setContent: (_content: ExtractedContent | null) => void;

  // UI状态
  isLoading: boolean;
  setLoading: (_isLoading: boolean) => void;

  // 错误状态
  error: string | null;
  setError: (_error: string | null) => void;

  // 阅读器状态
  isReaderActive: boolean;
  setReaderActive: (_isReaderActive: boolean) => void;
}

// 主题配置
export interface ThemeConfig {
  bg: string;
  secondary: string;
  text: string;
  border: string;
}

// 组件Props类型
export interface ReaderProps {
  content: ExtractedContent;
  settings: UserSettings;
  onClose: () => void;
  onSettingsChange: (_newSettings: Partial<UserSettings>) => void;
}

export interface SettingsPanelProps {
  settings: UserSettings;
  onChange: (_newSettings: Partial<UserSettings>) => void;
  onClose: () => void;
}

// 内容提取器接口
export interface ContentExtractor {
  extract(_doc: Document): Promise<ExtractedContent>;
  canHandle(_doc: Document): boolean;
  priority: number;
}

// 评分元素接口
export interface ScoredElement {
  element: HTMLElement;
  score: number;
}
