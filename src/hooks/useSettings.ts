import { useCallback } from 'react';
import { useAppStore } from '@/stores/appStore';
import type { UserSettings } from '@/types';

export const useSettings = () => {
  const { settings, updateSettings } = useAppStore();

  const updateSetting = useCallback(
    <K extends keyof UserSettings>(key: K, value: UserSettings[K]) => {
      updateSettings({ [key]: value });
    },
    [updateSettings]
  );

  const resetToDefaults = useCallback(() => {
    const defaultSettings: UserSettings = {
      fontSize: 16,
      lineHeight: 1.6,
      contentWidth: 65,
      theme: 'light',
      fontFamily: 'system-ui, sans-serif',
      enableAI: true,
      autoExtract: true,
      keyboardShortcuts: true,
      showProgress: true,
      enableSync: false,
      language: 'zh-CN'
    };
    updateSettings(defaultSettings);
  }, [updateSettings]);

  const exportSettings = useCallback(() => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `reading-assistant-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [settings]);

  const importSettings = useCallback((jsonString: string) => {
    try {
      const importedSettings = JSON.parse(jsonString) as UserSettings;
      updateSettings(importedSettings);
      return { success: true };
    } catch (error) {
      console.error('Failed to import settings:', error);
      return { success: false, error: '设置文件格式不正确' };
    }
  }, [updateSettings]);

  return {
    settings,
    updateSetting,
    resetToDefaults,
    exportSettings,
    importSettings
  };
};
