import { useState, useEffect, useCallback } from 'react';
import { BrowserAPI } from '@/utils/browserApi';
import type { BrowserMessage } from '@/types';

export const useBrowserAPI = () => {
  const [currentTab, setCurrentTab] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const browserAPI = BrowserAPI.getInstance();

  // 获取当前标签页
  const getCurrentTab = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const tab = await browserAPI.getCurrentTab();
      setCurrentTab(tab);
      return tab;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取标签页失败';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [browserAPI]);

  // 发送消息到内容脚本
  const sendMessage = useCallback(async (message: BrowserMessage, retries = 3) => {
    if (!currentTab?.id) {
      throw new Error('没有活动的标签页');
    }

    setIsLoading(true);
    setError(null);

    let lastError: Error | null = null;
    
    for (let i = 0; i < retries; i++) {
      try {
        const response = await browserAPI.sendTabMessage(currentTab.id, message);
        setIsLoading(false);
        return response;
      } catch (err) {
        lastError = err instanceof Error ? err : new Error('发送消息失败');
        if (i < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
    }

    setIsLoading(false);
    setError(lastError?.message || '消息发送失败');
    throw lastError;
  }, [currentTab, browserAPI]);

  // 检查是否为特殊页面
  const isSpecialPage = useCallback((url?: string) => {
    const targetUrl = url || currentTab?.url;
    return targetUrl ? browserAPI.isSpecialPage(targetUrl) : false;
  }, [currentTab, browserAPI]);

  // 打开选项页面
  const openOptionsPage = useCallback(() => {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.openOptionsPage();
    }
  }, []);

  // 初始化时获取当前标签页
  useEffect(() => {
    getCurrentTab().catch(console.error);
  }, [getCurrentTab]);

  return {
    currentTab,
    isLoading,
    error,
    setError,
    getCurrentTab,
    sendMessage,
    isSpecialPage,
    openOptionsPage
  };
};
