// 阅读器渲染器 - 基于架构文档设计
import { useAppStore } from '@/stores/appStore';
import { THEME_CONFIGS } from '@/utils/constants';
import type { ExtractedContent, UserSettings } from '@/types';

/**
 * 阅读器渲染器类
 * 负责创建和管理阅读器UI
 */
export class ReaderRenderer {
  private container: HTMLElement | null = null;
  private overlay: HTMLElement | null = null;
  private isRendered = false;

  /**
   * 渲染阅读器
   */
  async render(content: ExtractedContent): Promise<void> {
    try {
      console.log('[阅读器渲染器] 开始渲染阅读器');
      
      // 如果已经渲染，先销毁
      if (this.isRendered) {
        this.destroy();
      }

      // 创建覆盖层
      this.createOverlay();
      
      // 创建阅读器容器
      this.createContainer();
      
      // 渲染内容
      this.renderContent(content);
      
      // 应用样式
      this.applyStyles();
      
      // 添加到页面
      this.appendToPage();
      
      // 标记为已渲染
      this.isRendered = true;
      
      console.log('[阅读器渲染器] 阅读器渲染完成');
      
    } catch (error) {
      console.error('[阅读器渲染器] 渲染失败:', error);
      throw error;
    }
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<UserSettings>): void {
    if (!this.isRendered || !this.container) {
      return;
    }

    console.log('[阅读器渲染器] 更新设置:', newSettings);
    
    // 重新应用样式
    this.applyStyles();
  }

  /**
   * 销毁阅读器
   */
  destroy(): void {
    try {
      console.log('[阅读器渲染器] 销毁阅读器');
      
      // 移除容器
      if (this.container && this.container.parentNode) {
        this.container.parentNode.removeChild(this.container);
      }
      
      // 移除覆盖层
      if (this.overlay && this.overlay.parentNode) {
        this.overlay.parentNode.removeChild(this.overlay);
      }
      
      // 重置状态
      this.container = null;
      this.overlay = null;
      this.isRendered = false;
      
      // 恢复页面滚动
      document.body.style.overflow = '';
      
    } catch (error) {
      console.error('[阅读器渲染器] 销毁失败:', error);
    }
  }

  /**
   * 创建覆盖层
   */
  private createOverlay(): void {
    this.overlay = document.createElement('div');
    this.overlay.id = 'reading-assistant-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999998;
      backdrop-filter: blur(2px);
    `;
    
    // 点击覆盖层关闭阅读器
    this.overlay.addEventListener('click', (e) => {
      if (e.target === this.overlay) {
        this.destroy();
        useAppStore.getState().setReaderActive(false);
      }
    });
  }

  /**
   * 创建阅读器容器
   */
  private createContainer(): void {
    this.container = document.createElement('div');
    this.container.id = 'reading-assistant-container';
    this.container.innerHTML = this.getContainerHTML();
  }

  /**
   * 获取容器HTML模板 - 全屏阅读器布局
   */
  private getContainerHTML(): string {
    return `
      <div class="reader-header">
        <div class="header-content">
          <div class="reader-title"></div>
          <div class="reader-meta"></div>
        </div>
        <div class="reader-controls">
          <button class="control-btn" id="reader-settings" title="设置 (S)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </svg>
          </button>
          <button class="control-btn" id="reader-fullscreen" title="全屏切换 (F)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
            </svg>
          </button>
          <button class="control-btn" id="reader-close" title="关闭阅读器 (Esc)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="reader-content">
        <div class="content-inner"></div>
      </div>
    `;
  }

  /**
   * 渲染内容
   */
  private renderContent(content: ExtractedContent): void {
    if (!this.container) {return;}

    // 设置标题
    const titleElement = this.container.querySelector('.reader-title');
    if (titleElement) {
      titleElement.textContent = content.title;
    }

    // 设置元数据
    const metaElement = this.container.querySelector('.reader-meta');
    if (metaElement) {
      metaElement.innerHTML = `
        <span>字数: ${content.metadata.wordCount.toLocaleString()}</span>
        <span>阅读时间: ${content.metadata.readingTime}分钟</span>
        <span>置信度: ${Math.round(content.confidence * 100)}%</span>
        <span>语言: ${content.metadata.language}</span>
      `;
    }

    // 设置内容
    const contentInner = this.container.querySelector('.content-inner');
    if (contentInner) {
      // 处理内容：支持HTML和纯文本
      let processedContent = '';

      if (content.originalContent && content.originalContent.trim()) {
        // 如果有原始HTML内容，使用它
        processedContent = this.processHtmlContent(content.originalContent);
      } else {
        // 否则将纯文本转换为段落
        const paragraphs = content.content.split('\n').filter(p => p.trim());
        processedContent = paragraphs.map(p => `<p>${this.escapeHtml(p)}</p>`).join('');
      }

      contentInner.innerHTML = processedContent;
    }

    // 绑定事件
    this.bindEvents();
  }

  /**
   * 处理HTML内容，清理和优化 - 增强的多媒体支持
   */
  private processHtmlContent(html: string): string {
    // 创建临时容器来处理HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 1. 预处理多媒体内容
    this.preprocessMediaContent(tempDiv);

    // 2. 移除干扰元素（保留重要的多媒体内容）
    this.removeUnwantedElements(tempDiv);

    // 3. 优化内容结构
    this.optimizeContentStructure(tempDiv);

    // 4. 清理空元素（保留有意义的空元素）
    this.cleanEmptyElements(tempDiv);

    return tempDiv.innerHTML;
  }

  /**
   * 预处理多媒体内容
   */
  private preprocessMediaContent(container: HTMLElement): void {
    // 处理懒加载图片
    const lazyImages = container.querySelectorAll('img[data-src], img[data-lazy-src], img[data-original]');
    lazyImages.forEach(img => {
      const dataSrc = img.getAttribute('data-src') ||
                     img.getAttribute('data-lazy-src') ||
                     img.getAttribute('data-original');
      if (dataSrc && !img.getAttribute('src')) {
        img.setAttribute('src', dataSrc);
      }
    });

    // 处理响应式图片
    const responsiveImages = container.querySelectorAll('img[data-srcset]');
    responsiveImages.forEach(img => {
      const dataSrcset = img.getAttribute('data-srcset');
      if (dataSrcset && !img.getAttribute('srcset')) {
        img.setAttribute('srcset', dataSrcset);
      }
    });

    // 处理视频占位符
    const videoPlaceholders = container.querySelectorAll('[data-video-id], [data-youtube-id]');
    videoPlaceholders.forEach(placeholder => {
      const videoId = placeholder.getAttribute('data-video-id') ||
                     placeholder.getAttribute('data-youtube-id');
      if (videoId) {
        const videoNote = document.createElement('div');
        videoNote.className = 'video-placeholder';
        videoNote.innerHTML = `
          <div style="
            background: #f0f0f0;
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin: 16px 0;
          ">
            📹 视频内容: ${videoId}
          </div>
        `;
        placeholder.parentNode?.replaceChild(videoNote, placeholder);
      }
    });
  }

  /**
   * 移除不需要的元素
   */
  private removeUnwantedElements(container: HTMLElement): void {
    // 严格的移除选择器（不包括可能包含重要内容的元素）
    const removeSelectors = [
      'script', 'style', 'noscript', 'link[rel="stylesheet"]',
      '.ad', '.advertisement', '.promo', '.sponsor',
      '.social-share', '.sharing', '.social-buttons',
      '.comments', '.comment-section', '.disqus',
      '.related', '.recommended', '.sidebar', '.widget',
      '.nav', '.navigation', '.menu', '.breadcrumb',
      '.header', '.footer', '.banner',
      'form', '.search', '.newsletter', '.subscribe'
    ];

    removeSelectors.forEach(selector => {
      const elements = container.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    // 移除隐藏元素
    const hiddenElements = container.querySelectorAll('[style*="display: none"], [style*="visibility: hidden"], .hidden');
    hiddenElements.forEach(el => el.remove());

    // 移除空链接和无意义链接
    const links = container.querySelectorAll('a');
    links.forEach(link => {
      const href = link.getAttribute('href') || '';
      const text = link.textContent?.trim() || '';

      // 移除无意义的链接
      if (!href || href === '#' || text.length === 0 ||
          text.toLowerCase().includes('read more') ||
          text.toLowerCase().includes('continue reading')) {
        if (link.querySelector('img')) {
          // 如果链接包含图片，保留图片但移除链接
          const img = link.querySelector('img');
          if (img) {
            link.parentNode?.replaceChild(img, link);
          }
        } else {
          link.remove();
        }
      }
    });
  }

  /**
   * 优化内容结构
   */
  private optimizeContentStructure(container: HTMLElement): void {
    // 合并相邻的相同标签
    const mergeTags = ['p', 'div', 'span'];
    mergeTags.forEach(tag => {
      const elements = container.querySelectorAll(tag);
      for (let i = 0; i < elements.length - 1; i++) {
        const current = elements[i];
        const next = elements[i + 1];

        if (next && current.nextElementSibling === next &&
            current.className === next.className &&
            !current.querySelector('img, video, audio') &&
            !next.querySelector('img, video, audio')) {

          current.innerHTML += ' ' + next.innerHTML;
          next.remove();
        }
      }
    });

    // 优化图片容器
    const images = container.querySelectorAll('img');
    images.forEach(img => {
      // 确保图片有合适的父容器
      const parent = img.parentElement;
      if (parent && parent.tagName.toLowerCase() === 'a') {
        // 如果图片被链接包裹，移除链接但保留图片
        parent.parentNode?.replaceChild(img, parent);
      }

      // 为独立的图片添加段落包装
      if (img.parentElement?.tagName.toLowerCase() === 'div' &&
          img.parentElement.children.length === 1) {
        const p = document.createElement('p');
        p.style.textAlign = 'center';
        img.parentElement.parentNode?.replaceChild(p, img.parentElement);
        p.appendChild(img);
      }
    });
  }

  /**
   * 清理空元素
   */
  private cleanEmptyElements(container: HTMLElement): void {
    // 多次清理，因为移除元素可能会产生新的空元素
    let hasChanges = true;
    let iterations = 0;
    const maxIterations = 5;

    while (hasChanges && iterations < maxIterations) {
      hasChanges = false;
      iterations++;

      const allElements = container.querySelectorAll('*');
      allElements.forEach(el => {
        const hasText = el.textContent?.trim().length > 0;
        const hasMedia = el.querySelector('img, video, audio, iframe, canvas, svg');
        const hasImportantStructure = el.tagName.toLowerCase() === 'br' ||
                                     el.tagName.toLowerCase() === 'hr';

        // 移除完全空的元素
        if (!hasText && !hasMedia && !hasImportantStructure && el.children.length === 0) {
          el.remove();
          hasChanges = true;
        }
        // 移除只包含空白字符的元素
        else if (el.textContent?.trim() === '' && !hasMedia && !hasImportantStructure) {
          el.remove();
          hasChanges = true;
        }
      });
    }
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    if (!this.container) {return;}

    // 关闭按钮
    const closeBtn = this.container.querySelector('#reader-close');
    closeBtn?.addEventListener('click', () => {
      this.destroy();
      useAppStore.getState().setReaderActive(false);
    });

    // 设置按钮
    const settingsBtn = this.container.querySelector('#reader-settings');
    settingsBtn?.addEventListener('click', () => {
      // 打开设置面板（后续实现）
      console.log('打开设置面板');
    });
  }

  /**
   * 应用样式 - 全屏阅读模式
   */
  private applyStyles(): void {
    if (!this.container) {return;}

    const settings = useAppStore.getState().settings;
    const theme = THEME_CONFIGS[settings.theme];

    // 全屏容器样式
    this.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: ${theme.bg};
      color: ${theme.text};
      z-index: 999999;
      font-family: ${settings.fontFamily};
      font-size: ${settings.fontSize}px;
      line-height: ${settings.lineHeight};
      overflow: hidden;
      display: flex;
      flex-direction: column;
    `;

    // 应用头部样式
    const header = this.container.querySelector('.reader-header') as HTMLElement;
    if (header) {
      header.style.cssText = `
        padding: 16px 20px;
        border-bottom: 1px solid ${theme.border};
        background: ${theme.secondary};
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
      `;
    }

    // 应用头部内容样式
    const headerContent = this.container.querySelector('.header-content') as HTMLElement;
    if (headerContent) {
      headerContent.style.cssText = `
        flex: 1;
        min-width: 0;
      `;
    }

    // 应用控制按钮容器样式
    const controlsContainer = this.container.querySelector('.reader-controls') as HTMLElement;
    if (controlsContainer) {
      controlsContainer.style.cssText = `
        display: flex;
        gap: 8px;
        flex-shrink: 0;
      `;
    }

    // 应用控制按钮样式
    const controlBtns = this.container.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
      (btn as HTMLElement).style.cssText = `
        background: none;
        border: 1px solid ${theme.border};
        color: ${theme.text};
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        margin-left: 8px;
        font-size: 14px;
        transition: all 0.2s;
      `;

      // 悬停效果
      btn.addEventListener('mouseenter', () => {
        (btn as HTMLElement).style.background = theme.border;
      });
      btn.addEventListener('mouseleave', () => {
        (btn as HTMLElement).style.background = 'none';
      });
    });

    // 应用标题样式
    const title = this.container.querySelector('.reader-title') as HTMLElement;
    if (title) {
      title.style.cssText = `
        font-size: ${Math.max(settings.fontSize + 8, 24)}px;
        font-weight: 700;
        margin: 0;
        color: ${theme.text};
        line-height: 1.3;
        max-width: 80%;
      `;
    }

    // 应用元数据样式
    const meta = this.container.querySelector('.reader-meta') as HTMLElement;
    if (meta) {
      meta.style.cssText = `
        font-size: ${Math.max(settings.fontSize - 2, 12)}px;
        color: ${theme.text};
        opacity: 0.7;
        display: flex;
        gap: 16px;
        margin-top: 8px;
      `;
    }

    // 应用内容区域样式
    const contentArea = this.container.querySelector('.reader-content') as HTMLElement;
    if (contentArea) {
      contentArea.style.cssText = `
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        justify-content: center;
        background: ${theme.bg};
      `;

      // 内容容器样式（限制宽度以提高可读性）
      const contentInner = contentArea.querySelector('.content-inner') as HTMLElement;
      if (contentInner) {
        const maxWidth = Math.min(settings.contentWidth * 10, 800); // 转换百分比为像素
        contentInner.style.cssText = `
          max-width: ${maxWidth}px;
          width: 100%;
          padding: 32px 24px;
          line-height: ${settings.lineHeight};
        `;

        // 应用内容元素样式
        this.applyContentStyles(contentInner, settings, theme);
      }
    }
  }

  /**
   * 应用内容样式
   */
  private applyContentStyles(container: HTMLElement, settings: any, theme: any): void {
    // 段落样式
    const paragraphs = container.querySelectorAll('p');
    paragraphs.forEach(p => {
      (p as HTMLElement).style.cssText = `
        margin: 0 0 ${settings.fontSize * 1.2}px 0;
        text-align: justify;
        word-break: break-word;
        hyphens: auto;
        color: ${theme.text};
      `;
    });

    // 标题样式
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      const fontSize = Math.max(settings.fontSize + (7 - level) * 2, settings.fontSize);
      (heading as HTMLElement).style.cssText = `
        font-size: ${fontSize}px;
        font-weight: 600;
        margin: ${fontSize * 1.5}px 0 ${fontSize * 0.8}px 0;
        color: ${theme.text};
        line-height: 1.3;
      `;

      // 第一个标题减少上边距
      if (index === 0) {
        (heading as HTMLElement).style.marginTop = `${fontSize * 0.5}px`;
      }
    });

    // 列表样式
    const lists = container.querySelectorAll('ul, ol');
    lists.forEach(list => {
      (list as HTMLElement).style.cssText = `
        margin: ${settings.fontSize}px 0;
        padding-left: ${settings.fontSize * 2}px;
        color: ${theme.text};
      `;

      const listItems = list.querySelectorAll('li');
      listItems.forEach(li => {
        (li as HTMLElement).style.cssText = `
          margin: ${settings.fontSize * 0.5}px 0;
          line-height: ${settings.lineHeight};
        `;
      });
    });

    // 图片样式 - 增强的多媒体处理
    const images = container.querySelectorAll('img');
    images.forEach((img) => {
      this.processImageElement(img as HTMLImageElement, settings, theme);
    });

    // 引用样式
    const blockquotes = container.querySelectorAll('blockquote');
    blockquotes.forEach(quote => {
      (quote as HTMLElement).style.cssText = `
        margin: ${settings.fontSize * 1.5}px 0;
        padding: ${settings.fontSize}px ${settings.fontSize * 1.5}px;
        border-left: 4px solid ${theme.border};
        background: ${theme.secondary};
        font-style: italic;
        color: ${theme.text};
        border-radius: 0 8px 8px 0;
      `;
    });

    // 代码样式
    const codeBlocks = container.querySelectorAll('pre, code');
    codeBlocks.forEach(code => {
      (code as HTMLElement).style.cssText = `
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: ${Math.max(settings.fontSize - 2, 12)}px;
        background: ${theme.secondary};
        color: ${theme.text};
        padding: ${code.tagName === 'PRE' ? settings.fontSize : '2px 6px'};
        border-radius: 4px;
        overflow-x: auto;
        ${code.tagName === 'PRE' ? `margin: ${settings.fontSize}px 0;` : 'display: inline;'}
      `;
    });

    // 链接样式
    const links = container.querySelectorAll('a');
    links.forEach(link => {
      (link as HTMLElement).style.cssText = `
        color: #007acc;
        text-decoration: underline;
        text-decoration-color: rgba(0, 122, 204, 0.3);
        transition: all 0.2s;
      `;

      link.addEventListener('mouseenter', () => {
        (link as HTMLElement).style.textDecorationColor = '#007acc';
      });
      link.addEventListener('mouseleave', () => {
        (link as HTMLElement).style.textDecorationColor = 'rgba(0, 122, 204, 0.3)';
      });
    });

    // 表格样式
    const tables = container.querySelectorAll('table');
    tables.forEach(table => {
      (table as HTMLElement).style.cssText = `
        width: 100%;
        border-collapse: collapse;
        margin: ${settings.fontSize * 1.5}px 0;
        font-size: ${Math.max(settings.fontSize - 1, 13)}px;
      `;

      const cells = table.querySelectorAll('th, td');
      cells.forEach(cell => {
        (cell as HTMLElement).style.cssText = `
          padding: ${settings.fontSize * 0.5}px ${settings.fontSize * 0.8}px;
          border: 1px solid ${theme.border};
          text-align: left;
          color: ${theme.text};
        `;
      });

      const headers = table.querySelectorAll('th');
      headers.forEach(th => {
        (th as HTMLElement).style.background = theme.secondary;
        (th as HTMLElement).style.fontWeight = '600';
      });
    });
  }

  /**
   * 处理图片元素 - 增强的多媒体支持
   */
  private processImageElement(
    img: HTMLImageElement,
    settings: any,
    theme: any
  ): void {
    // 基础图片样式
    img.style.cssText = `
      max-width: 100%;
      height: auto;
      margin: ${settings.fontSize * 1.5}px auto;
      display: block;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;
      cursor: pointer;
    `;

    // 处理懒加载图片
    this.handleLazyLoadedImage(img);

    // 添加图片加载错误处理
    this.addImageErrorHandling(img, settings, theme);

    // 创建图片容器和说明
    this.wrapImageWithCaption(img, settings, theme);

    // 添加图片交互功能
    this.addImageInteractions(img);
  }

  /**
   * 处理懒加载图片
   */
  private handleLazyLoadedImage(img: HTMLImageElement): void {
    // 检查各种懒加载属性
    const dataSrc = img.getAttribute('data-src') ||
                   img.getAttribute('data-lazy-src') ||
                   img.getAttribute('data-original');

    if (dataSrc && !img.src) {
      img.src = dataSrc;
    }

    // 处理srcset
    const dataSrcset = img.getAttribute('data-srcset');
    if (dataSrcset && !img.srcset) {
      img.srcset = dataSrcset;
    }

    // 触发懒加载
    if ('loading' in img) {
      img.loading = 'eager';
    }
  }

  /**
   * 添加图片错误处理
   */
  private addImageErrorHandling(img: HTMLImageElement, settings: any, theme: any): void {
    img.addEventListener('error', () => {
      // 创建占位符
      const placeholder = document.createElement('div');
      placeholder.style.cssText = `
        width: 100%;
        min-height: 200px;
        background: ${theme.secondary};
        border: 2px dashed ${theme.border};
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: ${settings.fontSize * 1.5}px auto;
        color: ${theme.text};
        opacity: 0.7;
        font-size: ${settings.fontSize - 2}px;
      `;

      const icon = document.createElement('div');
      icon.innerHTML = '🖼️';
      icon.style.fontSize = '48px';
      icon.style.marginBottom = '12px';

      const text = document.createElement('div');
      text.textContent = '图片加载失败';
      text.style.marginBottom = '8px';

      const alt = img.alt;
      if (alt) {
        const altText = document.createElement('div');
        altText.textContent = `描述: ${alt}`;
        altText.style.fontSize = `${settings.fontSize - 4}px`;
        altText.style.fontStyle = 'italic';
        altText.style.textAlign = 'center';
        altText.style.maxWidth = '80%';
        placeholder.appendChild(altText);
      }

      placeholder.appendChild(icon);
      placeholder.appendChild(text);

      // 替换原图片
      img.parentNode?.replaceChild(placeholder, img);
    });
  }

  /**
   * 为图片添加说明文字
   */
  private wrapImageWithCaption(img: HTMLImageElement, settings: any, theme: any): void {
    const alt = img.alt;
    const title = img.title;
    const caption = this.findImageCaption(img);

    if (!alt && !title && !caption) {
      return; // 没有说明文字
    }

    // 创建图片容器
    const figure = document.createElement('figure');
    figure.style.cssText = `
      margin: ${settings.fontSize * 2}px 0;
      text-align: center;
    `;

    // 移动图片到容器中
    img.parentNode?.insertBefore(figure, img);
    figure.appendChild(img);

    // 创建说明文字
    if (alt || title || caption) {
      const figcaption = document.createElement('figcaption');
      figcaption.style.cssText = `
        margin-top: ${settings.fontSize * 0.8}px;
        font-size: ${Math.max(settings.fontSize - 2, 12)}px;
        color: ${theme.text};
        opacity: 0.8;
        font-style: italic;
        line-height: 1.4;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
      `;

      const captionText = caption || alt || title;
      figcaption.textContent = captionText;
      figure.appendChild(figcaption);
    }
  }

  /**
   * 查找图片说明
   */
  private findImageCaption(img: HTMLImageElement): string {
    // 检查figure/figcaption结构
    const figure = img.closest('figure');
    if (figure) {
      const figcaption = figure.querySelector('figcaption');
      if (figcaption?.textContent) {
        return figcaption.textContent.trim();
      }
    }

    // 检查常见的说明类名
    const parent = img.parentElement;
    if (parent) {
      const captionSelectors = [
        '.caption', '.image-caption', '.photo-caption',
        '.img-caption', '.description', '.subtitle'
      ];

      for (const selector of captionSelectors) {
        const caption = parent.querySelector(selector);
        if (caption?.textContent) {
          return caption.textContent.trim();
        }
      }
    }

    return '';
  }

  /**
   * 添加图片交互功能
   */
  private addImageInteractions(img: HTMLImageElement): void {
    // 悬停效果
    img.addEventListener('mouseenter', () => {
      img.style.transform = 'scale(1.02)';
    });

    img.addEventListener('mouseleave', () => {
      img.style.transform = 'scale(1)';
    });

    // 点击放大功能
    img.addEventListener('click', () => {
      this.showImageModal(img);
    });
  }

  /**
   * 显示图片模态框
   */
  private showImageModal(img: HTMLImageElement): void {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.9);
      z-index: 1000000;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    `;

    // 创建放大的图片
    const modalImg = document.createElement('img');
    modalImg.src = img.src;
    modalImg.alt = img.alt;
    modalImg.style.cssText = `
      max-width: 90vw;
      max-height: 90vh;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    `;

    modal.appendChild(modalImg);

    // 点击关闭
    modal.addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        document.body.removeChild(modal);
        document.removeEventListener('keydown', handleKeydown);
      }
    };
    document.addEventListener('keydown', handleKeydown);

    document.body.appendChild(modal);
  }

  /**
   * 添加到页面
   */
  private appendToPage(): void {
    if (this.overlay && this.container) {
      document.body.appendChild(this.overlay);
      document.body.appendChild(this.container);
      
      // 禁用页面滚动
      document.body.style.overflow = 'hidden';
    }
  }

  /**
   * 转义HTML
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}
