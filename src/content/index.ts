// Content Script主入口 - 基于架构文档的模块化设计
import { ContentExtractor } from './extractors/ContentExtractor';
import { ReaderRenderer } from './renderers/ReaderRenderer';
import { KeyboardHandler } from './handlers/KeyboardHandler';
import { MessageHandler } from './handlers/MessageHandler';
import { useAppStore, loadSettingsFromBrowser } from '@/stores/appStore';
import type { ExtractedContent } from '@/types';

/**
 * 阅读助手Content Script主类
 * 负责协调各个模块的工作
 */
class ReadingAssistant {
  private extractor: ContentExtractor;
  private renderer: ReaderRenderer;
  private keyboardHandler: KeyboardHandler;
  private messageHandler: MessageHandler;
  private isActive = false;

  constructor() {
    this.extractor = new ContentExtractor();
    this.renderer = new ReaderRenderer();
    this.keyboardHandler = new KeyboardHandler();
    this.messageHandler = new MessageHandler();

    this.init();
  }

  /**
   * 初始化阅读助手
   */
  private async init(): Promise<void> {
    try {
      console.log('[阅读助手] Content Script 初始化开始');
      
      // 加载用户设置
      await loadSettingsFromBrowser();
      
      // 设置消息处理器
      this.setupMessageHandlers();
      
      // 设置键盘处理器
      this.setupKeyboardHandlers();
      
      // 检查是否需要自动提取
      const settings = useAppStore.getState().settings;
      if (settings.autoExtract) {
        // 延迟自动提取，确保页面完全加载
        setTimeout(() => this.checkAutoExtract(), 2000);
      }
      
      console.log('[阅读助手] Content Script 初始化完成');
      
    } catch (error) {
      console.error('[阅读助手] 初始化失败:', error);
    }
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandlers(): void {
    this.messageHandler.onMessage('TOGGLE_READER', async () => {
      try {
        await this.toggleReader();
        return { success: true, message: '阅读模式切换成功' };
      } catch (error) {
        console.error('[阅读助手] 切换失败:', error);
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    this.messageHandler.onMessage('EXTRACT_CONTENT', async () => {
      try {
        const content = await this.extractContent();
        return { success: true, content };
      } catch (error) {
        console.error('[阅读助手] 内容提取失败:', error);
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    this.messageHandler.onMessage('UPDATE_SETTINGS', (payload) => {
      this.handleSettingsUpdate(payload);
    });

    this.messageHandler.onMessage('GET_SETTINGS', () => {
      return useAppStore.getState().settings;
    });
  }

  /**
   * 设置键盘处理器
   */
  private setupKeyboardHandlers(): void {
    this.keyboardHandler.onKeyPress('Escape', () => {
      if (this.isActive) {
        this.closeReader();
      }
    });

    this.keyboardHandler.onKeyPress('Equal', () => {
      if (this.isActive) {
        this.adjustFontSize(1);
      }
    });

    this.keyboardHandler.onKeyPress('Minus', () => {
      if (this.isActive) {
        this.adjustFontSize(-1);
      }
    });
  }

  /**
   * 切换阅读模式（公共方法）
   */
  public async toggleReader(): Promise<void> {
    console.log('[阅读助手] 开始切换阅读模式，当前状态:', this.isActive);

    try {
      if (this.isActive) {
        console.log('[阅读助手] 关闭阅读模式');
        this.closeReader();
      } else {
        console.log('[阅读助手] 开启阅读模式');
        await this.openReader();
      }

      console.log('[阅读助手] 阅读模式切换完成，新状态:', this.isActive);
    } catch (error) {
      console.error('[阅读助手] 切换阅读模式失败:', error);
      this.showError('切换阅读模式失败，请重试');
      throw error; // 重新抛出错误，让调用者知道失败了
    }
  }

  /**
   * 开启阅读模式
   */
  private async openReader(): Promise<void> {
    console.log('[阅读助手] 开启阅读模式');
    
    // 显示加载状态
    this.showLoading();
    
    try {
      // 提取内容
      const content = await this.extractContent();
      
      if (!content) {
        throw new Error('无法提取页面内容');
      }
      
      // 渲染阅读器
      await this.renderer.render(content);
      
      // 更新状态
      this.isActive = true;
      useAppStore.getState().setReaderActive(true);
      
      // 启用键盘快捷键
      const settings = useAppStore.getState().settings;
      if (settings.keyboardShortcuts) {
        this.keyboardHandler.enable();
      }
      
      console.log('[阅读助手] 阅读模式已开启');
      
    } catch (error) {
      console.error('[阅读助手] 开启阅读模式失败:', error);
      this.showError('开启阅读模式失败：' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.hideLoading();
    }
  }

  /**
   * 关闭阅读模式
   */
  private closeReader(): void {
    console.log('[阅读助手] 关闭阅读模式');
    
    try {
      // 移除渲染器
      this.renderer.destroy();
      
      // 更新状态
      this.isActive = false;
      useAppStore.getState().setReaderActive(false);
      
      // 禁用键盘快捷键
      this.keyboardHandler.disable();
      
      console.log('[阅读助手] 阅读模式已关闭');
      
    } catch (error) {
      console.error('[阅读助手] 关闭阅读模式失败:', error);
    }
  }

  /**
   * 提取页面内容
   */
  private async extractContent(): Promise<ExtractedContent | null> {
    try {
      console.log('[阅读助手] 开始提取内容');
      
      const content = await this.extractor.extract(document);
      
      if (content) {
        useAppStore.getState().setContent(content);
        console.log('[阅读助手] 内容提取成功:', content.title);
      } else {
        console.warn('[阅读助手] 内容提取失败');
      }
      
      return content;
      
    } catch (error) {
      console.error('[阅读助手] 内容提取错误:', error);
      return null;
    }
  }

  /**
   * 处理设置更新
   */
  private handleSettingsUpdate(newSettings: any): void {
    try {
      useAppStore.getState().updateSettings(newSettings);
      
      // 如果阅读器处于活动状态，更新渲染
      if (this.isActive) {
        this.renderer.updateSettings(newSettings);
      }
      
      console.log('[阅读助手] 设置已更新:', newSettings);
      
    } catch (error) {
      console.error('[阅读助手] 设置更新失败:', error);
    }
  }

  /**
   * 调整字体大小
   */
  private adjustFontSize(delta: number): void {
    const currentSettings = useAppStore.getState().settings;
    const newFontSize = Math.max(12, Math.min(24, currentSettings.fontSize + delta));
    
    this.handleSettingsUpdate({ fontSize: newFontSize });
  }

  /**
   * 检查是否需要自动提取
   */
  private checkAutoExtract(): void {
    const settings = useAppStore.getState().settings;
    
    if (settings.autoExtract && this.extractor.canHandle(document)) {
      console.log('[阅读助手] 执行自动内容提取');
      this.extractContent();
    }
  }

  /**
   * 显示加载状态
   */
  private showLoading(): void {
    useAppStore.getState().setLoading(true);
    // 这里可以添加加载动画的显示逻辑
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading(): void {
    useAppStore.getState().setLoading(false);
  }

  /**
   * 显示错误信息
   */
  private showError(message: string): void {
    useAppStore.getState().setError(message);
    // 这里可以添加错误提示的显示逻辑
    console.error('[阅读助手] 错误:', message);
  }
}

// 防止重复初始化
if (!(window as any).__ReadingAssistantInitialized__) {
  console.log('[阅读助手] 开始初始化Content Script');

  // 标记已初始化
  (window as any).__ReadingAssistantInitialized__ = true;

  // 初始化阅读助手
  let assistant: ReadingAssistant;

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      console.log('[阅读助手] DOM加载完成，初始化阅读助手');
      assistant = new ReadingAssistant();
      (window as any).__ReadingAssistant__ = assistant;
    });
  } else {
    console.log('[阅读助手] DOM已就绪，立即初始化阅读助手');
    assistant = new ReadingAssistant();
    (window as any).__ReadingAssistant__ = assistant;
  }

  // 添加全局消息监听器作为备用
  chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    console.log('[阅读助手] 全局消息监听器收到消息:', message);

    if (message.type === 'TOGGLE_READER') {
      console.log('[阅读助手] 处理TOGGLE_READER消息');

      if (assistant) {
        // 异步处理toggleReader
        assistant.toggleReader()
          .then(() => {
            console.log('[阅读助手] 阅读模式切换成功');
            sendResponse({ success: true, message: '阅读模式切换成功' });
          })
          .catch((error) => {
            console.error('[阅读助手] 切换失败:', error);
            sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
          });
      } else {
        console.warn('[阅读助手] 助手实例未初始化');
        sendResponse({ success: false, error: '助手未初始化' });
      }

      return true; // 异步响应
    }

    return false;
  });

} else {
  console.log('[阅读助手] Content Script已经初始化，跳过重复初始化');
}
