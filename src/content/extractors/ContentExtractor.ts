// 内容提取器 - 基于架构文档的启发式算法
import { CONTENT_SELECTORS, REMOVE_SELECTORS, PERFORMANCE_CONFIG } from '@/utils/constants';
import type { ExtractedContent, ContentMetadata } from '@/types';

/**
 * 内容提取器类
 * 使用启发式算法提取页面主要内容
 */
export class ContentExtractor {
  private readonly MIN_CONTENT_LENGTH = 100;

  /**
   * 提取页面内容
   */
  async extract(document: Document): Promise<ExtractedContent | null> {
    try {
      console.log('[内容提取器] 开始提取内容');
      
      // 设置超时保护
      const timeoutPromise = new Promise<null>((_, reject) => {
        setTimeout(() => reject(new Error('内容提取超时')), PERFORMANCE_CONFIG.EXTRACTION_TIMEOUT);
      });

      const extractionPromise = this.performExtraction(document);
      
      const result = await Promise.race([extractionPromise, timeoutPromise]);
      
      if (result) {
        console.log('[内容提取器] 内容提取成功:', result.title);
      }
      
      return result;
      
    } catch (error) {
      console.error('[内容提取器] 提取失败:', error);
      return null;
    }
  }

  /**
   * 检查是否可以处理当前文档
   */
  canHandle(document: Document): boolean {
    // 检查是否是HTML文档
    if (!document || document.contentType !== 'text/html') {
      return false;
    }

    // 检查是否有足够的文本内容
    const bodyText = document.body?.textContent || '';
    if (bodyText.length < this.MIN_CONTENT_LENGTH) {
      return false;
    }

    // 检查是否是特殊页面
    const url = document.location.href;
    const specialPatterns = [
      /^chrome:/,
      /^about:/,
      /^moz-extension:/,
      /^chrome-extension:/,
      /^data:/,
      /^file:/
    ];

    return !specialPatterns.some(pattern => pattern.test(url));
  }

  /**
   * 执行内容提取 - 增强版，支持动态内容
   */
  private async performExtraction(document: Document): Promise<ExtractedContent | null> {
    // 1. 等待动态内容加载
    await this.waitForDynamicContent(document);

    // 2. 清理文档
    const cleanedDoc = this.cleanDocument(document);

    // 3. 查找主要内容容器
    const contentContainer = this.findMainContent(cleanedDoc);

    if (!contentContainer) {
      console.warn('[内容提取器] 未找到主要内容容器');
      return null;
    }

    // 4. 提取标题
    const title = this.extractTitle(document);

    // 5. 提取内容
    const content = this.extractContentFromContainer(contentContainer);

    if (!content || content.length < this.MIN_CONTENT_LENGTH) {
      console.warn('[内容提取器] 提取的内容太短');
      return null;
    }

    // 6. 提取元数据
    const metadata = this.extractMetadata(document, content);

    // 7. 生成结果
    const result: ExtractedContent = {
      id: this.generateId(),
      url: document.location.href,
      urlHash: this.hashUrl(document.location.href),
      title,
      content,
      originalContent: contentContainer.innerHTML,
      metadata,
      extractedAt: new Date(),
      confidence: this.calculateConfidence(contentContainer, content),
      extractionMethod: 'enhanced'
    };

    console.log('[内容提取器] 内容提取完成，置信度:', result.confidence);
    return result;
  }

  /**
   * 等待动态内容加载
   */
  private async waitForDynamicContent(document: Document): Promise<void> {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 10;
      const checkInterval = 200;

      const checkContent = () => {
        attempts++;

        // 检查是否有懒加载指示器
        const loadingIndicators = document.querySelectorAll(
          '.loading, .spinner, .skeleton, [data-loading], .lazy-loading'
        );

        // 检查是否有未加载的图片
        const lazyImages = document.querySelectorAll(
          'img[data-src], img[data-lazy-src], img[loading="lazy"]'
        );

        // 如果没有加载指示器且没有懒加载图片，或者达到最大尝试次数
        if ((loadingIndicators.length === 0 && lazyImages.length === 0) ||
            attempts >= maxAttempts) {
          resolve();
        } else {
          setTimeout(checkContent, checkInterval);
        }
      };

      // 立即开始检查，但也设置一个最小等待时间
      setTimeout(checkContent, 100);
    });
  }

  /**
   * 清理文档，移除不需要的元素
   */
  private cleanDocument(document: Document): Document {
    const clonedDoc = document.cloneNode(true) as Document;
    
    // 移除不需要的元素
    REMOVE_SELECTORS.forEach(selector => {
      const elements = clonedDoc.querySelectorAll(selector);
      elements.forEach(element => element.remove());
    });

    return clonedDoc;
  }

  /**
   * 查找主要内容容器
   */
  private findMainContent(document: Document): HTMLElement | null {
    // 1. 尝试使用预定义选择器
    for (const selector of CONTENT_SELECTORS) {
      const element = document.querySelector(selector) as HTMLElement;
      if (element && this.isValidContentContainer(element)) {
        console.log('[内容提取器] 使用选择器找到内容:', selector);
        return element;
      }
    }

    // 2. 使用启发式算法评分
    const candidates = this.findContentCandidates(document);
    const scored = candidates.map(element => ({
      element,
      score: this.scoreElement(element)
    }));

    // 排序并选择最高分的元素
    scored.sort((a, b) => b.score - a.score);
    
    if (scored.length > 0 && scored[0].score > 0) {
      console.log('[内容提取器] 使用启发式算法找到内容，得分:', scored[0].score);
      return scored[0].element;
    }

    // 3. 后备方案：使用body
    console.warn('[内容提取器] 使用后备方案：body元素');
    return document.body;
  }

  /**
   * 查找内容候选元素
   */
  private findContentCandidates(document: Document): HTMLElement[] {
    const candidates: HTMLElement[] = [];
    
    // 查找所有可能的容器元素
    const selectors = ['div', 'article', 'section', 'main', 'p'];
    
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const htmlElement = element as HTMLElement;
        if (this.isValidContentContainer(htmlElement)) {
          candidates.push(htmlElement);
        }
      });
    });

    return candidates;
  }

  /**
   * 检查是否是有效的内容容器
   */
  private isValidContentContainer(element: HTMLElement): boolean {
    const text = element.textContent || '';
    
    // 检查文本长度
    if (text.length < this.MIN_CONTENT_LENGTH) {
      return false;
    }

    // 检查段落数量
    const paragraphs = element.querySelectorAll('p');
    if (paragraphs.length < 2) {
      return false;
    }

    return true;
  }

  /**
   * 为元素评分 - 高精度启发式算法
   */
  private scoreElement(element: HTMLElement): number {
    let score = 0;
    const text = element.textContent || '';
    const textLength = text.length;

    // 如果文本太短，直接返回0
    if (textLength < this.MIN_CONTENT_LENGTH) {
      return 0;
    }

    // 1. 基础分数：文本长度（改进的对数增长）
    const baseScore = Math.log(textLength / 100 + 1) * 12;
    score += baseScore;

    // 2. 段落质量评分（更精确）
    const paragraphs = element.querySelectorAll('p');
    const validParagraphs = Array.from(paragraphs).filter(p => {
      const pText = p.textContent?.trim() || '';
      return pText.length > 30 && pText.split(' ').length > 5; // 更严格的段落质量要求
    });
    score += validParagraphs.length * 4;

    // 3. 文本结构和可读性评分
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 15);
    const avgSentenceLength = sentences.length > 0 ? textLength / sentences.length : 0;

    // 理想句子长度在50-150字符之间
    if (avgSentenceLength >= 50 && avgSentenceLength <= 150) {
      score += 10;
    } else if (avgSentenceLength >= 30 && avgSentenceLength <= 200) {
      score += 5;
    }

    score += Math.min(sentences.length * 0.8, 25);

    // 4. 改进的链接密度分析
    const links = element.querySelectorAll('a');
    const linkText = Array.from(links).reduce((sum, link) => sum + (link.textContent?.length || 0), 0);
    const linkDensity = textLength > 0 ? linkText / textLength : 0;

    // 更细致的链接密度评分
    if (linkDensity > 0.4) {
      score -= linkDensity * 40; // 极高链接密度严重扣分
    } else if (linkDensity > 0.25) {
      score -= linkDensity * 25; // 高链接密度大幅扣分
    } else if (linkDensity > 0.15) {
      score -= linkDensity * 15; // 中等链接密度适度扣分
    } else if (linkDensity > 0.05) {
      score += 2; // 适量链接反而加分（说明是正文）
    }

    // 5. 语义化标签权重（扩展）
    const tagName = element.tagName.toLowerCase();
    const tagScores: Record<string, number> = {
      'article': 30,
      'main': 25,
      'section': 15,
      'div': 0,
      'span': -3,
      'aside': -10,
      'nav': -15,
      'header': -10,
      'footer': -10
    };
    score += tagScores[tagName] || 0;

    // 6. 增强的语义分析
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    const combinedText = `${className} ${id}`;

    // 正面语义词汇
    const positiveTerms = [
      { terms: ['content', 'article', 'post', 'story', 'text', 'body'], weight: 18 },
      { terms: ['main', 'primary', 'principal'], weight: 15 },
      { terms: ['entry', 'blog', 'news'], weight: 12 },
      { terms: ['description', 'detail', 'info'], weight: 10 },
      { terms: ['full', 'complete', 'entire'], weight: 8 }
    ];

    // 负面语义词汇
    const negativeTerms = [
      { terms: ['sidebar', 'widget', 'ad', 'advertisement'], weight: -25 },
      { terms: ['nav', 'menu', 'navigation'], weight: -20 },
      { terms: ['comment', 'social', 'share', 'follow'], weight: -18 },
      { terms: ['related', 'recommended', 'popular'], weight: -15 },
      { terms: ['footer', 'header', 'banner'], weight: -12 },
      { terms: ['promo', 'sponsor', 'affiliate'], weight: -20 }
    ];

    // 应用语义评分
    for (const { terms, weight } of positiveTerms) {
      for (const term of terms) {
        if (combinedText.includes(term)) {
          score += weight;
          break;
        }
      }
    }

    for (const { terms, weight } of negativeTerms) {
      for (const term of terms) {
        if (combinedText.includes(term)) {
          score += weight;
          break;
        }
      }
    }

    // 7. 内容质量指标（增强）
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const headingScore = Math.min(headings.length * 3, 15);
    score += headingScore;

    // 8. 多媒体内容评分
    const mediaScore = this.scoreMediaContent(element);
    score += mediaScore;

    // 9. 结构完整性评分
    const structureScore = this.scoreContentStructure(element);
    score += structureScore;

    // 10. 负面指标检测（扩展）
    const negativeScore = this.scoreNegativeIndicators(element);
    score += negativeScore;

    // 11. 位置和可见性权重
    const positionScore = this.scoreElementPosition(element);
    score += positionScore;

    // 12. 内容密度评分
    const densityScore = this.scoreContentDensity(element);
    score += densityScore;

    return Math.max(0, Math.round(score));
  }

  /**
   * 评分多媒体内容
   */
  private scoreMediaContent(element: HTMLElement): number {
    let score = 0;

    // 图片评分
    const images = element.querySelectorAll('img');
    const validImages = Array.from(images).filter(img => {
      const src = img.getAttribute('src') || img.getAttribute('data-src') || '';
      const alt = img.getAttribute('alt') || '';
      const width = img.getAttribute('width') || img.naturalWidth || 0;
      const height = img.getAttribute('height') || img.naturalHeight || 0;

      // 过滤广告和装饰性图片
      const isAd = src.includes('ad') || src.includes('banner') ||
                  alt.toLowerCase().includes('ad') || alt.toLowerCase().includes('banner');
      const isIcon = (width && height && Math.max(+width, +height) < 50);
      const isValid = src.length > 0 && !isAd && !isIcon;

      return isValid;
    });

    score += Math.min(validImages.length * 2, 12); // 有效图片加分

    // 视频和音频内容
    const videos = element.querySelectorAll('video, iframe[src*="youtube"], iframe[src*="vimeo"]');
    score += Math.min(videos.length * 3, 9);

    // 代码块
    const codeBlocks = element.querySelectorAll('pre, code, .highlight, .code-block');
    score += Math.min(codeBlocks.length * 2, 8);

    return score;
  }

  /**
   * 评分内容结构
   */
  private scoreContentStructure(element: HTMLElement): number {
    let score = 0;

    // 列表结构
    const lists = element.querySelectorAll('ul, ol');
    const validLists = Array.from(lists).filter(list => {
      const items = list.querySelectorAll('li');
      return items.length >= 2 && Array.from(items).some(li => (li.textContent?.length || 0) > 10);
    });
    score += Math.min(validLists.length * 2, 8);

    // 引用块
    const quotes = element.querySelectorAll('blockquote, .quote');
    score += Math.min(quotes.length * 3, 9);

    // 表格
    const tables = element.querySelectorAll('table');
    const validTables = Array.from(tables).filter(table => {
      const rows = table.querySelectorAll('tr');
      return rows.length >= 2;
    });
    score += Math.min(validTables.length * 4, 12);

    // 分段结构
    const sections = element.querySelectorAll('section, .section');
    score += Math.min(sections.length * 1, 5);

    return score;
  }

  /**
   * 评分负面指标
   */
  private scoreNegativeIndicators(element: HTMLElement): number {
    let score = 0;

    // 检查是否包含大量负面元素
    const negativeSelectors = [
      '.ad', '.advertisement', '.promo', '.sponsor',
      '.comment', '.social', '.share', '.related',
      '.sidebar', '.widget', '.nav', '.menu',
      '.footer', '.header', '.banner'
    ];

    for (const selector of negativeSelectors) {
      const negativeElements = element.querySelectorAll(selector);
      if (negativeElements.length > 0) {
        score -= negativeElements.length * 5;
      }
    }

    // 检查表单元素（通常不是正文内容）
    const forms = element.querySelectorAll('form, input, textarea, select');
    score -= forms.length * 3;

    // 检查过多的链接（可能是导航或广告）
    const links = element.querySelectorAll('a');
    if (links.length > 20) {
      score -= (links.length - 20) * 0.5;
    }

    return Math.max(score, -50); // 限制负分上限
  }

  /**
   * 评分元素位置
   */
  private scoreElementPosition(element: HTMLElement): number {
    let score = 0;

    try {
      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // 位置权重：页面上部和中部内容优先
      if (rect.top < viewportHeight * 0.2) {
        score += 8; // 页面顶部
      } else if (rect.top < viewportHeight * 0.5) {
        score += 5; // 页面上半部
      } else if (rect.top < viewportHeight) {
        score += 2; // 首屏可见
      }

      // 宽度权重：占据主要宽度的内容优先
      const widthRatio = rect.width / viewportWidth;
      if (widthRatio > 0.6) {
        score += 5; // 占据大部分宽度
      } else if (widthRatio > 0.4) {
        score += 3; // 占据适中宽度
      } else if (widthRatio < 0.2) {
        score -= 5; // 过窄可能是侧边栏
      }

      // 可见性检查
      if (rect.height === 0 || rect.width === 0) {
        score -= 20; // 不可见元素大幅扣分
      }

    } catch (error) {
      // 如果获取位置信息失败，不影响评分
      console.warn('[内容提取器] 获取元素位置失败:', error);
    }

    return score;
  }

  /**
   * 评分内容密度
   */
  private scoreContentDensity(element: HTMLElement): number {
    const text = element.textContent || '';
    const textLength = text.length;
    const childElements = element.children.length;

    if (childElements === 0) {
      return textLength > 100 ? 5 : 0; // 纯文本节点
    }

    // 计算文本密度（文本长度 / 子元素数量）
    const density = textLength / childElements;

    if (density > 200) {
      return 10; // 高文本密度
    } else if (density > 100) {
      return 5; // 中等文本密度
    } else if (density < 20) {
      return -5; // 低文本密度（可能是导航或广告）
    }

    return 0;
  }

  /**
   * 提取标题 - 改进的标题识别算法
   */
  private extractTitle(document: Document): string {
    // 按优先级尝试多种方式获取标题
    const titleCandidates: { selector: string; priority: number }[] = [
      // 最高优先级：语义化标题
      { selector: 'article h1', priority: 100 },
      { selector: 'main h1', priority: 95 },
      { selector: '[role="main"] h1', priority: 90 },

      // 高优先级：常见标题类名
      { selector: '.post-title', priority: 85 },
      { selector: '.article-title', priority: 85 },
      { selector: '.entry-title', priority: 85 },
      { selector: '.story-title', priority: 85 },
      { selector: '.content-title', priority: 80 },
      { selector: '.page-title', priority: 75 },

      // 中等优先级：通用选择器
      { selector: 'h1', priority: 70 },
      { selector: '.title', priority: 65 },
      { selector: '#title', priority: 65 },
      { selector: '[class*="title"]', priority: 60 },
      { selector: '[id*="title"]', priority: 60 },

      // 较低优先级：其他标题
      { selector: 'h2', priority: 40 },
      { selector: '.headline', priority: 35 },
      { selector: '.header h1', priority: 30 },
      { selector: '.header h2', priority: 25 }
    ];

    // 收集所有候选标题
    const candidates: { text: string; priority: number; element: Element }[] = [];

    for (const { selector, priority } of titleCandidates) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent?.trim();
        if (text && text.length > 5 && text.length < 200) {
          candidates.push({ text, priority, element });
        }
      }
    }

    if (candidates.length === 0) {
      // 后备方案：使用页面标题
      const pageTitle = document.title?.trim();
      if (pageTitle && pageTitle !== 'Untitled') {
        // 清理页面标题（移除网站名称等）
        const cleanTitle = pageTitle
          .split(' - ')[0]
          .split(' | ')[0]
          .split(' :: ')[0]
          .split(' — ')[0]
          .trim();
        return cleanTitle || pageTitle;
      }
      return '未知标题';
    }

    // 按优先级和质量评分排序
    candidates.sort((a, b) => {
      // 首先按优先级排序
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }

      // 相同优先级时，考虑文本质量
      const aScore = this.scoreTitleCandidate(a.text, a.element);
      const bScore = this.scoreTitleCandidate(b.text, b.element);
      return bScore - aScore;
    });

    return candidates[0].text;
  }

  /**
   * 为标题候选项评分
   */
  private scoreTitleCandidate(text: string, element: Element): number {
    let score = 0;

    // 长度评分（适中长度最佳）
    const length = text.length;
    if (length >= 10 && length <= 100) {
      score += 10;
    } else if (length >= 5 && length <= 150) {
      score += 5;
    } else {
      score -= 5;
    }

    // 位置评分（页面上部优先）
    const rect = element.getBoundingClientRect();
    if (rect.top < window.innerHeight * 0.5) {
      score += 5;
    }

    // 避免常见的非标题文本
    const badPatterns = [
      /^(menu|nav|search|login|register|subscribe)/i,
      /^(home|back|next|previous|more)/i,
      /^(advertisement|sponsored|promoted)/i
    ];

    for (const pattern of badPatterns) {
      if (pattern.test(text)) {
        score -= 20;
      }
    }

    return score;
  }

  /**
   * 从容器中提取内容
   */
  private extractContentFromContainer(container: HTMLElement): string {
    // 克隆容器以避免修改原始DOM
    const cloned = container.cloneNode(true) as HTMLElement;
    
    // 移除不需要的元素
    REMOVE_SELECTORS.forEach(selector => {
      const elements = cloned.querySelectorAll(selector);
      elements.forEach(element => element.remove());
    });

    // 提取文本内容，保持基本格式
    return this.extractFormattedText(cloned);
  }

  /**
   * 提取格式化文本
   */
  private extractFormattedText(element: HTMLElement): string {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT | NodeFilter.SHOW_ELEMENT,
      null
    );

    let result = '';
    let node: Node | null;

    while ((node = walker.nextNode())) {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          result += text + ' ';
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;
        const tagName = element.tagName.toLowerCase();
        
        // 在块级元素后添加换行
        if (['p', 'div', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
          result += '\n';
        }
      }
    }

    return result.replace(/\s+/g, ' ').trim();
  }

  /**
   * 提取元数据
   */
  private extractMetadata(document: Document, content: string): ContentMetadata {
    const wordCount = content.split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / 200); // 假设每分钟200字

    return {
      wordCount,
      readingTime,
      language: this.detectLanguage(content),
      contentType: 'article',
      tags: [],
      images: this.extractImages(document)
    };
  }

  /**
   * 检测语言
   */
  private detectLanguage(content: string): string {
    // 简单的语言检测
    const chineseChars = content.match(/[\u4e00-\u9fff]/g);
    const totalChars = content.length;
    
    if (chineseChars && chineseChars.length / totalChars > 0.3) {
      return 'zh-CN';
    }
    
    return 'en';
  }

  /**
   * 提取图片信息 - 增强的多媒体处理
   */
  private extractImages(document: Document): any[] {
    const images = document.querySelectorAll('img');
    const processedImages: any[] = [];

    for (const img of Array.from(images)) {
      const imageInfo = this.processImage(img);
      if (imageInfo && this.isValidContentImage(imageInfo)) {
        processedImages.push(imageInfo);
      }
    }

    // 按重要性排序，取前15张
    return processedImages
      .sort((a, b) => b.importance - a.importance)
      .slice(0, 15);
  }

  /**
   * 处理单个图片
   */
  private processImage(img: HTMLImageElement): any | null {
    try {
      // 获取图片源（支持懒加载）
      const src = img.src ||
                  img.getAttribute('data-src') ||
                  img.getAttribute('data-lazy-src') ||
                  img.getAttribute('data-original') ||
                  img.getAttribute('srcset')?.split(' ')[0] || '';

      if (!src) {
        return null;
      }

      // 获取图片信息
      const alt = img.alt || img.getAttribute('title') || '';
      const width = img.naturalWidth || parseInt(img.getAttribute('width') || '0') || 0;
      const height = img.naturalHeight || parseInt(img.getAttribute('height') || '0') || 0;

      // 查找图片说明
      const caption = this.findImageCaption(img);

      // 计算重要性分数
      const importance = this.calculateImageImportance(img, src, alt, width, height);

      return {
        src: this.normalizeImageUrl(src),
        alt,
        caption,
        width,
        height,
        importance,
        position: this.getImagePosition(img),
        isInContent: this.isImageInMainContent(img)
      };
    } catch (error) {
      console.warn('[内容提取器] 处理图片失败:', error);
      return null;
    }
  }

  /**
   * 查找图片说明
   */
  private findImageCaption(img: HTMLImageElement): string {
    // 查找各种可能的图片说明
    const parent = img.parentElement;
    if (!parent) {return '';}

    // 检查figure/figcaption结构
    const figure = img.closest('figure');
    if (figure) {
      const figcaption = figure.querySelector('figcaption');
      if (figcaption?.textContent) {
        return figcaption.textContent.trim();
      }
    }

    // 检查常见的说明类名
    const captionSelectors = [
      '.caption',
      '.image-caption',
      '.photo-caption',
      '.img-caption',
      '.description',
      '.subtitle'
    ];

    for (const selector of captionSelectors) {
      const caption = parent.querySelector(selector) ||
                     parent.parentElement?.querySelector(selector);
      if (caption?.textContent) {
        return caption.textContent.trim();
      }
    }

    // 检查紧邻的文本节点
    const nextSibling = img.nextElementSibling;
    if (nextSibling && nextSibling.tagName.toLowerCase() === 'p') {
      const text = nextSibling.textContent?.trim() || '';
      if (text.length > 0 && text.length < 200) {
        return text;
      }
    }

    return '';
  }

  /**
   * 计算图片重要性
   */
  private calculateImageImportance(
    img: HTMLImageElement,
    src: string,
    alt: string,
    width: number,
    height: number
  ): number {
    let score = 0;

    // 尺寸评分
    const area = width * height;
    if (area > 100000) { // 大图片
      score += 10;
    } else if (area > 50000) { // 中等图片
      score += 7;
    } else if (area > 10000) { // 小图片
      score += 4;
    } else if (area < 2500) { // 可能是图标
      score -= 5;
    }

    // Alt文本质量评分
    if (alt.length > 10) {
      score += 5;
    } else if (alt.length > 0) {
      score += 2;
    }

    // 文件名评分
    const filename = src.split('/').pop()?.toLowerCase() || '';
    if (filename.includes('hero') || filename.includes('main') || filename.includes('featured')) {
      score += 8;
    }
    if (filename.includes('thumb') || filename.includes('icon') || filename.includes('logo')) {
      score -= 3;
    }

    // 位置评分
    if (this.isImageInMainContent(img)) {
      score += 10;
    }

    // 结构评分
    if (img.closest('figure')) {
      score += 5;
    }
    if (img.closest('article, main, .content')) {
      score += 7;
    }

    return Math.max(0, score);
  }

  /**
   * 检查图片是否在主要内容区域
   */
  private isImageInMainContent(img: HTMLImageElement): boolean {
    const contentSelectors = [
      'article', 'main', '.content', '.post-content',
      '.entry-content', '.article-content', '.story-body'
    ];

    for (const selector of contentSelectors) {
      if (img.closest(selector)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取图片位置信息
   */
  private getImagePosition(img: HTMLImageElement): any {
    try {
      const rect = img.getBoundingClientRect();
      return {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      };
    } catch {
      return { top: 0, left: 0, width: 0, height: 0 };
    }
  }

  /**
   * 标准化图片URL
   */
  private normalizeImageUrl(src: string): string {
    try {
      // 处理相对URL
      if (src.startsWith('//')) {
        return `https:${src}`;
      }
      if (src.startsWith('/')) {
        return `${window.location.origin}${src}`;
      }
      if (!src.startsWith('http')) {
        return `${window.location.origin}/${src}`;
      }
      return src;
    } catch {
      return src;
    }
  }

  /**
   * 验证是否为有效的内容图片
   */
  private isValidContentImage(imageInfo: any): boolean {
    const { src, width, height, importance } = imageInfo;

    // 基本验证
    if (!src || importance < 0) {
      return false;
    }

    // 尺寸验证
    if (width > 0 && height > 0 && Math.max(width, height) < 50) {
      return false; // 太小，可能是图标
    }

    // URL验证
    const url = src.toLowerCase();
    const badPatterns = [
      'ad', 'banner', 'sponsor', 'promo',
      'tracking', 'pixel', 'beacon',
      'logo', 'icon', 'avatar'
    ];

    for (const pattern of badPatterns) {
      if (url.includes(pattern)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 计算提取置信度
   */
  private calculateConfidence(container: HTMLElement, content: string): number {
    let confidence = 0.5; // 基础置信度

    // 根据内容长度调整
    if (content.length > 1000) {confidence += 0.2;}
    if (content.length > 2000) {confidence += 0.1;}

    // 根据段落数量调整
    const paragraphs = container.querySelectorAll('p');
    if (paragraphs.length > 3) {confidence += 0.1;}
    if (paragraphs.length > 5) {confidence += 0.1;}

    return Math.min(1.0, confidence);
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 生成URL哈希
   */
  private hashUrl(url: string): string {
    let hash = 0;
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }
}
