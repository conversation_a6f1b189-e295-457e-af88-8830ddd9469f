// 消息处理器 - 基于架构文档设计
import type { BrowserMessage } from '@/types';

/**
 * 消息处理器类
 * 负责处理来自popup和background的消息
 */
export class MessageHandler {
  private handlers: Map<string, (payload?: any) => any> = new Map();

  constructor() {
    this.bindMessageEvents();
  }

  /**
   * 注册消息处理器
   */
  onMessage(type: string, handler: (_payload?: any) => any): void {
    this.handlers.set(type, handler);
  }

  /**
   * 绑定消息事件
   */
  private bindMessageEvents(): void {
    chrome.runtime.onMessage.addListener((message: BrowserMessage, _sender, sendResponse) => {
      console.log('[消息处理器] 收到消息:', message);

      const handler = this.handlers.get(message.type);
      
      if (handler) {
        try {
          const result = handler(message.payload);
          
          // 如果返回Promise，等待结果
          if (result instanceof Promise) {
            result
              .then(data => sendResponse({ success: true, data }))
              .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // 异步响应
          } else {
            sendResponse({ success: true, data: result });
          }
        } catch (error) {
          console.error('[消息处理器] 处理消息失败:', error);
          sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
        }
      } else {
        console.warn('[消息处理器] 未知消息类型:', message.type);
        sendResponse({ success: false, error: 'Unknown message type' });
      }
    });
  }
}
