// 键盘处理器 - 基于架构文档设计

/**
 * 键盘处理器类
 * 负责处理键盘快捷键
 */
export class KeyboardHandler {
  private handlers: Map<string, () => void> = new Map();
  private isEnabled = false;

  constructor() {
    this.bindGlobalEvents();
  }

  /**
   * 注册按键处理器
   */
  onKeyPress(key: string, handler: () => void): void {
    this.handlers.set(key, handler);
  }

  /**
   * 启用键盘处理
   */
  enable(): void {
    this.isEnabled = true;
    console.log('[键盘处理器] 已启用');
  }

  /**
   * 禁用键盘处理
   */
  disable(): void {
    this.isEnabled = false;
    console.log('[键盘处理器] 已禁用');
  }

  /**
   * 绑定全局键盘事件
   */
  private bindGlobalEvents(): void {
    document.addEventListener('keydown', (event) => {
      if (!this.isEnabled) {return;}

      const key = this.getKeyFromEvent(event);
      const handler = this.handlers.get(key);

      if (handler) {
        event.preventDefault();
        event.stopPropagation();
        handler();
      }
    });
  }

  /**
   * 从事件中获取按键标识
   */
  private getKeyFromEvent(event: KeyboardEvent): string {
    // 处理特殊键
    if (event.key === 'Escape') {return 'Escape';}
    if (event.key === '=' && (event.ctrlKey || event.metaKey)) {return 'Equal';}
    if (event.key === '-' && (event.ctrlKey || event.metaKey)) {return 'Minus';}
    
    // 处理字母键
    if (event.code.startsWith('Key')) {
      return event.code;
    }

    return event.key;
  }
}
