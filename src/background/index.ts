// background.ts - Service Worker for browser extension
// 基于架构文档的后台脚本设计

import { BrowserAPI } from '@/utils/browserApi';
import type { BrowserMessage } from '@/types';

// 初始化后台脚本
chrome.runtime.onInstalled.addListener(() => {
  console.log('[阅读助手] 扩展已安装 - React+TypeScript版本');
});

// 处理扩展图标点击（后备方案）
chrome.action.onClicked?.addListener(async (tab) => {
  if (!tab.id) {return;}
  
  try {
    // 向content script发送切换消息
    await chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_READER' });
  } catch (error) {
    console.warn('[阅读助手] 无法发送消息到标签页:', error);
  }
});

// 处理来自popup和content script的消息
chrome.runtime.onMessage.addListener((message: BrowserMessage, _sender, sendResponse) => {
  console.log('[阅读助手] 收到消息:', message);
  
  switch (message.type) {
    case 'GET_ACTIVE_TAB':
      // 获取当前活动标签页
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        sendResponse(tabs?.[0]);
      });
      return true; // 异步响应
      
    case 'UPDATE_SETTINGS':
      // 更新用户设置
      handleUpdateSettings(message.payload)
        .then(() => sendResponse({ success: true }))
        .catch((error) => sendResponse({ success: false, error: error.message }));
      return true;
      
    case 'GET_SETTINGS':
      // 获取用户设置
      handleGetSettings()
        .then((settings) => sendResponse({ success: true, settings }))
        .catch((error) => sendResponse({ success: false, error: error.message }));
      return true;
      
    default:
      console.warn('[阅读助手] 未知消息类型:', message.type);
      sendResponse({ success: false, error: 'Unknown message type' });
  }
});

// 处理设置更新
async function handleUpdateSettings(settings: any): Promise<void> {
  try {
    const browserAPI = BrowserAPI.getInstance();
    await browserAPI.setStorage('userSettings', settings);
    console.log('[阅读助手] 设置已更新:', settings);
  } catch (error) {
    console.error('[阅读助手] 设置更新失败:', error);
    throw error;
  }
}

// 处理获取设置
async function handleGetSettings(): Promise<any> {
  try {
    const browserAPI = BrowserAPI.getInstance();
    const settings = await browserAPI.getStorage('userSettings');
    console.log('[阅读助手] 获取设置:', settings);
    return settings;
  } catch (error) {
    console.error('[阅读助手] 获取设置失败:', error);
    throw error;
  }
}

// 处理标签页更新事件
chrome.tabs.onUpdated.addListener((_tabId, changeInfo, tab) => {
  // 当页面加载完成时，可以进行一些初始化操作
  if (changeInfo.status === 'complete' && tab.url) {
    const browserAPI = BrowserAPI.getInstance();
    
    // 检查是否是特殊页面
    if (browserAPI.isSpecialPage(tab.url)) {
      console.log('[阅读助手] 跳过特殊页面:', tab.url);
      return;
    }
    
    // 这里可以添加自动提取逻辑（如果用户开启了自动提取）
    console.log('[阅读助手] 页面加载完成:', tab.url);
  }
});

// 错误处理
self.addEventListener('error', (event) => {
  console.error('[阅读助手] 后台脚本错误:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[阅读助手] 未处理的Promise拒绝:', event.reason);
});

console.log('[阅读助手] 后台脚本已加载');
