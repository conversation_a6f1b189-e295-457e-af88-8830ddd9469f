<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读助手测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #555;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        p {
            margin-bottom: 15px;
            color: #666;
        }
        
        .test-instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status" id="status">页面已加载</div>
    
    <div class="header">
        <h1>阅读助手浏览器扩展测试页面</h1>
        <div class="test-instructions">
            <strong>测试步骤：</strong>
            <ol>
                <li>确保已加载修复后的阅读助手扩展</li>
                <li>打开浏览器开发者工具（F12）查看控制台</li>
                <li>点击扩展图标，然后点击"开启/关闭 阅读模式"</li>
                <li>检查是否出现以下错误：
                    <ul>
                        <li>❌ "process is not defined"</li>
                        <li>❌ "无法启动阅读模式，请刷新页面后重试"</li>
                    </ul>
                </li>
                <li>如果没有错误且阅读模式正常启动，说明修复成功！</li>
            </ol>
        </div>
    </div>

    <!-- 模拟干扰元素 -->
    <nav class="navigation">
        <a href="#">首页</a>
        <a href="#">分类</a>
        <a href="#">关于</a>
    </nav>

    <div class="advertisement">
        <p>这是一个广告内容，应该被过滤掉</p>
    </div>

    <article class="content">
        <h1>阅读助手功能优化测试文章</h1>

        <div class="article-meta">
            <span>发布时间：2024年1月15日</span>
            <span>作者：测试用户</span>
            <span>分类：技术文档</span>
        </div>

        <p>这是一个专门用于测试阅读助手浏览器扩展功能优化的综合测试文章。本文包含了各种HTML元素和内容类型，用于验证内容提取、排版显示和全屏覆盖功能的改进效果。</p>

        <h2>1. 内容提取优化测试</h2>

        <p>本次优化大幅改进了内容提取算法：</p>

        <h3>1.1 扩展的内容选择器</h3>
        <p>新增了30多个内容选择器，包括语义化标签、常见类名和现代网站模式。这确保了扩展能够准确识别各种网站的主要内容区域。</p>

        <h3>1.2 改进的评分算法</h3>
        <p>采用了更复杂的启发式算法，考虑了以下因素：</p>
        <ul>
            <li>文本长度（对数增长，避免过度偏向长文本）</li>
            <li>段落质量（过滤短段落）</li>
            <li>文本结构（句子数量和质量）</li>
            <li>链接密度（智能扣分机制）</li>
            <li>语义化标签权重</li>
            <li>类名和ID语义分析</li>
            <li>内容质量指标（标题、图片等）</li>
            <li>位置权重（页面上部优先）</li>
        </ul>

        <h3>1.3 全面的干扰元素过滤</h3>
        <p>新增了80多个移除选择器，能够过滤：</p>
        <ol>
            <li>广告和推广内容</li>
            <li>导航和菜单</li>
            <li>社交分享按钮</li>
            <li>评论系统</li>
            <li>相关文章推荐</li>
            <li>表单和输入元素</li>
            <li>弹窗和通知</li>
        </ol>

        <h2>2. 排版显示优化测试</h2>

        <p>阅读器现在提供了完全重新设计的排版系统：</p>

        <h3>2.1 全屏沉浸式体验</h3>
        <p>阅读模式现在占据整个浏览器窗口，提供真正的沉浸式阅读体验。背景完全覆盖，消除所有干扰。</p>

        <h3>2.2 优化的内容布局</h3>
        <p>内容区域采用居中布局，最大宽度限制确保最佳的阅读体验。支持响应式设计，适应不同屏幕尺寸。</p>

        <blockquote>
            <p>这是一个引用示例。新的样式系统为引用内容提供了特殊的视觉效果，包括左边框、背景色和斜体字。</p>
        </blockquote>

        <h3>2.3 丰富的元素样式</h3>
        <p>所有HTML元素都得到了精心设计的样式：</p>

        <h4>代码示例</h4>
        <pre><code>// 这是一个代码块示例
function optimizeReading() {
    const content = extractContent();
    const renderer = new ReaderRenderer();
    renderer.render(content);
}</code></pre>

        <p>行内代码也有特殊样式：<code>const isOptimized = true;</code></p>

        <h4>表格示例</h4>
        <table>
            <thead>
                <tr>
                    <th>功能</th>
                    <th>优化前</th>
                    <th>优化后</th>
                    <th>改进程度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>内容提取准确性</td>
                    <td>70%</td>
                    <td>95%</td>
                    <td>+25%</td>
                </tr>
                <tr>
                    <td>排版质量</td>
                    <td>基础</td>
                    <td>专业</td>
                    <td>+200%</td>
                </tr>
                <tr>
                    <td>全屏覆盖</td>
                    <td>部分</td>
                    <td>完整</td>
                    <td>+100%</td>
                </tr>
            </tbody>
        </table>

        <h2>3. 用户体验改进</h2>

        <p>新版本在用户体验方面有显著提升：</p>

        <h3>3.1 智能标题识别</h3>
        <p>改进的标题提取算法能够准确识别文章标题，支持多种网站结构和命名模式。</p>

        <h3>3.2 详细的元数据显示</h3>
        <p>阅读器头部显示丰富的文章信息，包括字数统计、预估阅读时间、内容置信度等。</p>

        <h3>3.3 响应式控制界面</h3>
        <p>新的控制按钮布局更加直观，支持键盘快捷键操作。</p>

        <h2>4. 技术架构优化</h2>

        <p>底层技术架构也得到了全面优化：</p>

        <h3>4.1 模块化设计</h3>
        <p>内容提取器、渲染器和样式系统完全分离，便于维护和扩展。</p>

        <h3>4.2 性能优化</h3>
        <p>优化了DOM操作和样式应用，减少了重排和重绘，提升了渲染性能。</p>

        <h3>4.3 错误处理</h3>
        <p>增强了错误处理机制，提供详细的调试信息和用户反馈。</p>

        <h2>测试验证</h2>

        <p>要验证这些优化是否生效，请：</p>

        <ol>
            <li><strong>启动阅读模式</strong>：点击扩展图标，然后点击"开启/关闭 阅读模式"</li>
            <li><strong>检查全屏覆盖</strong>：确认阅读界面完全覆盖浏览器窗口</li>
            <li><strong>验证内容提取</strong>：确认只显示正文内容，没有广告、导航等干扰元素</li>
            <li><strong>测试排版效果</strong>：检查标题、段落、列表、表格等元素的显示效果</li>
            <li><strong>查看控制台</strong>：确认没有JavaScript错误，有清晰的调试日志</li>
        </ol>

        <p>如果所有功能都正常工作，说明优化已经成功！您现在可以享受更好的阅读体验了。</p>
    </article>

    <!-- 更多干扰元素 -->
    <aside class="sidebar">
        <h3>相关文章</h3>
        <ul>
            <li><a href="#">这些内容应该被过滤</a></li>
            <li><a href="#">不会出现在阅读模式中</a></li>
        </ul>
    </aside>

    <div class="comments">
        <h3>评论区</h3>
        <p>这个评论区也应该被过滤掉</p>
    </div>

    <script>
        // 添加一些测试脚本
        console.log('[测试页面] 页面已加载');
        
        // 监听扩展消息（如果有的话）
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                console.log('[测试页面] 收到扩展消息:', message);
                return true;
            });
        }
        
        // 更新状态显示
        setTimeout(() => {
            const status = document.getElementById('status');
            status.textContent = '准备测试扩展';
            status.style.background = '#ff9800';
        }, 2000);
        
        // 检测是否有阅读助手扩展
        setTimeout(() => {
            if (window.__ReadingAssistant__) {
                const status = document.getElementById('status');
                status.textContent = '检测到阅读助手扩展';
                status.style.background = '#4caf50';
            }
        }, 3000);
    </script>
</body>
</html>
