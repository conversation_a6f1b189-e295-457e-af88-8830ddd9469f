#!/usr/bin/env node

/**
 * 构建脚本 - 专门为浏览器扩展优化
 */

import { resolve } from 'path';
import { copyFileSync, existsSync, renameSync, rmSync } from 'fs';

const __dirname = new URL('.', import.meta.url).pathname;
const rootDir = resolve(__dirname, '..');

async function buildExtension() {
  console.log('🚀 开始后处理构建文件...');

  try {

    // 复制manifest.json到dist目录
    const manifestSrc = resolve(rootDir, 'manifest.json');
    const manifestDest = resolve(rootDir, 'dist/manifest.json');

    if (existsSync(manifestSrc)) {
      copyFileSync(manifestSrc, manifestDest);
      console.log('✅ manifest.json 已复制到 dist 目录');
    }

    // 复制content.css到dist目录
    const cssSrc = resolve(rootDir, 'content.css');
    const cssDest = resolve(rootDir, 'dist/content.css');

    if (existsSync(cssSrc)) {
      copyFileSync(cssSrc, cssDest);
      console.log('✅ content.css 已复制到 dist 目录');
    }

    // 移动HTML文件到正确位置
    const popupSrc = resolve(rootDir, 'dist/src/popup/index.html');
    const popupDest = resolve(rootDir, 'dist/popup.html');
    const optionsSrc = resolve(rootDir, 'dist/src/options/index.html');
    const optionsDest = resolve(rootDir, 'dist/options.html');

    if (existsSync(popupSrc)) {
      renameSync(popupSrc, popupDest);
      console.log('✅ popup.html 已移动到正确位置');
    }

    if (existsSync(optionsSrc)) {
      renameSync(optionsSrc, optionsDest);
      console.log('✅ options.html 已移动到正确位置');
    }

    // 清理临时目录
    const srcDir = resolve(rootDir, 'dist/src');
    if (existsSync(srcDir)) {
      rmSync(srcDir, { recursive: true, force: true });
      console.log('✅ 临时目录已清理');
    }

    console.log('🎉 构建完成！扩展文件已生成到 dist 目录');
    console.log('📦 可以将 dist 目录加载到浏览器中进行测试');

  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

buildExtension();
