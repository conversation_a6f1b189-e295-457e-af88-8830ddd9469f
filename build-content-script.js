#!/usr/bin/env node

/**
 * 专门构建content script的脚本
 * 确保生成的文件不包含ES模块导入
 */

import { build } from 'vite';
import { resolve } from 'path';

const __dirname = new URL('.', import.meta.url).pathname;

async function buildContentScript() {
  console.log('🔧 开始构建Content Script...');
  
  try {
    // 单独构建content script
    await build({
      configFile: false,
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src'),
          '@/components': resolve(__dirname, 'src/components'),
          '@/hooks': resolve(__dirname, 'src/hooks'),
          '@/types': resolve(__dirname, 'src/types'),
          '@/utils': resolve(__dirname, 'src/utils'),
          '@/stores': resolve(__dirname, 'src/stores')
        }
      },
      build: {
        outDir: 'dist',
        emptyOutDir: false,
        minify: true, // 启用压缩
        lib: {
          entry: resolve(__dirname, 'src/content/index.ts'),
          name: 'ContentScript',
          fileName: 'content',
          formats: ['iife']
        },
        rollupOptions: {
          output: {
            entryFileNames: 'content.js',
            // 确保所有依赖都内联
            inlineDynamicImports: true
          }
        }
      },
      define: {
        __DEV__: false,
        __VERSION__: '"0.2.0"',
        'process.env.NODE_ENV': '"production"',
        'process.env': '{"NODE_ENV":"production"}',
        'global': 'globalThis'
      }
    });
    
    console.log('✅ Content Script构建完成');
    
    // 单独构建background script
    await build({
      configFile: false,
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src'),
          '@/components': resolve(__dirname, 'src/components'),
          '@/hooks': resolve(__dirname, 'src/hooks'),
          '@/types': resolve(__dirname, 'src/types'),
          '@/utils': resolve(__dirname, 'src/utils'),
          '@/stores': resolve(__dirname, 'src/stores')
        }
      },
      build: {
        outDir: 'dist',
        emptyOutDir: false,
        minify: true, // 启用压缩
        lib: {
          entry: resolve(__dirname, 'src/background/index.ts'),
          name: 'BackgroundScript',
          fileName: 'background',
          formats: ['iife']
        },
        rollupOptions: {
          output: {
            entryFileNames: 'background.js',
            inlineDynamicImports: true
          }
        }
      },
      define: {
        __DEV__: false,
        __VERSION__: '"0.2.0"',
        'process.env.NODE_ENV': '"production"',
        'process.env': '{"NODE_ENV":"production"}',
        'global': 'globalThis'
      }
    });
    
    console.log('✅ Background Script构建完成');
    
  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

buildContentScript();
