{"name": "reading-assistant-extension", "version": "0.2.0", "description": "跨浏览器网页内容提取与重排版插件 - React+TypeScript版本", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && node build-content-script.js && node scripts/build.js", "build:clean": "rm -rf dist && npm run build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,css,json}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,json}\""}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.13", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.0.254", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/webextension-polyfill": "^0.10.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.1.1", "tailwindcss": "^4.1.13", "typescript": "^5.2.2", "vite": "^5.0.8", "webextension-polyfill": "^0.10.0"}, "engines": {"node": ">=18.0.0"}}